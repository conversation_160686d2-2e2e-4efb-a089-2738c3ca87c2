"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/dashboard/header"
import { Sidebar } from "@/components/dashboard/sidebar"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, Info, ChevronDown } from "lucide-react"

const quickAmounts = [50, 100, 200, 500]

export default function LoansPage() {
  const [activeMenuItem, setActiveMenuItem] = useState("loans")
  const [amount, setAmount] = useState("3000")
  const [selectedQuickAmount, setSelectedQuickAmount] = useState<number | null>(null)
  const [loanPurpose, setLoanPurpose] = useState("")
  const [repaymentPeriod, setRepaymentPeriod] = useState("")

  const handleQuickAmountClick = (value: number) => {
    setAmount(value.toString())
    setSelectedQuickAmount(value)
  }

  const handleAmountChange = (value: string) => {
    setAmount(value)
    setSelectedQuickAmount(null)
  }

  return (
    <div className="flex h-screen bg-[#F5F7FA]">
      <Sidebar activeItem={activeMenuItem} onItemClick={setActiveMenuItem} />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header 
          title="Borrow & Repay" 
          userName="James Bond"
        />
        
        <main className="flex-1 overflow-auto p-4 md:p-8">
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column - Amount Entry */}
              <div>
                <Card className="bg-[#1B263B] text-white">
                  <CardContent className="p-8">
                    <div className="text-center space-y-6">
                      <div>
                        <p className="text-[#AFABAB] text-lg mb-2">Enter Amount</p>
                        <div className="relative">
                          <span className="text-6xl font-semibold">${amount}</span>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <p className="text-[#AFABAB] text-left">Quick Amounts</p>
                        <div className="grid grid-cols-4 gap-3">
                          {quickAmounts.map((value) => (
                            <Button
                              key={value}
                              variant="outline"
                              className={`border-[#059AD1] text-white hover:bg-[#059AD1]/20 ${
                                selectedQuickAmount === value ? 'bg-[#059AD1]/20' : 'bg-black/30'
                              }`}
                              onClick={() => handleQuickAmountClick(value)}
                            >
                              ${value}
                            </Button>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Right Column - Loan Details */}
              <div className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="loanPurpose" className="text-[#585858] font-semibold">
                      Loan purpose
                    </Label>
                    <Input
                      id="loanPurpose"
                      placeholder="Input loan purpose"
                      value={loanPurpose}
                      onChange={(e) => setLoanPurpose(e.target.value)}
                      className="mt-2 h-16 border-[#CBCBCB] rounded-3xl px-6"
                    />
                  </div>

                  <div>
                    <Label htmlFor="repaymentPeriod" className="text-[#585858] font-semibold">
                      Repayment Period
                    </Label>
                    <div className="relative mt-2">
                      <Input
                        id="repaymentPeriod"
                        placeholder="Select repayment period"
                        value={repaymentPeriod}
                        onChange={(e) => setRepaymentPeriod(e.target.value)}
                        className="h-16 border-[#CBCBCB] rounded-3xl px-6 pr-12"
                      />
                      <ChevronDown className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    </div>
                  </div>
                </div>

                {/* Info Alert */}
                <div className="bg-[#F1F6FD] border border-[#A7C5FD] rounded-lg p-4 opacity-80">
                  <div className="flex gap-3">
                    <Info className="h-5 w-5 text-[#0052EA] mt-0.5 flex-shrink-0" />
                    <div className="space-y-2">
                      <p className="text-[#181818] font-medium text-sm">
                        Tier 1 Limit: You can borrow up to ₦50,000.
                      </p>
                      <p className="text-[#181818] text-sm">
                        Upgrade your tier to unlock higher limits and better rates.
                      </p>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="border-[#181818] text-[#181818] hover:bg-[#181818] hover:text-white"
                      >
                        Upgrade
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Loan Terms Preview */}
                <Card className="bg-white border border-gray-200">
                  <CardContent className="p-6">
                    <h3 className="font-semibold text-[#333B69] mb-4">Loan Summary</h3>
                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-[#6E6E6E]">Loan Amount:</span>
                        <span className="font-medium">${amount}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-[#6E6E6E]">Interest Rate:</span>
                        <span className="font-medium">5.5% per month</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-[#6E6E6E]">Processing Fee:</span>
                        <span className="font-medium">$15.00</span>
                      </div>
                      <hr className="my-3" />
                      <div className="flex justify-between font-semibold">
                        <span>Total to Repay:</span>
                        <span>${(parseFloat(amount) * 1.055 + 15).toFixed(2)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Continue Button */}
            <div className="mt-8 flex justify-center">
              <Button 
                className="bg-[#059AD1] hover:bg-[#059AD1]/90 text-white px-16 py-6 text-lg rounded-2xl"
                onClick={() => {
                  // Navigate to loan confirmation page
                  window.location.href = '/dashboard/loans/confirm'
                }}
              >
                Continue
              </Button>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
