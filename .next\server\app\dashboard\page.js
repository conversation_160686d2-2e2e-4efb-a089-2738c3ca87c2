(()=>{var a={};a.id=105,a.ids=[105],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7107:()=>{},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26589:(a,b,c)=>{"use strict";c.d(b,{Y:()=>k});var d=c(21124),e=c(93758),f=c(35284),g=c(88285),h=c(47268),i=c(94684),j=c(24515);function k({title:a,userAvatar:b,userName:c}){return(0,d.jsx)("header",{className:"bg-white border-b border-gray-200 px-4 md:px-8 py-4 md:py-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("h1",{className:"text-xl md:text-2xl font-semibold text-[#343C6A]",children:a}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-6",children:[(0,d.jsxs)("div",{className:"relative hidden md:block",children:[(0,d.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,d.jsx)(e.p,{placeholder:"Search for something",className:"pl-10 w-60 lg:w-80 bg-[#F5F7FA] border-none"})]}),(0,d.jsxs)(f.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,d.jsx)(h.A,{className:"h-5 w-5 text-[#718EBF]"}),(0,d.jsx)("span",{className:"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"})]}),(0,d.jsx)(f.$,{variant:"ghost",size:"icon",children:(0,d.jsx)(i.A,{className:"h-5 w-5 text-[#718EBF]"})}),(0,d.jsx)("div",{className:"flex items-center space-x-3",children:(0,d.jsx)("div",{className:"w-10 h-10 rounded-full overflow-hidden bg-gray-200",children:b?(0,d.jsx)(j.default,{src:b,alt:c||"User",width:40,height:40,className:"w-full h-full object-cover"}):(0,d.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center text-white font-semibold",children:c?.charAt(0)||"U"})})})]})]})})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29643:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,54160,23)),Promise.resolve().then(c.t.bind(c,31603,23)),Promise.resolve().then(c.t.bind(c,68495,23)),Promise.resolve().then(c.t.bind(c,75170,23)),Promise.resolve().then(c.t.bind(c,77526,23)),Promise.resolve().then(c.t.bind(c,78922,23)),Promise.resolve().then(c.t.bind(c,29234,23)),Promise.resolve().then(c.t.bind(c,12263,23)),Promise.resolve().then(c.bind(c,82146))},33873:a=>{"use strict";a.exports=require("path")},35284:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(21124),e=c(38301),f=c(26691),g=c(44943);let h=(0,f.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-[#1814F3] text-white shadow hover:bg-[#1814F3]/90",destructive:"bg-red-500 text-white shadow-sm hover:bg-red-500/90",outline:"border border-gray-200 bg-white shadow-sm hover:bg-gray-50 hover:text-gray-900",secondary:"bg-gray-100 text-gray-900 shadow-sm hover:bg-gray-100/80",ghost:"hover:bg-gray-100 hover:text-gray-900",link:"text-[#1814F3] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),i=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...f},i)=>(0,d.jsx)("button",{className:(0,g.cn)(h({variant:b,size:c,className:a})),ref:i,...f}));i.displayName="Button"},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41236:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\workspace\\.typescript\\aetrustfrontend\\src\\app\\dashboard\\page.tsx","default")},44943:(a,b,c)=>{"use strict";c.d(b,{Yq:()=>h,cn:()=>f,sL:()=>i,vv:()=>g});var d=c(43249),e=c(58829);function f(...a){return(0,e.QP)((0,d.$)(a))}function g(a){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a)}function h(a){return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric"}).format(new Date(a))}function i(a){return new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric"}).format(new Date(a))}},45192:(a,b,c)=>{Promise.resolve().then(c.bind(c,57053))},51472:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(75338),e=c(94294),f=c.n(e),g=c(81464),h=c.n(g);c(61135);let i={title:"Create Next App",description:"Generated by create next app"};function j({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:a})})}},57053:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>J});var d=c(21124),e=c(38301),f=c(26589),g=c(44943),h=c(35284),i=c(23339);let j=(0,i.A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);var k=c(86773),l=c(14343);let m=(0,i.A)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]);var n=c(65893),o=c(94684);let p=(0,i.A)("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),q=(0,i.A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]),r=[{id:"dashboard",label:"Dashboard",icon:j},{id:"transactions",label:"Transactions",icon:k.A},{id:"accounts",label:"Accounts",icon:l.A},{id:"investments",label:"Investments",icon:m},{id:"credit-cards",label:"Credit Cards",icon:l.A},{id:"loans",label:"Loans",icon:n.A},{id:"services",label:"Services",icon:o.A},{id:"privileges",label:"My Privileges",icon:n.A},{id:"settings",label:"Setting",icon:o.A}];function s({activeItem:a="dashboard",onItemClick:b}){return(0,d.jsxs)("aside",{className:"w-64 bg-white border-r border-gray-200 h-screen flex flex-col hidden lg:flex",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,d.jsx)("h2",{className:"text-2xl font-bold text-[#343C6A]",children:"AeTrust"})}),(0,d.jsx)("nav",{className:"flex-1 p-4",children:(0,d.jsx)("ul",{className:"space-y-2",children:r.map(c=>{let e=c.icon,f=a===c.id;return(0,d.jsx)("li",{children:(0,d.jsxs)(h.$,{variant:"ghost",className:(0,g.cn)("w-full justify-start h-12 px-4 text-left font-medium transition-all",f?"bg-[#1814F3] text-white hover:bg-[#1814F3]/90":"text-[#718EBF] hover:bg-gray-50 hover:text-[#343C6A]"),onClick:()=>b?.(c.id),children:[(0,d.jsx)(e,{className:"mr-3 h-5 w-5"}),c.label,f&&(0,d.jsx)("div",{className:"absolute right-0 top-0 bottom-0 w-1 bg-[#1814F3] rounded-l-lg"})]})},c.id)})})}),(0,d.jsxs)("div",{className:"p-4 border-t border-gray-200",children:[(0,d.jsxs)(h.$,{variant:"ghost",className:"w-full justify-start h-12 px-4 text-[#718EBF] hover:bg-gray-50 hover:text-[#343C6A]",children:[(0,d.jsx)(p,{className:"mr-3 h-5 w-5"}),"Help & Support"]}),(0,d.jsxs)(h.$,{variant:"ghost",className:"w-full justify-start h-12 px-4 text-[#718EBF] hover:bg-gray-50 hover:text-red-600",children:[(0,d.jsx)(q,{className:"mr-3 h-5 w-5"}),"Logout"]})]})]})}var t=c(62186);let u=(0,i.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);var v=c(3368);let w=(0,i.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),x=(0,i.A)("badge",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}]]);function y({user:a}){return(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 rounded-[25px]"}),(0,d.jsx)("div",{className:"absolute top-0 right-0 w-32 h-32 bg-blue-500/20 rounded-full -translate-y-8 translate-x-8"}),(0,d.jsx)("div",{className:"absolute bottom-0 left-0 w-24 h-24 bg-blue-400/20 rounded-full translate-y-4 -translate-x-4"}),(0,d.jsx)("div",{className:"absolute top-1/2 right-8 w-16 h-16 bg-yellow-400/30 rounded-full"}),(0,d.jsxs)(t.Zp,{className:"relative bg-transparent border-none shadow-none text-white p-8",children:[(0,d.jsxs)("div",{className:"flex flex-col space-y-6",children:[(0,d.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,d.jsx)("h3",{className:"text-xl font-semibold",children:a.name}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsxs)("span",{className:"text-sm opacity-80",children:["*** **** * ",a.cardNumber.slice(-3)]}),(0,d.jsx)(h.$,{variant:"ghost",size:"icon",className:"h-6 w-6 text-white hover:bg-white/20",children:(0,d.jsx)(u,{className:"h-3 w-3"})})]})]}),(0,d.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,d.jsx)("span",{className:"text-sm opacity-80",children:"Your balance"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-2xl font-bold",children:(0,g.vv)(a.balance)}),(0,d.jsx)(h.$,{variant:"ghost",size:"icon",className:"h-6 w-6 text-white hover:bg-white/20",children:(0,d.jsx)(v.A,{className:"h-4 w-4"})})]})]}),(0,d.jsx)("div",{className:"flex justify-end",children:(0,d.jsx)(h.$,{variant:"ghost",size:"icon",className:"h-10 w-10 bg-white/20 hover:bg-white/30 rounded-full",children:(0,d.jsx)(w,{className:"h-5 w-5"})})})]}),(0,d.jsxs)("div",{className:"absolute bottom-6 right-6 flex items-center space-x-2 bg-white/20 rounded-full px-3 py-1",children:[(0,d.jsx)(x,{className:"h-4 w-4 text-yellow-400"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:a.tier})]})]})]})}var z=c(65346);function A({data:a}){let b=a.map(a=>({name:a.label,value:a.value,color:a.color}));return(0,d.jsxs)(t.Zp,{className:"bg-white rounded-[25px] border-none shadow-lg",children:[(0,d.jsx)(t.aR,{children:(0,d.jsx)(t.ZB,{className:"text-[#343C6A] text-xl font-semibold",children:"Statistics"})}),(0,d.jsx)(t.Wu,{children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("div",{className:"flex-1",children:(0,d.jsx)(z.k,{data:b,height:200,showLegend:!1})}),(0,d.jsx)("div",{className:"flex flex-col space-y-4 ml-8",children:a.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:a.color}}),(0,d.jsxs)("div",{className:"flex flex-col",children:[(0,d.jsxs)("span",{className:"text-sm font-medium text-white",children:[a.percentage,"%"]}),(0,d.jsx)("span",{className:"text-xs text-white/80",children:a.label})]})]},b))})]})})]})}var B=c(45807);let C=(0,i.A)("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]),D=(0,i.A)("arrow-right-left",[["path",{d:"m16 3 4 4-4 4",key:"1x1c3m"}],["path",{d:"M20 7H4",key:"zbl0bi"}],["path",{d:"m8 21-4-4 4-4",key:"h9nckh"}],["path",{d:"M4 17h16",key:"g4d7ey"}]]);function E({transactions:a}){return(0,d.jsxs)(t.Zp,{className:"bg-white rounded-[25px] border-none shadow-lg",children:[(0,d.jsx)(t.aR,{children:(0,d.jsx)(t.ZB,{className:"text-[#343C6A] text-xl font-semibold",children:"Recent Transaction"})}),(0,d.jsx)(t.Wu,{children:(0,d.jsx)("div",{className:"space-y-4",children:a.map(a=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 hover:bg-gray-50 rounded-lg transition-colors",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:`p-3 rounded-full ${function(a){switch(a){case"cash_in":return"bg-green-100";case"cash_out":return"bg-red-100";case"transfer":return"bg-blue-100";case"loan":return"bg-orange-100";default:return"bg-gray-100"}}(a.type)}`,children:function(a){switch(a){case"cash_in":return(0,d.jsx)(B.A,{className:"h-5 w-5 text-green-500"});case"cash_out":return(0,d.jsx)(C,{className:"h-5 w-5 text-red-500"});case"transfer":return(0,d.jsx)(D,{className:"h-5 w-5 text-blue-500"});case"loan":return(0,d.jsx)(B.A,{className:"h-5 w-5 text-orange-500"});default:return(0,d.jsx)(D,{className:"h-5 w-5 text-gray-500"})}}(a.type)}),(0,d.jsxs)("div",{className:"flex flex-col",children:[(0,d.jsx)("span",{className:"font-medium text-[#232323]",children:a.description}),(0,d.jsx)("span",{className:"text-sm text-[#718EBF]",children:(0,g.sL)(a.date)})]})]}),(0,d.jsx)("div",{className:"text-right",children:(0,d.jsxs)("span",{className:`font-semibold ${function(a){switch(a){case"cash_in":case"transfer":return"text-green-600";case"cash_out":case"loan":return"text-red-600";default:return"text-gray-600"}}(a.type)}`,children:["cash_out"===a.type||"loan"===a.type?"-":"+",(0,g.vv)(a.amount)]})})]},a.id))})})]})}function F(){return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)(t.Zp,{className:"bg-[#1814F3] rounded-[25px] border-none shadow-lg text-white",children:(0,d.jsx)(t.Wu,{className:"p-6",children:(0,d.jsx)("div",{className:"flex items-center justify-between",children:(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"p-3 bg-white/20 rounded-full",children:(0,d.jsx)(C,{className:"h-6 w-6"})}),(0,d.jsx)("span",{className:"text-lg font-medium",children:"Transfer"})]})})})}),(0,d.jsx)(t.Zp,{className:"bg-white rounded-[25px] border border-gray-200 shadow-lg",children:(0,d.jsx)(t.Wu,{className:"p-6",children:(0,d.jsx)("div",{className:"flex items-center justify-between",children:(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"p-3 bg-gray-100 rounded-full",children:(0,d.jsx)(B.A,{className:"h-6 w-6 text-[#718EBF]"})}),(0,d.jsx)("span",{className:"text-lg font-medium text-[#718EBF]",children:"Withdraw"})]})})})}),(0,d.jsx)(t.Zp,{className:"bg-white rounded-[25px] border border-gray-200 shadow-lg",children:(0,d.jsx)(t.Wu,{className:"p-6",children:(0,d.jsx)("div",{className:"flex items-center justify-between",children:(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"p-3 bg-gray-100 rounded-full",children:(0,d.jsx)(B.A,{className:"h-6 w-6 text-[#718EBF]"})}),(0,d.jsx)("span",{className:"text-lg font-medium text-[#718EBF]",children:"Deposit"})]})})})})]})}let G={id:"1",name:"James Bond",email:"<EMAIL>",balance:35673,cardNumber:"3778123456781234",cardHolder:"James Bond",validThru:"12/22",tier:"Tier 1"},H=[{id:"1",type:"cash_in",amount:850,description:"Cash in (Agent yusuff)",date:"2021-01-28",status:"completed",agent:"Agent yusuff"},{id:"2",type:"cash_out",amount:2500,description:"Cash out",date:"2021-01-25",status:"completed"},{id:"3",type:"transfer",amount:5400,description:"Transfer",date:"2021-01-21",status:"completed"}],I=[{label:"Cash in",value:30,percentage:30,color:"#1814F3"},{label:"Loan",value:20,percentage:20,color:"#16DBCC"},{label:"Cash out",value:15,percentage:15,color:"#FF82AC"},{label:"Others",value:35,percentage:35,color:"#FFBB38"}];function J(){let[a,b]=(0,e.useState)("dashboard");return(0,d.jsxs)("div",{className:"flex h-screen bg-[#F5F7FA]",children:[(0,d.jsx)(s,{activeItem:a,onItemClick:b}),(0,d.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,d.jsx)(f.Y,{title:"Overview",userName:G.name}),(0,d.jsx)("main",{className:"flex-1 overflow-auto p-4 md:p-8",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-12 gap-6",children:[(0,d.jsxs)("div",{className:"lg:col-span-8 space-y-6",children:[(0,d.jsx)(y,{user:G}),(0,d.jsx)(E,{transactions:H})]}),(0,d.jsxs)("div",{className:"lg:col-span-4 space-y-6",children:[(0,d.jsx)(F,{}),(0,d.jsx)(A,{data:I})]})]})})]})]})}},61135:()=>{},62186:(a,b,c)=>{"use strict";c.d(b,{Wu:()=>j,ZB:()=>i,Zp:()=>g,aR:()=>h});var d=c(21124),e=c(38301),f=c(44943);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-xl border bg-white text-gray-950 shadow",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,f.cn)("font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm text-gray-500",a),...b})).displayName="CardDescription";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));j.displayName="CardContent",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b})).displayName="CardFooter"},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65346:(a,b,c)=>{"use strict";c.d(b,{k:()=>k});var d=c(21124),e=c(6077),f=c(86493),g=c(40853),h=c(83790),i=c(70522);let j=["#1814F3","#16DBCC","#FF82AC","#FFBB38"];function k({data:a,width:b=400,height:c=300,showLegend:k=!0}){return(0,d.jsx)(e.u,{width:"100%",height:c,children:(0,d.jsxs)(f.r,{children:[(0,d.jsx)(g.F,{data:a,cx:"50%",cy:"50%",innerRadius:60,outerRadius:100,paddingAngle:5,dataKey:"value",children:a.map((a,b)=>(0,d.jsx)(h.f,{fill:a.color||j[b%j.length]},`cell-${b}`))}),k&&(0,d.jsx)(i.s,{verticalAlign:"bottom",height:36,formatter:(a,b)=>(0,d.jsx)("span",{style:{color:b.color},children:a})})]})})}},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(97523);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},72763:()=>{},81640:(a,b,c)=>{Promise.resolve().then(c.bind(c,41236))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},92691:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,81170,23)),Promise.resolve().then(c.t.bind(c,23597,23)),Promise.resolve().then(c.t.bind(c,36893,23)),Promise.resolve().then(c.t.bind(c,89748,23)),Promise.resolve().then(c.t.bind(c,6060,23)),Promise.resolve().then(c.t.bind(c,7184,23)),Promise.resolve().then(c.t.bind(c,69576,23)),Promise.resolve().then(c.t.bind(c,73041,23)),Promise.resolve().then(c.t.bind(c,51384,23))},93758:(a,b,c)=>{"use strict";c.d(b,{p:()=>g});var d=c(21124),e=c(38301),f=c(44943);let g=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,f.cn)("flex h-9 w-full rounded-md border border-gray-200 bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-[#1814F3] disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...c}));g.displayName="Input"},95988:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,41236)),"D:\\workspace\\.typescript\\aetrustfrontend\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,51472)),"D:\\workspace\\.typescript\\aetrustfrontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,87028,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,I=["D:\\workspace\\.typescript\\aetrustfrontend\\src\\app\\dashboard\\page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/dashboard/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[586,703,303],()=>b(b.s=95988));module.exports=c})();