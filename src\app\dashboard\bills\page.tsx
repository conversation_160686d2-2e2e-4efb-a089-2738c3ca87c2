"use client"

import { useState } from "react"
import { Header } from "@/components/dashboard/header"
import { Sidebar } from "@/components/dashboard/sidebar"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ArrowLeft, Zap, Wifi, Phone, Tv, Car, Home } from "lucide-react"

const billCategories = [
  {
    id: 'electricity',
    title: 'Electricity',
    description: 'Pay your electricity bills',
    icon: <Zap className="h-6 w-6" />,
    color: '#FFD700',
    providers: ['NEPA', 'EKEDC', 'IKEDC', 'AEDC']
  },
  {
    id: 'internet',
    title: 'Internet & Cable',
    description: 'Internet and cable TV bills',
    icon: <Wifi className="h-6 w-6" />,
    color: '#059AD1',
    providers: ['MTN', 'Airtel', 'Glo', 'DSTV', 'GOtv']
  },
  {
    id: 'mobile',
    title: 'Mobile Recharge',
    description: 'Top up your mobile phone',
    icon: <Phone className="h-6 w-6" />,
    color: '#16DBCC',
    providers: ['MTN', 'Airtel', 'Glo', '9mobile']
  },
  {
    id: 'tv',
    title: 'TV Subscription',
    description: 'Pay for TV subscriptions',
    icon: <Tv className="h-6 w-6" />,
    color: '#FF6B6B',
    providers: ['DSTV', 'GOtv', 'StarTimes', 'Netflix']
  },
  {
    id: 'transport',
    title: 'Transportation',
    description: 'Transport and fuel payments',
    icon: <Car className="h-6 w-6" />,
    color: '#F2B134',
    providers: ['Uber', 'Bolt', 'Lagos BRT', 'Fuel Stations']
  },
  {
    id: 'rent',
    title: 'Rent & Utilities',
    description: 'Pay rent and utility bills',
    icon: <Home className="h-6 w-6" />,
    color: '#9B59B6',
    providers: ['Property Managers', 'Water Board', 'Waste Management']
  }
]

export default function BillsPage() {
  const [activeMenuItem, setActiveMenuItem] = useState("bills")
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [selectedProvider, setSelectedProvider] = useState<string | null>(null)
  const [customerInfo, setCustomerInfo] = useState({
    accountNumber: "",
    phoneNumber: "",
    amount: ""
  })

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId)
    setSelectedProvider(null)
  }

  const handleProviderSelect = (provider: string) => {
    setSelectedProvider(provider)
  }

  const selectedCategoryData = billCategories.find(cat => cat.id === selectedCategory)

  return (
    <div className="flex h-screen bg-[#F5F7FA]">
      <Sidebar activeItem={activeMenuItem} onItemClick={setActiveMenuItem} />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header 
          title="Bills Payment" 
          userName="James Bond"
        />
        
        <main className="flex-1 overflow-auto p-4 md:p-8">
          <div className="max-w-4xl mx-auto">
            {/* Back Button */}
            <Button 
              variant="ghost" 
              className="mb-6 text-[#343C6A] hover:bg-gray-100"
              onClick={() => window.history.back()}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>

            {!selectedCategory ? (
              /* Bill Categories */
              <div>
                <h2 className="text-2xl font-semibold text-[#333B69] mb-6">Select Bill Category</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {billCategories.map((category) => (
                    <Card 
                      key={category.id} 
                      className="cursor-pointer hover:shadow-md transition-shadow border border-gray-200"
                      onClick={() => handleCategorySelect(category.id)}
                    >
                      <CardContent className="p-6 text-center">
                        <div 
                          className="w-16 h-16 rounded-xl flex items-center justify-center mx-auto mb-4"
                          style={{ backgroundColor: `${category.color}20`, color: category.color }}
                        >
                          {category.icon}
                        </div>
                        <h3 className="font-medium text-[#1C1C1E] mb-2">{category.title}</h3>
                        <p className="text-sm text-[#6E6E6E]">{category.description}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ) : !selectedProvider ? (
              /* Provider Selection */
              <div>
                <Button 
                  variant="ghost" 
                  className="mb-4 text-[#343C6A] hover:bg-gray-100"
                  onClick={() => setSelectedCategory(null)}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Categories
                </Button>
                
                <h2 className="text-2xl font-semibold text-[#333B69] mb-6">
                  Select {selectedCategoryData?.title} Provider
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {selectedCategoryData?.providers.map((provider) => (
                    <Card 
                      key={provider} 
                      className="cursor-pointer hover:shadow-md transition-shadow border border-gray-200"
                      onClick={() => handleProviderSelect(provider)}
                    >
                      <CardContent className="p-6">
                        <div className="flex items-center gap-4">
                          <div 
                            className="w-12 h-12 rounded-xl flex items-center justify-center"
                            style={{ backgroundColor: `${selectedCategoryData.color}20`, color: selectedCategoryData.color }}
                          >
                            {selectedCategoryData.icon}
                          </div>
                          <div>
                            <h3 className="font-medium text-[#1C1C1E]">{provider}</h3>
                            <p className="text-sm text-[#6E6E6E]">{selectedCategoryData.title}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ) : (
              /* Payment Form */
              <div>
                <Button 
                  variant="ghost" 
                  className="mb-4 text-[#343C6A] hover:bg-gray-100"
                  onClick={() => setSelectedProvider(null)}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Providers
                </Button>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Payment Form */}
                  <div>
                    <Card>
                      <CardContent className="p-6">
                        <div className="flex items-center gap-3 mb-6">
                          <div 
                            className="w-12 h-12 rounded-xl flex items-center justify-center"
                            style={{ backgroundColor: `${selectedCategoryData?.color}20`, color: selectedCategoryData?.color }}
                          >
                            {selectedCategoryData?.icon}
                          </div>
                          <div>
                            <h3 className="font-semibold text-[#333B69]">{selectedProvider}</h3>
                            <p className="text-sm text-[#6E6E6E]">{selectedCategoryData?.title}</p>
                          </div>
                        </div>

                        <div className="space-y-4">
                          <div>
                            <Label htmlFor="accountNumber">
                              {selectedCategory === 'mobile' ? 'Phone Number' : 'Account/Meter Number'}
                            </Label>
                            <Input
                              id="accountNumber"
                              placeholder={selectedCategory === 'mobile' ? 'Enter phone number' : 'Enter account/meter number'}
                              value={customerInfo.accountNumber}
                              onChange={(e) => setCustomerInfo(prev => ({ ...prev, accountNumber: e.target.value }))}
                              className="mt-1"
                            />
                          </div>

                          {selectedCategory !== 'mobile' && (
                            <div>
                              <Label htmlFor="phoneNumber">Phone Number</Label>
                              <Input
                                id="phoneNumber"
                                placeholder="Enter your phone number"
                                value={customerInfo.phoneNumber}
                                onChange={(e) => setCustomerInfo(prev => ({ ...prev, phoneNumber: e.target.value }))}
                                className="mt-1"
                              />
                            </div>
                          )}

                          <div>
                            <Label htmlFor="amount">Amount</Label>
                            <Input
                              id="amount"
                              type="number"
                              placeholder="Enter amount"
                              value={customerInfo.amount}
                              onChange={(e) => setCustomerInfo(prev => ({ ...prev, amount: e.target.value }))}
                              className="mt-1"
                            />
                          </div>

                          <Button 
                            className="w-full bg-[#059AD1] hover:bg-[#059AD1]/90 text-white h-12"
                            onClick={() => {
                              // Validate customer info
                              window.location.href = '/dashboard/bills/confirm'
                            }}
                          >
                            Validate & Continue
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Recent Bills */}
                  <div>
                    <Card>
                      <CardContent className="p-6">
                        <h3 className="font-semibold text-[#333B69] mb-4">Recent Bills</h3>
                        <div className="space-y-3">
                          {[
                            { provider: 'NEPA', amount: 5000, date: '2024-01-10', account: '****1234' },
                            { provider: 'MTN', amount: 1000, date: '2024-01-08', account: '****5678' },
                            { provider: 'DSTV', amount: 8500, date: '2024-01-05', account: '****9012' }
                          ].map((bill, index) => (
                            <div 
                              key={index}
                              className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg cursor-pointer"
                              onClick={() => {
                                setCustomerInfo(prev => ({ 
                                  ...prev, 
                                  accountNumber: bill.account,
                                  amount: bill.amount.toString()
                                }))
                              }}
                            >
                              <div>
                                <p className="font-medium text-sm">{bill.provider}</p>
                                <p className="text-xs text-[#6E6E6E]">{bill.account} • {bill.date}</p>
                              </div>
                              <span className="font-medium text-sm">${bill.amount}</span>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>

                    {/* Quick Amounts */}
                    <Card className="mt-6">
                      <CardContent className="p-6">
                        <h3 className="font-semibold text-[#333B69] mb-4">Quick Amounts</h3>
                        <div className="grid grid-cols-3 gap-3">
                          {[1000, 2000, 5000, 10000, 15000, 20000].map((amount) => (
                            <Button
                              key={amount}
                              variant="outline"
                              className="h-12"
                              onClick={() => setCustomerInfo(prev => ({ ...prev, amount: amount.toString() }))}
                            >
                              ${amount}
                            </Button>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  )
}
