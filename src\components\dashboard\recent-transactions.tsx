"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { ArrowUpRight, ArrowDownLeft, ArrowRightLeft } from "lucide-react"
import { formatCurrency, formatShortDate } from "@/lib/utils"
import { Transaction } from "@/types"

interface RecentTransactionsProps {
  transactions: Transaction[]
}

function getTransactionIcon(type: Transaction['type']) {
  switch (type) {
    case 'cash_in':
      return <ArrowDownLeft className="h-5 w-5 text-green-500" />
    case 'cash_out':
      return <ArrowUpRight className="h-5 w-5 text-red-500" />
    case 'transfer':
      return <ArrowRightLeft className="h-5 w-5 text-blue-500" />
    case 'loan':
      return <ArrowDownLeft className="h-5 w-5 text-orange-500" />
    default:
      return <ArrowRightLeft className="h-5 w-5 text-gray-500" />
  }
}

function getTransactionColor(type: Transaction['type']) {
  switch (type) {
    case 'cash_in':
      return 'bg-green-100'
    case 'cash_out':
      return 'bg-red-100'
    case 'transfer':
      return 'bg-blue-100'
    case 'loan':
      return 'bg-orange-100'
    default:
      return 'bg-gray-100'
  }
}

function getAmountColor(type: Transaction['type']) {
  switch (type) {
    case 'cash_in':
    case 'transfer':
      return 'text-green-600'
    case 'cash_out':
    case 'loan':
      return 'text-red-600'
    default:
      return 'text-gray-600'
  }
}

export function RecentTransactions({ transactions }: RecentTransactionsProps) {
  return (
    <Card className="bg-white rounded-[25px] border-none shadow-lg">
      <CardHeader>
        <CardTitle className="text-[#343C6A] text-xl font-semibold">Recent Transaction</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {transactions.map((transaction) => (
            <div key={transaction.id} className="flex items-center justify-between p-4 hover:bg-gray-50 rounded-lg transition-colors">
              <div className="flex items-center space-x-4">
                <div className={`p-3 rounded-full ${getTransactionColor(transaction.type)}`}>
                  {getTransactionIcon(transaction.type)}
                </div>
                <div className="flex flex-col">
                  <span className="font-medium text-[#232323]">
                    {transaction.description}
                  </span>
                  <span className="text-sm text-[#718EBF]">
                    {formatShortDate(transaction.date)}
                  </span>
                </div>
              </div>
              <div className="text-right">
                <span className={`font-semibold ${getAmountColor(transaction.type)}`}>
                  {transaction.type === 'cash_out' || transaction.type === 'loan' ? '-' : '+'}
                  {formatCurrency(transaction.amount)}
                </span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
