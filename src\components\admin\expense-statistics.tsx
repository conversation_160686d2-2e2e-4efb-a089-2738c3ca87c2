"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { PieChartComponent } from "@/components/charts/pie-chart"
import { StatisticItem } from "@/types"

interface ExpenseStatisticsProps {
  data: StatisticItem[]
}

export function ExpenseStatistics({ data }: ExpenseStatisticsProps) {
  const chartData = data.map(item => ({
    name: item.label,
    value: item.value,
    color: item.color
  }))

  return (
    <Card className="bg-white rounded-[25px] border-none shadow-lg">
      <CardHeader>
        <CardTitle className="text-[#343C6A] text-xl font-semibold">Expense Statistics</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <PieChartComponent 
              data={chartData} 
              height={200} 
              showLegend={false}
            />
          </div>
          <div className="flex flex-col space-y-4 ml-8">
            {data.map((item, index) => (
              <div key={index} className="flex items-center space-x-3">
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: item.color }}
                />
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-white">
                    {item.percentage}%
                  </span>
                  <span className="text-xs text-white/80">
                    {item.label}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
