import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Users, Shield } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-8">
      <div className="max-w-4xl w-full">
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold text-[#1814F3] mb-4">AeTrust</h1>
          <p className="text-xl text-gray-600 mb-8">
            Modern Financial Dashboard Platform
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {/* User Dashboard Card */}
          <Card className="bg-white shadow-xl border-none hover:shadow-2xl transition-shadow duration-300">
            <CardHeader className="text-center pb-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-[#1814F3]" />
              </div>
              <CardTitle className="text-2xl text-[#343C6A]">User Dashboard</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-gray-600 mb-6">
                Access your personal financial dashboard with account overview,
                transaction history, and transfer capabilities.
              </p>
              <Link href="/dashboard">
                <Button className="w-full bg-[#1814F3] hover:bg-[#1814F3]/90 text-white py-3 text-lg">
                  Enter User Dashboard
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Admin Dashboard Card */}
          <Card className="bg-white shadow-xl border-none hover:shadow-2xl transition-shadow duration-300">
            <CardHeader className="text-center pb-4">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-green-600" />
              </div>
              <CardTitle className="text-2xl text-[#343C6A]">Admin Panel</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-gray-600 mb-6">
                Comprehensive admin dashboard with analytics, user management,
                transaction monitoring, and system controls.
              </p>
              <Link href="/admin">
                <Button className="w-full bg-green-600 hover:bg-green-600/90 text-white py-3 text-lg">
                  Enter Admin Panel
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>

        <div className="text-center mt-12">
          <p className="text-gray-500">
            Built with Next.js, TypeScript, and Tailwind CSS
          </p>
        </div>
      </div>
    </div>
  );
}
