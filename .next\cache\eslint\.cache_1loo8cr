[{"D:\\workspace\\.typescript\\aetrustfrontend\\src\\app\\admin\\page.tsx": "1", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\app\\dashboard\\page.tsx": "2", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\app\\layout.tsx": "3", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\app\\page.tsx": "4", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\admin\\balance-history.tsx": "5", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\admin\\expense-statistics.tsx": "6", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\admin\\quick-transfer.tsx": "7", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\admin\\stats-cards.tsx": "8", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\admin\\transaction-table.tsx": "9", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\admin\\weekly-activity.tsx": "10", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\charts\\bar-chart.tsx": "11", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\charts\\line-chart.tsx": "12", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\charts\\pie-chart.tsx": "13", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\dashboard\\account-card.tsx": "14", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\dashboard\\header.tsx": "15", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\dashboard\\recent-transactions.tsx": "16", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\dashboard\\sidebar.tsx": "17", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\dashboard\\statistics.tsx": "18", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\dashboard\\transfer.tsx": "19", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\ui\\button.tsx": "20", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\ui\\card.tsx": "21", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\ui\\input.tsx": "22", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\lib\\utils.ts": "23", "D:\\workspace\\.typescript\\aetrustfrontend\\src\\types\\index.ts": "24"}, {"size": 3573, "mtime": *************, "results": "25", "hashOfConfig": "26"}, {"size": 2694, "mtime": 1756595785926, "results": "27", "hashOfConfig": "26"}, {"size": 689, "mtime": 1756594306335, "results": "28", "hashOfConfig": "26"}, {"size": 3017, "mtime": 1756595560378, "results": "29", "hashOfConfig": "26"}, {"size": 688, "mtime": 1756595344063, "results": "30", "hashOfConfig": "26"}, {"size": 1688, "mtime": 1756595304409, "results": "31", "hashOfConfig": "26"}, {"size": 3014, "mtime": 1756595331424, "results": "32", "hashOfConfig": "26"}, {"size": 2952, "mtime": 1756595583235, "results": "33", "hashOfConfig": "26"}, {"size": 4080, "mtime": 1756595373118, "results": "34", "hashOfConfig": "26"}, {"size": 659, "mtime": 1756595373954, "results": "35", "hashOfConfig": "26"}, {"size": 1406, "mtime": 1756595121955, "results": "36", "hashOfConfig": "26"}, {"size": 1258, "mtime": 1756595143991, "results": "37", "hashOfConfig": "26"}, {"size": 1760, "mtime": 1756595108364, "results": "38", "hashOfConfig": "26"}, {"size": 2714, "mtime": 1756595164894, "results": "39", "hashOfConfig": "26"}, {"size": 2220, "mtime": 1756595871738, "results": "40", "hashOfConfig": "26"}, {"size": 2962, "mtime": 1756595204785, "results": "41", "hashOfConfig": "26"}, {"size": 2906, "mtime": 1756595824136, "results": "42", "hashOfConfig": "26"}, {"size": 1676, "mtime": 1756595180391, "results": "43", "hashOfConfig": "26"}, {"size": 1995, "mtime": 1756595232969, "results": "44", "hashOfConfig": "26"}, {"size": 1647, "mtime": 1756595068024, "results": "45", "hashOfConfig": "26"}, {"size": 1804, "mtime": 1756595081867, "results": "46", "hashOfConfig": "26"}, {"size": 800, "mtime": 1756595093035, "results": "47", "hashOfConfig": "26"}, {"size": 703, "mtime": 1756595706474, "results": "48", "hashOfConfig": "26"}, {"size": 798, "mtime": 1756595056036, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "16mv8bq", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\workspace\\.typescript\\aetrustfrontend\\src\\app\\admin\\page.tsx", ["122"], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\app\\dashboard\\page.tsx", [], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\app\\layout.tsx", [], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\app\\page.tsx", [], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\admin\\balance-history.tsx", [], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\admin\\expense-statistics.tsx", [], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\admin\\quick-transfer.tsx", [], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\admin\\stats-cards.tsx", [], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\admin\\transaction-table.tsx", ["123"], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\admin\\weekly-activity.tsx", [], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\charts\\bar-chart.tsx", [], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\charts\\line-chart.tsx", [], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\charts\\pie-chart.tsx", ["124"], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\dashboard\\account-card.tsx", [], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\dashboard\\header.tsx", [], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\dashboard\\recent-transactions.tsx", [], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\dashboard\\sidebar.tsx", [], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\dashboard\\statistics.tsx", ["125"], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\dashboard\\transfer.tsx", ["126", "127", "128"], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\ui\\button.tsx", ["129"], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\ui\\card.tsx", [], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\components\\ui\\input.tsx", ["130"], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\lib\\utils.ts", [], [], "D:\\workspace\\.typescript\\aetrustfrontend\\src\\types\\index.ts", [], [], {"ruleId": "131", "severity": 1, "message": "132", "line": 3, "column": 10, "nodeType": null, "messageId": "133", "endLine": 3, "endColumn": 18}, {"ruleId": "131", "severity": 1, "message": "134", "line": 57, "column": 47, "nodeType": null, "messageId": "133", "endLine": 57, "endColumn": 52}, {"ruleId": "131", "severity": 1, "message": "135", "line": 17, "column": 3, "nodeType": null, "messageId": "133", "endLine": 17, "endColumn": 8}, {"ruleId": "131", "severity": 1, "message": "136", "line": 4, "column": 29, "nodeType": null, "messageId": "133", "endLine": 4, "endColumn": 44}, {"ruleId": "131", "severity": 1, "message": "137", "line": 3, "column": 29, "nodeType": null, "messageId": "133", "endLine": 3, "endColumn": 39}, {"ruleId": "131", "severity": 1, "message": "138", "line": 3, "column": 41, "nodeType": null, "messageId": "133", "endLine": 3, "endColumn": 50}, {"ruleId": "131", "severity": 1, "message": "139", "line": 4, "column": 10, "nodeType": null, "messageId": "133", "endLine": 4, "endColumn": 16}, {"ruleId": "131", "severity": 1, "message": "140", "line": 38, "column": 32, "nodeType": null, "messageId": "133", "endLine": 38, "endColumn": 39}, {"ruleId": "141", "severity": 2, "message": "142", "line": 4, "column": 18, "nodeType": "143", "messageId": "144", "endLine": 4, "endColumn": 28, "suggestions": "145"}, "@typescript-eslint/no-unused-vars", "'useState' is defined but never used.", "unusedVar", "'index' is defined but never used.", "'width' is assigned a value but never used.", "'CustomPieLegend' is defined but never used.", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", "'Button' is defined but never used.", "'as<PERSON><PERSON>d' is assigned a value but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["146"], {"messageId": "147", "fix": "148", "desc": "149"}, "replaceEmptyInterfaceWithSuper", {"range": "150", "text": "151"}, "Replace empty interface with a type alias.", [72, 149], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>"]