"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { BarChartComponent } from "@/components/charts/bar-chart"
import { WeeklyActivity } from "@/types"

interface WeeklyActivityProps {
  data: WeeklyActivity[]
}

export function WeeklyActivityCard({ data }: WeeklyActivityProps) {
  return (
    <Card className="bg-white rounded-[25px] border-none shadow-lg">
      <CardHeader>
        <CardTitle className="text-[#343C6A] text-xl font-semibold">Weekly Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <BarChartComponent data={data} height={250} />
      </CardContent>
    </Card>
  )
}
