"use client"

import { useState } from "react"
import { Header } from "@/components/dashboard/header"
import { Sidebar } from "@/components/dashboard/sidebar"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ArrowLeft, CreditCard, Building, Smartphone, Plus } from "lucide-react"

const quickAmounts = [50, 100, 200, 500]

const fundingSources = [
  {
    id: 'bank',
    title: 'Bank Transfer',
    description: 'Add funds from your bank account',
    icon: <Building className="h-6 w-6" />,
    color: '#059AD1'
  },
  {
    id: 'card',
    title: 'Debit/Credit Card',
    description: 'Add funds using your card',
    icon: <CreditCard className="h-6 w-6" />,
    color: '#16DBCC'
  },
  {
    id: 'mobile',
    title: 'Mobile Money',
    description: 'Add funds via mobile money',
    icon: <Smartphone className="h-6 w-6" />,
    color: '#F2B134'
  }
]

export default function AddFundsPage() {
  const [activeMenuItem, setActiveMenuItem] = useState("add-funds")
  const [amount, setAmount] = useState("100")
  const [selectedQuickAmount, setSelectedQuickAmount] = useState<number | null>(null)
  const [selectedSource, setSelectedSource] = useState<string | null>(null)

  const handleQuickAmountClick = (value: number) => {
    setAmount(value.toString())
    setSelectedQuickAmount(value)
  }

  const handleAmountChange = (value: string) => {
    setAmount(value)
    setSelectedQuickAmount(null)
  }

  const handleSourceSelect = (sourceId: string) => {
    setSelectedSource(sourceId)
  }

  return (
    <div className="flex h-screen bg-[#F5F7FA]">
      <Sidebar activeItem={activeMenuItem} onItemClick={setActiveMenuItem} />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header 
          title="Add Funds" 
          userName="James Bond"
        />
        
        <main className="flex-1 overflow-auto p-4 md:p-8">
          <div className="max-w-4xl mx-auto">
            {/* Back Button */}
            <Button 
              variant="ghost" 
              className="mb-6 text-[#343C6A] hover:bg-gray-100"
              onClick={() => window.history.back()}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column - Amount Entry */}
              <div className="space-y-6">
                <Card className="bg-[#1B263B] text-white">
                  <CardContent className="p-8">
                    <div className="text-center space-y-6">
                      <div>
                        <p className="text-[#AFABAB] text-lg mb-2">Enter Amount</p>
                        <div className="relative">
                          <span className="text-6xl font-semibold">${amount}</span>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <p className="text-[#AFABAB] text-left">Quick Amounts</p>
                        <div className="grid grid-cols-4 gap-3">
                          {quickAmounts.map((value) => (
                            <Button
                              key={value}
                              variant="outline"
                              className={`border-[#059AD1] text-white hover:bg-[#059AD1]/20 ${
                                selectedQuickAmount === value ? 'bg-[#059AD1]/20' : 'bg-black/30'
                              }`}
                              onClick={() => handleQuickAmountClick(value)}
                            >
                              ${value}
                            </Button>
                          ))}
                        </div>
                      </div>

                      <div className="pt-4">
                        <Label htmlFor="customAmount" className="text-[#AFABAB] text-left block mb-2">
                          Or enter custom amount
                        </Label>
                        <Input
                          id="customAmount"
                          type="number"
                          placeholder="0.00"
                          value={amount}
                          onChange={(e) => handleAmountChange(e.target.value)}
                          className="bg-black/30 border-[#059AD1] text-white placeholder:text-[#AFABAB]"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Right Column - Funding Sources */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold text-[#333B69] mb-4">Select Funding Source</h3>
                  <div className="space-y-4">
                    {fundingSources.map((source) => (
                      <Card 
                        key={source.id} 
                        className={`cursor-pointer transition-all border-2 ${
                          selectedSource === source.id 
                            ? 'border-[#059AD1] bg-[#059AD1]/5' 
                            : 'border-gray-200 hover:border-[#059AD1]/50'
                        }`}
                        onClick={() => handleSourceSelect(source.id)}
                      >
                        <CardContent className="p-6">
                          <div className="flex items-center gap-4">
                            <div 
                              className="w-12 h-12 rounded-xl flex items-center justify-center"
                              style={{ backgroundColor: `${source.color}20`, color: source.color }}
                            >
                              {source.icon}
                            </div>
                            
                            <div className="flex-1">
                              <h4 className="font-medium text-[#1C1C1E] mb-1">{source.title}</h4>
                              <p className="text-sm text-[#6E6E6E]">{source.description}</p>
                            </div>
                            
                            <div className={`w-5 h-5 rounded-full border-2 ${
                              selectedSource === source.id 
                                ? 'border-[#059AD1] bg-[#059AD1]' 
                                : 'border-gray-300'
                            }`}>
                              {selectedSource === source.id && (
                                <div className="w-full h-full rounded-full bg-white scale-50"></div>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>

                {/* Fee Information */}
                <Card className="bg-[#F1F6FD] border border-[#A7C5FD]">
                  <CardContent className="p-4">
                    <h4 className="font-medium text-[#333B69] mb-3">Transaction Fees</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-[#6E6E6E]">Bank Transfer:</span>
                        <span className="font-medium">Free</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-[#6E6E6E]">Debit/Credit Card:</span>
                        <span className="font-medium">1.5% + $0.30</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-[#6E6E6E]">Mobile Money:</span>
                        <span className="font-medium">1.0%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Current Balance */}
                <Card>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-center">
                      <span className="text-[#6E6E6E]">Current Balance:</span>
                      <span className="text-xl font-bold text-[#333B69]">$35,673.00</span>
                    </div>
                    <div className="flex justify-between items-center mt-2">
                      <span className="text-[#6E6E6E]">After Adding:</span>
                      <span className="text-xl font-bold text-green-600">
                        ${(35673 + parseFloat(amount || '0')).toLocaleString()}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Continue Button */}
            <div className="mt-8 flex justify-center">
              <Button 
                className="bg-[#059AD1] hover:bg-[#059AD1]/90 text-white px-16 py-6 text-lg rounded-2xl"
                disabled={!selectedSource || !amount}
                onClick={() => {
                  // Navigate to payment details page
                  window.location.href = `/dashboard/add-funds/${selectedSource}`
                }}
              >
                Continue
              </Button>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
