(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[306],{1524:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},1953:(e,t,r)=>{"use strict";r.d(t,{P:()=>y});var n=r(12115),i=r(81262),a=r(74797),l=r(38881),o=r(84020),c=r(72743),s=r(85224),u=r(92377),f=["width","height"];function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var p={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},y=(0,n.forwardRef)(function(e,t){var r,y=(0,s.e)(e.categoricalChartProps,p),{width:v,height:h}=y,m=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(y,f);if(!(0,u.F)(v)||!(0,u.F)(h))return null;var{chartName:b,defaultTooltipEventType:g,validateTooltipEventTypes:x,tooltipPayloadSearcher:O,categoricalChartProps:w}=e;return n.createElement(i.J,{preloadedState:{options:{chartName:b,defaultTooltipEventType:g,validateTooltipEventTypes:x,tooltipPayloadSearcher:O,eventEmitter:void 0}},reduxStoreName:null!=(r=w.id)?r:b},n.createElement(a.TK,{chartData:w.data}),n.createElement(l.s,{width:v,height:h,layout:y.layout,margin:y.margin}),n.createElement(o.p,{accessibilityLayer:y.accessibilityLayer,barCategoryGap:y.barCategoryGap,maxBarSize:y.maxBarSize,stackOffset:y.stackOffset,barGap:y.barGap,barSize:y.barSize,syncId:y.syncId,syncMethod:y.syncMethod,className:y.className}),n.createElement(c.L,d({},m,{width:v,height:h,ref:t})))})},14724:(e,t,r)=>{"use strict";r.d(t,{Z:()=>b});var n=r(12115),i=r(40207),a=r.n(i),l=r(63296),o=r(87095),c=r(70543),s=r(51023),u=r(49580),f=["valueAccessor"],d=["data","dataKey","clockWise","id","textBreakAll"];function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function h(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var m=e=>Array.isArray(e.value)?a()(e.value):e.value;function b(e){var{valueAccessor:t=m}=e,r=h(e,f),{data:i,dataKey:a,clockWise:y,id:b,textBreakAll:g}=r,x=h(r,d);return i&&i.length?n.createElement(o.W,{className:"recharts-label-list"},i.map((e,r)=>{var i=(0,u.uy)(a)?t(e,r):(0,s.kr)(e&&e.payload,a),o=(0,u.uy)(b)?{}:{id:"".concat(b,"-").concat(r)};return n.createElement(l.J,p({},(0,c.J9)(e,!0),x,o,{parentViewBox:e.parentViewBox,value:i,textBreakAll:g,viewBox:l.J.parseViewBox((0,u.uy)(y)?e:v(v({},e),{},{clockWise:y})),key:"label-".concat(r),index:r}))})):null}b.displayName="LabelList",b.renderCallByParent=function(e,t){var r,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&i&&!e.label)return null;var{children:a}=e,o=(0,c.aS)(a,b).map((e,r)=>(0,n.cloneElement)(e,{data:t,key:"labelList-".concat(r)}));return i?[(r=e.label,r?!0===r?n.createElement(b,{key:"labelList-implicit",data:t}):n.isValidElement(r)||(0,l.Z)(r)?n.createElement(b,{key:"labelList-implicit",data:t,content:r}):"object"==typeof r?n.createElement(b,p({data:t},r,{key:"labelList-implicit"})):null:null),...o]:o}},18085:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},20774:(e,t,r)=>{"use strict";r.d(t,{f:()=>d});var n=r(49580),i=r(64680),a=r(33692);class l{static create(e){return new l(e)}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(t){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}constructor(e){this.scale=e}}!function(e,t,r){var n;(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:1e-4,enumerable:!0,configurable:!0,writable:!0}):e[t]=1e-4}(l,"EPS",1e-4);var o=function(e){var{width:t,height:r}=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,a=Math.atan(r/t);return Math.abs(i>a&&i<Math.PI-a?r/Math.sin(i):t/Math.cos(i))};function c(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],i=0;i<e.length;i+=t)if(void 0!==r&&!0!==r(e[i]))return;else n.push(e[i]);return n}function s(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e,t,r){var l,{tick:u,ticks:d,viewBox:p,minTickGap:y,orientation:v,interval:h,tickFormatter:m,unit:b,angle:g}=e;if(!d||!d.length||!u)return[];if((0,n.Et)(h)||a.m.isSsr)return null!=(l=c(d,((0,n.Et)(h)?h:0)+1))?l:[];var x="top"===v||"bottom"===v?"width":"height",O=b&&"width"===x?(0,i.Pu)(b,{fontSize:t,letterSpacing:r}):{width:0,height:0},w=(e,n)=>{var a,l="function"==typeof m?m(e.value,n):e.value;return"width"===x?(a=(0,i.Pu)(l,{fontSize:t,letterSpacing:r}),o({width:a.width+O.width,height:a.height+O.height},g)):(0,i.Pu)(l,{fontSize:t,letterSpacing:r})[x]},P=d.length>=2?(0,n.sA)(d[1].coordinate-d[0].coordinate):1,E=function(e,t,r){var n="width"===r,{x:i,y:a,width:l,height:o}=e;return 1===t?{start:n?i:a,end:n?i+l:a+o}:{start:n?i+l:a+o,end:n?i:a}}(p,P,x);return"equidistantPreserveStart"===h?function(e,t,r,n,i){for(var a,l=(n||[]).slice(),{start:o,end:u}=t,f=0,d=1,p=o;d<=l.length;)if(a=function(){var t,a=null==n?void 0:n[f];if(void 0===a)return{v:c(n,d)};var l=f,y=()=>(void 0===t&&(t=r(a,l)),t),v=a.coordinate,h=0===f||s(e,v,y,p,u);h||(f=0,p=o,d+=1),h&&(p=v+e*(y()/2+i),f+=d)}())return a.v;return[]}(P,E,w,d,y):("preserveStart"===h||"preserveStartEnd"===h?function(e,t,r,n,i,a){var l=(n||[]).slice(),o=l.length,{start:c,end:u}=t;if(a){var d=n[o-1],p=r(d,o-1),y=e*(d.coordinate+e*p/2-u);l[o-1]=d=f(f({},d),{},{tickCoord:y>0?d.coordinate-y*e:d.coordinate}),s(e,d.tickCoord,()=>p,c,u)&&(u=d.tickCoord-e*(p/2+i),l[o-1]=f(f({},d),{},{isShow:!0}))}for(var v=a?o-1:o,h=function(t){var n,a=l[t],o=()=>(void 0===n&&(n=r(a,t)),n);if(0===t){var d=e*(a.coordinate-e*o()/2-c);l[t]=a=f(f({},a),{},{tickCoord:d<0?a.coordinate-d*e:a.coordinate})}else l[t]=a=f(f({},a),{},{tickCoord:a.coordinate});s(e,a.tickCoord,o,c,u)&&(c=a.tickCoord+e*(o()/2+i),l[t]=f(f({},a),{},{isShow:!0}))},m=0;m<v;m++)h(m);return l}(P,E,w,d,y,"preserveStartEnd"===h):function(e,t,r,n,i){for(var a=(n||[]).slice(),l=a.length,{start:o}=t,{end:c}=t,u=function(t){var n,u=a[t],d=()=>(void 0===n&&(n=r(u,t)),n);if(t===l-1){var p=e*(u.coordinate+e*d()/2-c);a[t]=u=f(f({},u),{},{tickCoord:p>0?u.coordinate-p*e:u.coordinate})}else a[t]=u=f(f({},u),{},{tickCoord:u.coordinate});s(e,u.tickCoord,d,o,c)&&(c=u.tickCoord-e*(d()/2+i),a[t]=f(f({},u),{},{isShow:!0}))},d=l-1;d>=0;d--)u(d);return a}(P,E,w,d,y)).filter(e=>e.isShow)}},26921:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},29552:(e,t,r)=>{"use strict";r.d(t,{Q:()=>c,l:()=>o});var n=r(12115),i=r(81024),a=r(1444),l=r(70806);function o(e,t){var r,n,l=(0,i.G)(t=>(0,a.Rl)(t,e)),o=(0,i.G)(e=>(0,a.sf)(e,t)),c=null!=(r=null==l?void 0:l.allowDataOverflow)?r:a.PU.allowDataOverflow,s=null!=(n=null==o?void 0:o.allowDataOverflow)?n:a.cd.allowDataOverflow;return{needClip:c||s,needClipX:c,needClipY:s}}function c(e){var{xAxisId:t,yAxisId:r,clipPathId:i}=e,a=(0,l.oM)(),{needClipX:c,needClipY:s,needClip:u}=o(t,r);if(!u)return null;var{x:f,y:d,width:p,height:y}=a;return n.createElement("clipPath",{id:"clipPath-".concat(i)},n.createElement("rect",{x:c?f:f-p/2,y:s?d:d-y/2,width:c?p:2*p,height:s?y:2*y}))}},33323:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.last=function(e){return e[e.length-1]}},37772:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},39867:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},40207:(e,t,r)=>{e.exports=r(56942).last},47734:(e,t,r)=>{"use strict";r.d(t,{W:()=>g});var n=r(12115),i=r(2821),a=r(76177),l=r(81024),o=r(92487),c=r(1444),s=r(8291),u=r(35704),f=["children"],d=["dangerouslySetInnerHTML","ticks"];function p(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function v(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function h(e){var t=(0,l.j)(),r=(0,n.useMemo)(()=>{var{children:t}=e;return v(e,f)},[e]),i=(0,l.G)(e=>(0,c.Rl)(e,r.id)),a=r===i;return((0,n.useEffect)(()=>(t((0,o.Vi)(r)),()=>{t((0,o.MC)(r))}),[r,t]),a)?e.children:null}var m=e=>{var{xAxisId:t,className:r}=e,o=(0,l.G)(s.c2),f=(0,u.r)(),p="xAxis",h=(0,l.G)(e=>(0,c.iV)(e,p,t,f)),m=(0,l.G)(e=>(0,c.Zi)(e,p,t,f)),b=(0,l.G)(e=>(0,c.Lw)(e,t)),g=(0,l.G)(e=>(0,c.L$)(e,t));if(null==b||null==g)return null;var{dangerouslySetInnerHTML:x,ticks:O}=e,w=v(e,d);return n.createElement(a.u,y({},w,{scale:h,x:g.x,y:g.y,width:b.width,height:b.height,className:(0,i.$)("recharts-".concat(p," ").concat(p),r),viewBox:o,ticks:m}))},b=e=>{var t,r,i,a,l;return n.createElement(h,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(i=e.angle)?i:0,minTickGap:null!=(a=e.minTickGap)?a:5,tick:null==(l=e.tick)||l,tickFormatter:e.tickFormatter},n.createElement(m,e))};class g extends n.Component{render(){return n.createElement(b,this.props)}}p(g,"displayName","XAxis"),p(g,"defaultProps",{allowDataOverflow:c.PU.allowDataOverflow,allowDecimals:c.PU.allowDecimals,allowDuplicatedCategory:c.PU.allowDuplicatedCategory,height:c.PU.height,hide:!1,mirror:c.PU.mirror,orientation:c.PU.orientation,padding:c.PU.padding,reversed:c.PU.reversed,scale:c.PU.scale,tickCount:c.PU.tickCount,type:c.PU.type,xAxisId:0})},50906:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},56942:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(33323),i=r(92673),a=r(89644);t.last=function(e){if(a.isArrayLike(e))return n.last(i.toArray(e))}},58155:(e,t,r)=>{"use strict";r.d(t,{y:()=>ex,L:()=>eg});var n=r(12115),i=r(2821),a=r(87095),l=r(69386),o=r(14724),c=r(49580),s=r(70543),u=r(33692),f=r(51023),d=r(84072),p=r(49887),y=["x","y"];function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function b(e,t){var{x:r,y:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,y),a=parseInt("".concat(r),10),l=parseInt("".concat(n),10),o=parseInt("".concat(t.height||i.height),10),c=parseInt("".concat(t.width||i.width),10);return m(m(m(m(m({},t),i),a?{x:a}:{}),l?{y:l}:{}),{},{height:o,width:c,name:t.name,radius:t.radius})}function g(e){return n.createElement(p.y,v({shapeType:"rectangle",propTransformer:b,activeClassName:"recharts-active-bar"},e))}var x=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,n)=>{if((0,c.Et)(e))return e;var i=(0,c.Et)(r)||(0,c.uy)(r);return i?e(r,n):(i||function(e,t){if(!e)throw Error("Invariant failed")}(!1),t)}},O=r(92056),w=r(87176),P=r(64148),E=r(29552),k=r(90167),j=r(76069),A=r(1444),S=r(90135),C=r(8291),I=r(15195),D=r(92377),z=r(34565),M=r(35923);function N(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function T(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?N(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):N(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var G=(0,j.Mz)([A.ld,(e,t,r,n,i)=>i],(e,t)=>e.filter(e=>"bar"===e.type).find(e=>e.id===t)),B=(0,j.Mz)([G],e=>null==e?void 0:e.maxBarSize),R=(e,t,r)=>{var n=null!=r?r:e;if(!(0,c.uy)(n))return(0,c.F4)(n,t,0)},L=(0,j.Mz)([k.fz,A.ld,(e,t)=>t,(e,t,r)=>r,(e,t,r,n)=>n],(e,t,r,n,i)=>t.filter(t=>"horizontal"===e?t.xAxisId===r:t.yAxisId===n).filter(e=>e.isPanorama===i).filter(e=>!1===e.hide).filter(e=>"bar"===e.type)),W=(0,j.Mz)([L,I.x3,(e,t,r)=>"horizontal"===(0,k.fz)(e)?(0,A.BQ)(e,"xAxis",t):(0,A.BQ)(e,"yAxis",r)],(e,t,r)=>{var n=e.filter(M.g),i=e.filter(e=>null==e.stackId);return[...Object.entries(n.reduce((e,t)=>(e[t.stackId]||(e[t.stackId]=[]),e[t.stackId].push(t),e),{})).map(e=>{var[n,i]=e;return{stackId:n,dataKeys:i.map(e=>e.dataKey),barSize:R(t,r,i[0].barSize)}}),...i.map(e=>({stackId:void 0,dataKeys:[e.dataKey].filter(e=>null!=e),barSize:R(t,r,e.barSize)}))]}),K=(e,t,r,n)=>{var i,a;return"horizontal"===(0,k.fz)(e)?(i=(0,A.Gx)(e,"xAxis",t,n),a=(0,A.CR)(e,"xAxis",t,n)):(i=(0,A.Gx)(e,"yAxis",r,n),a=(0,A.CR)(e,"yAxis",r,n)),(0,f.Hj)(i,a)},F=(0,j.Mz)([W,I.JN,I._5,I.gY,(e,t,r,n,i)=>{var a,l,o,s,u=G(e,t,r,n,i);if(null!=u){var d=(0,k.fz)(e),p=(0,I.JN)(e),{maxBarSize:y}=u,v=(0,c.uy)(y)?p:y;return"horizontal"===d?(o=(0,A.Gx)(e,"xAxis",t,n),s=(0,A.CR)(e,"xAxis",t,n)):(o=(0,A.Gx)(e,"yAxis",r,n),s=(0,A.CR)(e,"yAxis",r,n)),null!=(a=null!=(l=(0,f.Hj)(o,s,!0))?l:v)?a:0}},K,B],(e,t,r,n,i,a,l)=>{var o=function(e,t,r,n,i){var a,l=n.length;if(!(l<1)){var o=(0,c.F4)(e,r,0,!0),s=[];if((0,D.H)(n[0].barSize)){var u=!1,f=r/l,d=n.reduce((e,t)=>e+(t.barSize||0),0);(d+=(l-1)*o)>=r&&(d-=(l-1)*o,o=0),d>=r&&f>0&&(u=!0,f*=.9,d=l*f);var p={offset:((r-d)/2|0)-o,size:0};a=n.reduce((e,t)=>{var r,n=[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:p.offset+p.size+o,size:u?f:null!=(r=t.barSize)?r:0}}];return p=n[n.length-1].position,n},s)}else{var y=(0,c.F4)(t,r,0,!0);r-2*y-(l-1)*o<=0&&(o=0);var v=(r-2*y-(l-1)*o)/l;v>1&&(v>>=0);var h=(0,D.H)(i)?Math.min(v,i):v;a=n.reduce((e,t,r)=>[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:y+(v+o)*r+(v-h)/2,size:h}}],s)}return a}}(r,n,i!==a?i:a,e,(0,c.uy)(l)?t:l);return i!==a&&null!=o&&(o=o.map(e=>T(T({},e),{},{position:T(T({},e.position),{},{offset:e.position.offset-i/2})}))),o}),V=(0,j.Mz)([F,G],(e,t)=>{if(null!=e&&null!=t){var r=e.find(e=>e.stackId===t.stackId&&null!=t.dataKey&&e.dataKeys.includes(t.dataKey));if(null!=r)return r.position}}),H=(0,j.Mz)([(e,t,r,n)=>"horizontal"===(0,k.fz)(e)?(0,A.TC)(e,"yAxis",r,n):(0,A.TC)(e,"xAxis",t,n),G],(e,t)=>{var r=(0,z.x)(t);if(!e||null==r||null==t)return;var{stackId:n}=t;if(null!=n){var i=e[n];if(i){var{stackedData:a}=i;if(a)return a.find(e=>e.key===r)}}}),J=(0,j.Mz)([C.HZ,(e,t,r,n)=>(0,A.Gx)(e,"xAxis",t,n),(e,t,r,n)=>(0,A.Gx)(e,"yAxis",r,n),(e,t,r,n)=>(0,A.CR)(e,"xAxis",t,n),(e,t,r,n)=>(0,A.CR)(e,"yAxis",r,n),V,k.fz,S.HS,K,H,G,(e,t,r,n,i,a)=>a],(e,t,r,n,i,a,l,o,c,s,u,f)=>{var d,{chartData:p,dataStartIndex:y,dataEndIndex:v}=o;if(null!=u&&null!=a&&("horizontal"===l||"vertical"===l)&&null!=t&&null!=r&&null!=n&&null!=i&&null!=c){var{data:h}=u;if(null!=(d=null!=h&&h.length>0?h:null==p?void 0:p.slice(y,v+1)))return eg({layout:l,barSettings:u,pos:a,bandSize:c,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,stackedData:s,displayedData:d,offset:e,cells:f})}}),U=r(81024),$=r(35704),Z=r(72481),_=r(68997),X=r(94913),Q=r(85224),q=r(48971),Y=r(3838),ee=r(4264),et=r(34140),er=["onMouseEnter","onMouseLeave","onClick"],en=["value","background","tooltipPosition"],ei=["id"],ea=["onMouseEnter","onClick","onMouseLeave"];function el(){return(el=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function eo(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ec(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eo(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eo(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function es(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function eu(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:l,unit:o}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,dataKey:t,nameKey:void 0,name:(0,f.uM)(a,t),hide:l,type:e.tooltipType,color:e.fill,unit:o}}}function ef(e){var t=(0,U.G)(Z.A2),{data:r,dataKey:i,background:a,allOtherBarProps:l}=e,{onMouseEnter:o,onMouseLeave:c,onClick:u}=l,f=es(l,er),p=(0,O.Cj)(o,i),y=(0,O.Pg)(c),v=(0,O.Ub)(u,i);if(!a||null==r)return null;var h=(0,s.J9)(a,!1);return n.createElement(n.Fragment,null,r.map((e,r)=>{var{value:l,background:o,tooltipPosition:c}=e,s=es(e,en);if(!o)return null;var u=p(e,r),m=y(e,r),b=v(e,r),x=ec(ec(ec(ec(ec({option:a,isActive:String(r)===t},s),{},{fill:"#eee"},o),h),(0,d.XC)(f,e,r)),{},{onMouseEnter:u,onMouseLeave:m,onClick:b,dataKey:i,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(g,el({key:"background-bar-".concat(r)},x))}))}function ed(e){var{data:t,props:r,showLabels:i}=e,l=(0,ee.u)(r),{id:c}=l,s=es(l,ei),{shape:u,dataKey:f,activeBar:p}=r,y=(0,U.G)(Z.A2),v=(0,U.G)(Z.Xb),{onMouseEnter:h,onClick:m,onMouseLeave:b}=r,x=es(r,ea),w=(0,O.Cj)(h,f),P=(0,O.Pg)(b),E=(0,O.Ub)(m,f);return t?n.createElement(n.Fragment,null,t.map((e,t)=>{var r=p&&String(t)===y&&(null==v||f===v),i=ec(ec(ec({},s),e),{},{isActive:r,option:r?p:u,index:t,dataKey:f});return n.createElement(a.W,el({className:"recharts-bar-rectangle"},(0,d.XC)(x,e,t),{onMouseEnter:w(e,t),onMouseLeave:P(e,t),onClick:E(e,t),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(t)}),n.createElement(g,i))}),i&&o.Z.renderCallByParent(r,t)):null}function ep(e){var{props:t,previousRectanglesRef:r}=e,{data:i,layout:l,isAnimationActive:o,animationBegin:s,animationDuration:u,animationEasing:f,onAnimationEnd:d,onAnimationStart:p}=t,y=r.current,v=(0,X.n)(t,"recharts-bar-"),[h,m]=(0,n.useState)(!1),b=(0,n.useCallback)(()=>{"function"==typeof d&&d(),m(!1)},[d]),g=(0,n.useCallback)(()=>{"function"==typeof p&&p(),m(!0)},[p]);return n.createElement(et.J,{begin:s,duration:u,isActive:o,easing:f,onAnimationEnd:b,onAnimationStart:g,key:v},e=>{var o=1===e?i:null==i?void 0:i.map((t,r)=>{var n=y&&y[r];if(n)return ec(ec({},t),{},{x:(0,c.GW)(n.x,t.x,e),y:(0,c.GW)(n.y,t.y,e),width:(0,c.GW)(n.width,t.width,e),height:(0,c.GW)(n.height,t.height,e)});if("horizontal"===l){var i=(0,c.GW)(0,t.height,e);return ec(ec({},t),{},{y:t.y+t.height-i,height:i})}var a=(0,c.GW)(0,t.width,e);return ec(ec({},t),{},{width:a})});return(e>0&&(r.current=null!=o?o:null),null==o)?null:n.createElement(a.W,null,n.createElement(ed,{props:t,data:o,showLabels:!h}))})}function ey(e){var{data:t,isAnimationActive:r}=e,i=(0,n.useRef)(null);return r&&t&&t.length&&(null==i.current||i.current!==t)?n.createElement(ep,{previousRectanglesRef:i,props:e}):n.createElement(ed,{props:e,data:t,showLabels:!0})}var ev=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:(0,f.kr)(e,t)}};class eh extends n.PureComponent{render(){var{hide:e,data:t,dataKey:r,className:l,xAxisId:o,yAxisId:c,needClip:s,background:u,id:f}=this.props;if(e)return null;var d=(0,i.$)("recharts-bar",l);return n.createElement(a.W,{className:d,id:f},s&&n.createElement("defs",null,n.createElement(E.Q,{clipPathId:f,xAxisId:o,yAxisId:c})),n.createElement(a.W,{className:"recharts-bar-rectangles",clipPath:s?"url(#clipPath-".concat(f,")"):void 0},n.createElement(ef,{data:t,dataKey:r,background:u,allOtherBarProps:this.props}),n.createElement(ey,this.props)),this.props.children)}}var em={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!u.m.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function eb(e){var t,{xAxisId:r,yAxisId:i,hide:a,legendType:o,minPointSize:c,activeBar:u,animationBegin:f,animationDuration:d,animationEasing:p,isAnimationActive:y}=e,{needClip:v}=(0,E.l)(r,i),h=(0,k.WX)(),m=(0,$.r)(),b=(0,s.aS)(e.children,l.f),g=(0,U.G)(t=>J(t,r,i,m,e.id,b));if("vertical"!==h&&"horizontal"!==h)return null;var x=null==g?void 0:g[0];return t=null==x||null==x.height||null==x.width?0:"vertical"===h?x.height/2:x.width/2,n.createElement(P.zk,{xAxisId:r,yAxisId:i,data:g,dataPointFormatter:ev,errorBarOffset:t},n.createElement(eh,el({},e,{layout:h,needClip:v,data:g,xAxisId:r,yAxisId:i,hide:a,legendType:o,minPointSize:c,activeBar:u,animationBegin:f,animationDuration:d,animationEasing:p,isAnimationActive:y})))}function eg(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:i,bandSize:a,xAxis:l,yAxis:o,xAxisTicks:s,yAxisTicks:u,stackedData:d,displayedData:p,offset:y,cells:v}=e,h="horizontal"===t?o:l,m=d?h.scale.domain():null,b=(0,f.DW)({numericAxis:h});return p.map((e,p)=>{d?g=(0,f._f)(d[p],m):Array.isArray(g=(0,f.kr)(e,r))||(g=[b,g]);var h=x(n,0)(g[1],p);if("horizontal"===t){var g,O,w,P,E,k,j,[A,S]=[o.scale(g[0]),o.scale(g[1])];O=(0,f.y2)({axis:l,ticks:s,bandSize:a,offset:i.offset,entry:e,index:p}),w=null!=(j=null!=S?S:A)?j:void 0,P=i.size;var C=A-S;if(E=(0,c.M8)(C)?0:C,k={x:O,y:y.top,width:P,height:y.height},Math.abs(h)>0&&Math.abs(E)<Math.abs(h)){var I=(0,c.sA)(E||h)*(Math.abs(h)-Math.abs(E));w-=I,E+=I}}else{var[D,z]=[l.scale(g[0]),l.scale(g[1])];if(O=D,w=(0,f.y2)({axis:o,ticks:u,bandSize:a,offset:i.offset,entry:e,index:p}),P=z-D,E=i.size,k={x:y.left,y:w,width:y.width,height:E},Math.abs(h)>0&&Math.abs(P)<Math.abs(h)){var M=(0,c.sA)(P||h)*(Math.abs(h)-Math.abs(P));P+=M}}return null==O||null==w||null==P||null==E?null:ec(ec({},e),{},{x:O,y:w,width:P,height:E,value:d?g:g[1],payload:e,background:k,tooltipPosition:{x:O+P/2,y:w+E/2}},v&&v[p]&&v[p].props)}).filter(Boolean)}function ex(e){var t=(0,Q.e)(e,em),r=(0,$.r)();return n.createElement(q.x,{id:t.id,type:"bar"},e=>n.createElement(n.Fragment,null,n.createElement(_.A,{legendPayload:(e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:(0,f.uM)(r,t),payload:e}]})(t)}),n.createElement(w.r,{fn:eu,args:t}),n.createElement(Y.p,{type:"bar",id:e,data:void 0,xAxisId:t.xAxisId,yAxisId:t.yAxisId,zAxisId:0,dataKey:t.dataKey,stackId:(0,f.$8)(t.stackId),hide:t.hide,barSize:t.barSize,minPointSize:t.minPointSize,maxBarSize:t.maxBarSize,isPanorama:r}),n.createElement(eb,el({},t,{id:e}))))}ex.displayName="Bar"},63296:(e,t,r)=>{"use strict";r.d(t,{J:()=>g,Z:()=>b});var n=r(12115),i=r(2821),a=r(39346),l=r(70543),o=r(49580),c=r(34010),s=r(90167),u=r(81024),f=r(14821),d=["offset"],p=["labelRef"];function y(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function m(){return(m=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var b=e=>null!=e&&"function"==typeof e;function g(e){var t,{offset:r=5}=e,v=h({offset:r},y(e,d)),{viewBox:b,position:g,value:x,children:O,content:w,className:P="",textBreakAll:E,labelRef:k}=v,j=(0,u.G)(f.D0),A=(0,s.sk)(),S=b||("center"===g?A:null!=j?j:A);if(!S||(0,o.uy)(x)&&(0,o.uy)(O)&&!(0,n.isValidElement)(w)&&"function"!=typeof w)return null;var C=h(h({},v),{},{viewBox:S});if((0,n.isValidElement)(w)){var{labelRef:I}=C,D=y(C,p);return(0,n.cloneElement)(w,D)}if("function"==typeof w){if(t=(0,n.createElement)(w,C),(0,n.isValidElement)(t))return t}else t=(e=>{var{value:t,formatter:r}=e,n=(0,o.uy)(e.children)?t:e.children;return"function"==typeof r?r(n):n})(v);var z="cx"in S&&(0,o.Et)(S.cx),M=(0,l.J9)(v,!0);if(z&&("insideStart"===g||"insideEnd"===g||"end"===g))return((e,t,r,a)=>{let l,s;var u,f,{position:d,offset:p,className:y}=e,{cx:v,cy:h,innerRadius:b,outerRadius:g,startAngle:x,endAngle:O,clockWise:w}=a,P=(b+g)/2,E=(l=x,s=O,(0,o.sA)(s-l)*Math.min(Math.abs(s-l),360)),k=E>=0?1:-1;"insideStart"===d?(u=x+k*p,f=w):"insideEnd"===d?(u=O-k*p,f=!w):"end"===d&&(u=O+k*p,f=w),f=E<=0?f:!f;var j=(0,c.IZ)(v,h,P,u),A=(0,c.IZ)(v,h,P,u+(f?1:-1)*359),S="M".concat(j.x,",").concat(j.y,"\n    A").concat(P,",").concat(P,",0,1,").concat(+!f,",\n    ").concat(A.x,",").concat(A.y),C=(0,o.uy)(e.id)?(0,o.NF)("recharts-radial-line-"):e.id;return n.createElement("text",m({},r,{dominantBaseline:"central",className:(0,i.$)("recharts-radial-bar-label",y)}),n.createElement("defs",null,n.createElement("path",{id:C,d:S})),n.createElement("textPath",{xlinkHref:"#".concat(C)},t))})(v,t,M,S);var N=z?((e,t,r)=>{var{cx:n,cy:i,innerRadius:a,outerRadius:l,startAngle:o,endAngle:s}=e,u=(o+s)/2;if("outside"===r){var{x:f,y:d}=(0,c.IZ)(n,i,l+t,u);return{x:f,y:d,textAnchor:f>=n?"start":"end",verticalAnchor:"middle"}}if("center"===r)return{x:n,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===r)return{x:n,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===r)return{x:n,y:i,textAnchor:"middle",verticalAnchor:"end"};var{x:p,y}=(0,c.IZ)(n,i,(a+l)/2,u);return{x:p,y,textAnchor:"middle",verticalAnchor:"middle"}})(S,v.offset,v.position):((e,t)=>{var{parentViewBox:r,offset:n,position:i}=e,{x:a,y:l,width:c,height:s}=t,u=s>=0?1:-1,f=u*n,d=u>0?"end":"start",p=u>0?"start":"end",y=c>=0?1:-1,v=y*n,m=y>0?"end":"start",b=y>0?"start":"end";if("top"===i)return h(h({},{x:a+c/2,y:l-u*n,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(l-r.y,0),width:c}:{});if("bottom"===i)return h(h({},{x:a+c/2,y:l+s+f,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(l+s),0),width:c}:{});if("left"===i){var g={x:a-v,y:l+s/2,textAnchor:m,verticalAnchor:"middle"};return h(h({},g),r?{width:Math.max(g.x-r.x,0),height:s}:{})}if("right"===i){var x={x:a+c+v,y:l+s/2,textAnchor:b,verticalAnchor:"middle"};return h(h({},x),r?{width:Math.max(r.x+r.width-x.x,0),height:s}:{})}var O=r?{width:c,height:s}:{};return"insideLeft"===i?h({x:a+v,y:l+s/2,textAnchor:b,verticalAnchor:"middle"},O):"insideRight"===i?h({x:a+c-v,y:l+s/2,textAnchor:m,verticalAnchor:"middle"},O):"insideTop"===i?h({x:a+c/2,y:l+f,textAnchor:"middle",verticalAnchor:p},O):"insideBottom"===i?h({x:a+c/2,y:l+s-f,textAnchor:"middle",verticalAnchor:d},O):"insideTopLeft"===i?h({x:a+v,y:l+f,textAnchor:b,verticalAnchor:p},O):"insideTopRight"===i?h({x:a+c-v,y:l+f,textAnchor:m,verticalAnchor:p},O):"insideBottomLeft"===i?h({x:a+v,y:l+s-f,textAnchor:b,verticalAnchor:d},O):"insideBottomRight"===i?h({x:a+c-v,y:l+s-f,textAnchor:m,verticalAnchor:d},O):i&&"object"==typeof i&&((0,o.Et)(i.x)||(0,o._3)(i.x))&&((0,o.Et)(i.y)||(0,o._3)(i.y))?h({x:a+(0,o.F4)(i.x,c),y:l+(0,o.F4)(i.y,s),textAnchor:"end",verticalAnchor:"end"},O):h({x:a+c/2,y:l+s/2,textAnchor:"middle",verticalAnchor:"middle"},O)})(v,S);return n.createElement(a.E,m({ref:k,className:(0,i.$)("recharts-label",P)},M,N,{breakAll:E}),t)}g.displayName="Label";var x=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:a,r:l,radius:c,innerRadius:s,outerRadius:u,x:f,y:d,top:p,left:y,width:v,height:h,clockWise:m,labelViewBox:b}=e;if(b)return b;if((0,o.Et)(v)&&(0,o.Et)(h)){if((0,o.Et)(f)&&(0,o.Et)(d))return{x:f,y:d,width:v,height:h};if((0,o.Et)(p)&&(0,o.Et)(y))return{x:p,y:y,width:v,height:h}}return(0,o.Et)(f)&&(0,o.Et)(d)?{x:f,y:d,width:0,height:0}:(0,o.Et)(t)&&(0,o.Et)(r)?{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:s||0,outerRadius:u||c||l||0,clockWise:m}:e.viewBox?e.viewBox:void 0};g.parseViewBox=x,g.renderCallByParent=function(e,t){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:i,labelRef:a}=e,c=x(e),s=(0,l.aS)(i,g).map((e,r)=>(0,n.cloneElement)(e,{viewBox:t||c,key:"label-".concat(r)}));return r?[((e,t,r)=>{if(!e)return null;var i={viewBox:t,labelRef:r};return!0===e?n.createElement(g,m({key:"label-implicit"},i)):(0,o.vh)(e)?n.createElement(g,m({key:"label-implicit",value:e},i)):(0,n.isValidElement)(e)?e.type===g?(0,n.cloneElement)(e,h({key:"label-implicit"},i)):n.createElement(g,m({key:"label-implicit",content:e},i)):b(e)?n.createElement(g,m({key:"label-implicit",content:e},i)):e&&"object"==typeof e?n.createElement(g,m({},e,{key:"label-implicit"},i)):null})(e.label,t||c,a),...s]:s}},64148:(e,t,r)=>{"use strict";r.d(t,{zk:()=>l});var n=r(12115),i=["children"],a=(0,n.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function l(e){var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,i);return n.createElement(a.Provider,{value:r},t)}},68425:(e,t,r)=>{"use strict";r.d(t,{d:()=>z});var n=r(12115),i=r(58672),a=r(49580),l=r(51023),o=r(20774),c=r(76177),s=r(90167),u=r(1444),f=r(81024),d=r(35704),p=r(85224),y=r(4264),v=["x1","y1","x2","y2","key"],h=["offset"],m=["xAxisId","yAxisId"],b=["xAxisId","yAxisId"];function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function x(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function O(){return(O=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function w(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var P=e=>{var{fill:t}=e;if(!t||"none"===t)return null;var{fillOpacity:r,x:i,y:a,width:l,height:o,ry:c}=e;return n.createElement("rect",{x:i,y:a,ry:c,width:l,height:o,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function E(e,t){var r;if(n.isValidElement(e))r=n.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var{x1:i,y1:a,x2:l,y2:o,key:c}=t,s=w(t,v),u=(0,y.u)(s),{offset:f}=u,d=w(u,h);r=n.createElement("line",O({},d,{x1:i,y1:a,x2:l,y2:o,fill:"none",key:c}))}return r}function k(e){var{x:t,width:r,horizontal:i=!0,horizontalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:l,yAxisId:o}=e,c=w(e,m),s=a.map((e,n)=>E(i,x(x({},c),{},{x1:t,y1:e,x2:t+r,y2:e,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},s)}function j(e){var{y:t,height:r,vertical:i=!0,verticalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:l,yAxisId:o}=e,c=w(e,b),s=a.map((e,n)=>E(i,x(x({},c),{},{x1:e,y1:t,x2:e,y2:t+r,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},s)}function A(e){var{horizontalFill:t,fillOpacity:r,x:i,y:a,width:l,height:o,horizontalPoints:c,horizontal:s=!0}=e;if(!s||!t||!t.length)return null;var u=c.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==u[0]&&u.unshift(0);var f=u.map((e,c)=>{var s=u[c+1]?u[c+1]-e:a+o-e;if(s<=0)return null;var f=c%t.length;return n.createElement("rect",{key:"react-".concat(c),y:e,x:i,height:s,width:l,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function S(e){var{vertical:t=!0,verticalFill:r,fillOpacity:i,x:a,y:l,width:o,height:c,verticalPoints:s}=e;if(!t||!r||!r.length)return null;var u=s.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==u[0]&&u.unshift(0);var f=u.map((e,t)=>{var s=u[t+1]?u[t+1]-e:a+o-e;if(s<=0)return null;var f=t%r.length;return n.createElement("rect",{key:"react-".concat(t),x:e,y:l,width:s,height:c,stroke:"none",fill:r[f],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var C=(e,t)=>{var{xAxis:r,width:n,height:i,offset:a}=e;return(0,l.PW)((0,o.f)(x(x(x({},c.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},I=(e,t)=>{var{yAxis:r,width:n,height:i,offset:a}=e;return(0,l.PW)((0,o.f)(x(x(x({},c.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},D={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function z(e){var t=(0,s.yi)(),r=(0,s.rY)(),l=(0,s.W7)(),o=x(x({},(0,p.e)(e,D)),{},{x:(0,a.Et)(e.x)?e.x:l.left,y:(0,a.Et)(e.y)?e.y:l.top,width:(0,a.Et)(e.width)?e.width:l.width,height:(0,a.Et)(e.height)?e.height:l.height}),{xAxisId:c,yAxisId:y,x:v,y:h,width:m,height:b,syncWithTicks:g,horizontalValues:w,verticalValues:E}=o,z=(0,d.r)(),M=(0,f.G)(e=>(0,u.ZB)(e,"xAxis",c,z)),N=(0,f.G)(e=>(0,u.ZB)(e,"yAxis",y,z));if(!(0,a.Et)(m)||m<=0||!(0,a.Et)(b)||b<=0||!(0,a.Et)(v)||v!==+v||!(0,a.Et)(h)||h!==+h)return null;var T=o.verticalCoordinatesGenerator||C,G=o.horizontalCoordinatesGenerator||I,{horizontalPoints:B,verticalPoints:R}=o;if((!B||!B.length)&&"function"==typeof G){var L=w&&w.length,W=G({yAxis:N?x(x({},N),{},{ticks:L?w:N.ticks}):void 0,width:t,height:r,offset:l},!!L||g);(0,i.R)(Array.isArray(W),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof W,"]")),Array.isArray(W)&&(B=W)}if((!R||!R.length)&&"function"==typeof T){var K=E&&E.length,F=T({xAxis:M?x(x({},M),{},{ticks:K?E:M.ticks}):void 0,width:t,height:r,offset:l},!!K||g);(0,i.R)(Array.isArray(F),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof F,"]")),Array.isArray(F)&&(R=F)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(P,{fill:o.fill,fillOpacity:o.fillOpacity,x:o.x,y:o.y,width:o.width,height:o.height,ry:o.ry}),n.createElement(A,O({},o,{horizontalPoints:B})),n.createElement(S,O({},o,{verticalPoints:R})),n.createElement(k,O({},o,{offset:l,horizontalPoints:B,xAxis:M,yAxis:N})),n.createElement(j,O({},o,{offset:l,verticalPoints:R,xAxis:M,yAxis:N})))}z.displayName="CartesianGrid"},74595:(e,t,r)=>{"use strict";r.d(t,{h:()=>g});var n=r(12115),i=r(2821),a=r(76177),l=r(92487),o=r(81024),c=r(1444),s=r(8291),u=r(35704),f=r(63296),d=["dangerouslySetInnerHTML","ticks"];function p(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function v(e){var t=(0,o.j)();return(0,n.useEffect)(()=>(t((0,l.cU)(e)),()=>{t((0,l.fR)(e))}),[e,t]),null}var h=e=>{var t,{yAxisId:r,className:p,width:v,label:h}=e,m=(0,n.useRef)(null),b=(0,n.useRef)(null),g=(0,o.G)(s.c2),x=(0,u.r)(),O=(0,o.j)(),w="yAxis",P=(0,o.G)(e=>(0,c.iV)(e,w,r,x)),E=(0,o.G)(e=>(0,c.wP)(e,r)),k=(0,o.G)(e=>(0,c.KR)(e,r)),j=(0,o.G)(e=>(0,c.Zi)(e,w,r,x));if((0,n.useLayoutEffect)(()=>{if(!("auto"!==v||!E||(0,f.Z)(h)||(0,n.isValidElement)(h))){var e,t=m.current,i=null==t||null==(e=t.tickRefs)?void 0:e.current,{tickSize:a,tickMargin:o}=t.props,c=(e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=e,l=0;if(t){t.forEach(e=>{if(e){var t=e.getBoundingClientRect();t.width>l&&(l=t.width)}});var o=r?r.getBoundingClientRect().width:0;return Math.round(l+(i+a)+o+(r?n:0))}return 0})({ticks:i,label:b.current,labelGapWithTick:5,tickSize:a,tickMargin:o});Math.round(E.width)!==Math.round(c)&&O((0,l.QG)({id:r,width:c}))}},[m,null==m||null==(t=m.current)||null==(t=t.tickRefs)?void 0:t.current,null==E?void 0:E.width,E,O,h,r,v]),null==E||null==k)return null;var{dangerouslySetInnerHTML:A,ticks:S}=e,C=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,d);return n.createElement(a.u,y({},C,{ref:m,labelRef:b,scale:P,x:k.x,y:k.y,width:E.width,height:E.height,className:(0,i.$)("recharts-".concat(w," ").concat(w),p),viewBox:g,ticks:j}))},m=e=>{var t,r,i,a,l;return n.createElement(n.Fragment,null,n.createElement(v,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(i=e.angle)?i:0,minTickGap:null!=(a=e.minTickGap)?a:5,tick:null==(l=e.tick)||l,tickFormatter:e.tickFormatter}),n.createElement(h,e))},b={allowDataOverflow:c.cd.allowDataOverflow,allowDecimals:c.cd.allowDecimals,allowDuplicatedCategory:c.cd.allowDuplicatedCategory,hide:!1,mirror:c.cd.mirror,orientation:c.cd.orientation,padding:c.cd.padding,reversed:c.cd.reversed,scale:c.cd.scale,tickCount:c.cd.tickCount,type:c.cd.type,width:c.cd.width,yAxisId:0};class g extends n.Component{render(){return n.createElement(m,this.props)}}p(g,"displayName","YAxis"),p(g,"defaultProps",b)},76177:(e,t,r)=>{"use strict";r.d(t,{u:()=>P});var n=r(12115),i=r(54241),a=r.n(i),l=r(2821);function o(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}var c=r(87095),s=r(39346),u=r(63296),f=r(49580),d=r(84072),p=r(70543),y=r(20774),v=r(4264),h=["viewBox"],m=["viewBox"];function b(){return(b=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function x(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){w(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function O(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function w(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class P extends n.Component{shouldComponentUpdate(e,t){var{viewBox:r}=e,n=O(e,h),i=this.props,{viewBox:a}=i,l=O(i,m);return!o(r,a)||!o(n,l)||!o(t,this.state)}getTickLineCoord(e){var t,r,n,i,a,l,{x:o,y:c,width:s,height:u,orientation:d,tickSize:p,mirror:y,tickMargin:v}=this.props,h=y?-1:1,m=e.tickSize||p,b=(0,f.Et)(e.tickCoord)?e.tickCoord:e.coordinate;switch(d){case"top":t=r=e.coordinate,l=(n=(i=c+!y*u)-h*m)-h*v,a=b;break;case"left":n=i=e.coordinate,a=(t=(r=o+!y*s)-h*m)-h*v,l=b;break;case"right":n=i=e.coordinate,a=(t=(r=o+y*s)+h*m)+h*v,l=b;break;default:t=r=e.coordinate,l=(n=(i=c+y*u)+h*m)+h*v,a=b}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:a,y:l}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:t,width:r,height:i,orientation:o,mirror:c,axisLine:s}=this.props,u=x(x(x({},(0,p.J9)(this.props,!1)),(0,p.J9)(s,!1)),{},{fill:"none"});if("top"===o||"bottom"===o){var f=+("top"===o&&!c||"bottom"===o&&c);u=x(x({},u),{},{x1:e,y1:t+f*i,x2:e+r,y2:t+f*i})}else{var d=+("left"===o&&!c||"right"===o&&c);u=x(x({},u),{},{x1:e+d*r,y1:t,x2:e+d*r,y2:t+i})}return n.createElement("line",b({},u,{className:(0,l.$)("recharts-cartesian-axis-line",a()(s,"className"))}))}static renderTickItem(e,t,r){var i,a=(0,l.$)(t.className,"recharts-cartesian-axis-tick-value");if(n.isValidElement(e))i=n.cloneElement(e,x(x({},t),{},{className:a}));else if("function"==typeof e)i=e(x(x({},t),{},{className:a}));else{var o="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(o=(0,l.$)(o,e.className)),i=n.createElement(s.E,b({},t,{className:o}),r)}return i}renderTicks(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:i,stroke:o,tick:s,tickFormatter:u,unit:f,padding:h}=this.props,m=(0,y.f)(x(x({},this.props),{},{ticks:r}),e,t),g=this.getTickTextAnchor(),O=this.getTickVerticalAnchor(),w=(0,v.u)(this.props),E=(0,p.J9)(s,!1),k=x(x({},w),{},{fill:"none"},(0,p.J9)(i,!1)),j=m.map((e,t)=>{var{line:r,tick:p}=this.getTickLineCoord(e),y=x(x(x(x({textAnchor:g,verticalAnchor:O},w),{},{stroke:"none",fill:o},E),p),{},{index:t,payload:e,visibleTicksCount:m.length,tickFormatter:u,padding:h});return n.createElement(c.W,b({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},(0,d.XC)(this.props,e,t)),i&&n.createElement("line",b({},k,r,{className:(0,l.$)("recharts-cartesian-axis-tick-line",a()(i,"className"))})),s&&P.renderTickItem(s,y,"".concat("function"==typeof u?u(e.value,t):e.value).concat(f||"")))});return j.length>0?n.createElement("g",{className:"recharts-cartesian-axis-ticks"},j):null}render(){var{axisLine:e,width:t,height:r,className:i,hide:a}=this.props;if(a)return null;var{ticks:o}=this.props;return null!=t&&t<=0||null!=r&&r<=0?null:n.createElement(c.W,{className:(0,l.$)("recharts-cartesian-axis",i),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||i!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,o),u.J.renderCallByParent(this.props))}constructor(e){super(e),this.tickRefs=n.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}}w(P,"displayName","CartesianAxis"),w(P,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},80019:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var n=r(12115),i=r(33308),a=r(1953),l=["axis"],o=(0,n.forwardRef)((e,t)=>n.createElement(a.P,{chartName:"LineChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:l,tooltipPayloadSearcher:i.uN,categoricalChartProps:e,ref:t}))},89559:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},89585:(e,t,r)=>{"use strict";r.d(t,{N:()=>ec,l:()=>eo});var n=r(12115),i=r(2821),a=r(7050),l=r(84072),o=r(4264);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var s=e=>{var{cx:t,cy:r,r:a,className:s}=e,u=(0,i.$)("recharts-dot",s);return t===+t&&r===+r&&a===+a?n.createElement("circle",c({},(0,o.u)(e),(0,l._U)(e),{className:u,cx:t,cy:r,r:a})):null},u=r(87095),f=r(14724),d=r(49580),p=r(70543),y=r(33692),v=r(51023),h=r(81024),m=r(72481),b=r(70806);function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function x(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function O(e){var{points:t,mainColor:r,activeDot:i,itemDataKey:a}=e,o=(0,h.G)(m.A2),c=(0,b.EI)();if(null==t||null==c)return null;var f=t.find(e=>c.includes(e.payload));return(0,d.uy)(f)?null:(e=>{var t,{point:r,childIndex:i,mainColor:a,activeDot:o,dataKey:c}=e;if(!1===o||null==r.x||null==r.y)return null;var f=x(x({index:i,dataKey:c,cx:r.x,cy:r.y,r:4,fill:null!=a?a:"none",strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},(0,p.J9)(o,!1)),(0,l._U)(o));return t=(0,n.isValidElement)(o)?(0,n.cloneElement)(o,f):"function"==typeof o?o(f):n.createElement(s,f),n.createElement(u.W,{className:"recharts-active-dot"},t)})({point:f,childIndex:Number(o),mainColor:r,dataKey:a,activeDot:i})}var w=r(87176),P=r(64148),E=r(29552),k=r(90167),j=r(35704),A=r(76069),S=r(90135),C=r(1444),I=(e,t,r,n)=>(0,C.Gx)(e,"xAxis",t,n),D=(e,t,r,n)=>(0,C.CR)(e,"xAxis",t,n),z=(e,t,r,n)=>(0,C.Gx)(e,"yAxis",r,n),M=(e,t,r,n)=>(0,C.CR)(e,"yAxis",r,n),N=(0,A.Mz)([k.fz,I,z,D,M],(e,t,r,n,i)=>(0,v._L)(e,"xAxis")?(0,v.Hj)(t,n,!1):(0,v.Hj)(r,i,!1));function T(e){return"line"===e.type}var G=(0,A.Mz)([C.ld,(e,t,r,n,i)=>i],(e,t)=>e.filter(T).find(e=>e.id===t)),B=(0,A.Mz)([k.fz,I,z,D,M,G,N,S.HS],(e,t,r,n,i,a,l,o)=>{var c,{chartData:s,dataStartIndex:u,dataEndIndex:f}=o;if(null!=a&&null!=t&&null!=r&&null!=n&&null!=i&&0!==n.length&&0!==i.length&&null!=l){var{dataKey:d,data:p}=a;if(null!=(c=null!=p&&p.length>0?p:null==s?void 0:s.slice(u,f+1)))return eo({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,dataKey:d,bandSize:l,displayedData:c})}}),R=r(68997),L=r(94913),W=r(85224),K=r(48971),F=r(3838),V=r(34140),H=["id"],J=["type","layout","connectNulls","needClip"],U=["activeDot","animateNewValues","animationBegin","animationDuration","animationEasing","connectNulls","dot","hide","isAnimationActive","label","legendType","xAxisId","yAxisId","id"];function $(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Z(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?$(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function _(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function X(){return(X=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function Q(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,fill:a,name:l,hide:o,unit:c}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,nameKey:void 0,name:(0,v.uM)(l,t),hide:o,type:e.tooltipType,color:e.stroke,unit:c}}}var q=(e,t)=>"".concat(t,"px ").concat(e-t,"px");function Y(e){var{clipPathId:t,points:r,props:a}=e,{dot:l,dataKey:c,needClip:f}=a;if(null==r||!l&&1!==r.length)return null;var{id:d}=a,y=_(a,H),v=(0,p.y$)(l),h=(0,o.u)(y),m=(0,p.J9)(l,!0),b=r.map((e,t)=>{var a,o=Z(Z(Z({key:"dot-".concat(t),r:3},h),m),{},{index:t,cx:e.x,cy:e.y,dataKey:c,value:e.value,payload:e.payload,points:r});if(n.isValidElement(l))a=n.cloneElement(l,o);else if("function"==typeof l)a=l(o);else{var u=(0,i.$)("recharts-line-dot","boolean"!=typeof l?l.className:"");a=n.createElement(s,X({},o,{className:u}))}return a}),g={clipPath:f?"url(#clipPath-".concat(v?"":"dots-").concat(t,")"):void 0};return n.createElement(u.W,X({className:"recharts-line-dots",key:"dots"},g),b)}function ee(e){var{clipPathId:t,pathRef:r,points:i,strokeDasharray:l,props:o,showLabels:c}=e,{type:s,layout:u,connectNulls:d,needClip:y}=o,v=_(o,J),h=Z(Z({},(0,p.J9)(v,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:y?"url(#clipPath-".concat(t,")"):void 0,points:i,type:s,layout:u,connectNulls:d,strokeDasharray:null!=l?l:o.strokeDasharray});return n.createElement(n.Fragment,null,(null==i?void 0:i.length)>1&&n.createElement(a.I,X({},h,{pathRef:r})),n.createElement(Y,{points:i,clipPathId:t,props:o}),c&&f.Z.renderCallByParent(o,i))}function et(e){var{clipPathId:t,props:r,pathRef:i,previousPointsRef:a,longestAnimatedLengthRef:l}=e,{points:o,strokeDasharray:c,isAnimationActive:s,animationBegin:u,animationDuration:f,animationEasing:p,animateNewValues:y,width:v,height:h,onAnimationEnd:m,onAnimationStart:b}=r,g=a.current,x=(0,L.n)(r,"recharts-line-"),[O,w]=(0,n.useState)(!1),P=(0,n.useCallback)(()=>{"function"==typeof m&&m(),w(!1)},[m]),E=(0,n.useCallback)(()=>{"function"==typeof b&&b(),w(!0)},[b]),k=function(e){try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(e){return 0}}(i.current),j=l.current;return n.createElement(V.J,{begin:u,duration:f,isActive:s,easing:p,onAnimationEnd:P,onAnimationStart:E,key:x},e=>{var s,u=Math.min((0,d.GW)(j,k+j,e),k);if(s=c?((e,t,r)=>{var n=r.reduce((e,t)=>e+t);if(!n)return q(t,e);for(var i=Math.floor(e/n),a=e%n,l=t-e,o=[],c=0,s=0;c<r.length;s+=r[c],++c)if(s+r[c]>a){o=[...r.slice(0,c),a-s];break}var u=o.length%2==0?[0,l]:[l];return[...function(e,t){for(var r=e.length%2!=0?[...e,0]:e,n=[],i=0;i<t;++i)n=[...n,...r];return n}(r,i),...o,...u].map(e=>"".concat(e,"px")).join(", ")})(u,k,"".concat(c).split(/[,\s]+/gim).map(e=>parseFloat(e))):q(k,u),g){var f=g.length/o.length,p=1===e?o:o.map((t,r)=>{var n=Math.floor(r*f);if(g[n]){var i=g[n];return Z(Z({},t),{},{x:(0,d.GW)(i.x,t.x,e),y:(0,d.GW)(i.y,t.y,e)})}return y?Z(Z({},t),{},{x:(0,d.GW)(2*v,t.x,e),y:(0,d.GW)(h/2,t.y,e)}):Z(Z({},t),{},{x:t.x,y:t.y})});return a.current=p,n.createElement(ee,{props:r,points:p,clipPathId:t,pathRef:i,showLabels:!O,strokeDasharray:s})}return e>0&&k>0&&(a.current=o,l.current=u),n.createElement(ee,{props:r,points:o,clipPathId:t,pathRef:i,showLabels:!O,strokeDasharray:s})})}function er(e){var{clipPathId:t,props:r}=e,{points:i,isAnimationActive:a}=r,l=(0,n.useRef)(null),o=(0,n.useRef)(0),c=(0,n.useRef)(null),s=l.current;return a&&i&&i.length&&s!==i?n.createElement(et,{props:r,clipPathId:t,previousPointsRef:l,longestAnimatedLengthRef:o,pathRef:c}):n.createElement(ee,{props:r,points:i,clipPathId:t,pathRef:c,showLabels:!0})}var en=(e,t)=>({x:e.x,y:e.y,value:e.value,errorVal:(0,v.kr)(e.payload,t)});class ei extends n.Component{render(){var e,{hide:t,dot:r,points:a,className:l,xAxisId:o,yAxisId:c,top:s,left:f,width:d,height:y,id:v,needClip:h}=this.props;if(t)return null;var m=(0,i.$)("recharts-line",l),{r:b=3,strokeWidth:g=2}=null!=(e=(0,p.J9)(r,!1))?e:{r:3,strokeWidth:2},x=(0,p.y$)(r),w=2*b+g;return n.createElement(n.Fragment,null,n.createElement(u.W,{className:m},h&&n.createElement("defs",null,n.createElement(E.Q,{clipPathId:v,xAxisId:o,yAxisId:c}),!x&&n.createElement("clipPath",{id:"clipPath-dots-".concat(v)},n.createElement("rect",{x:f-w/2,y:s-w/2,width:d+w,height:y+w}))),n.createElement(er,{props:this.props,clipPathId:v}),n.createElement(P.zk,{xAxisId:o,yAxisId:c,data:a,dataPointFormatter:en,errorBarOffset:0},this.props.children)),n.createElement(O,{activeDot:this.props.activeDot,points:a,mainColor:this.props.stroke,itemDataKey:this.props.dataKey}))}}var ea={activeDot:!0,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!0,fill:"#fff",hide:!1,isAnimationActive:!y.m.isSsr,label:!1,legendType:"line",stroke:"#3182bd",strokeWidth:1,xAxisId:0,yAxisId:0};function el(e){var t=(0,W.e)(e,ea),{activeDot:r,animateNewValues:i,animationBegin:a,animationDuration:l,animationEasing:o,connectNulls:c,dot:s,hide:u,isAnimationActive:f,label:d,legendType:p,xAxisId:y,yAxisId:v,id:m}=t,g=_(t,U),{needClip:x}=(0,E.l)(y,v),O=(0,b.oM)(),w=(0,k.WX)(),P=(0,j.r)(),A=(0,h.G)(e=>B(e,y,v,P,m));if("horizontal"!==w&&"vertical"!==w||null==A||null==O)return null;var{height:S,width:C,x:I,y:D}=O;return n.createElement(ei,X({},g,{id:m,connectNulls:c,dot:s,activeDot:r,animateNewValues:i,animationBegin:a,animationDuration:l,animationEasing:o,isAnimationActive:f,hide:u,label:d,legendType:p,xAxisId:y,yAxisId:v,points:A,layout:w,height:S,width:C,left:I,top:D,needClip:x}))}function eo(e){var{layout:t,xAxis:r,yAxis:n,xAxisTicks:i,yAxisTicks:a,dataKey:l,bandSize:o,displayedData:c}=e;return c.map((e,c)=>{var s=(0,v.kr)(e,l);if("horizontal"===t)return{x:(0,v.nb)({axis:r,ticks:i,bandSize:o,entry:e,index:c}),y:(0,d.uy)(s)?null:n.scale(s),value:s,payload:e};var u=(0,d.uy)(s)?null:r.scale(s),f=(0,v.nb)({axis:n,ticks:a,bandSize:o,entry:e,index:c});return null==u||null==f?null:{x:u,y:f,value:s,payload:e}}).filter(Boolean)}function ec(e){var t=(0,W.e)(e,ea),r=(0,j.r)();return n.createElement(K.x,{id:t.id,type:"line"},e=>n.createElement(n.Fragment,null,n.createElement(R.A,{legendPayload:(e=>{var{dataKey:t,name:r,stroke:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:(0,v.uM)(r,t),payload:e}]})(t)}),n.createElement(w.r,{fn:Q,args:t}),n.createElement(F.p,{type:"line",id:e,data:t.data,xAxisId:t.xAxisId,yAxisId:t.yAxisId,zAxisId:0,dataKey:t.dataKey,hide:t.hide,isPanorama:r}),n.createElement(el,X({},t,{id:e}))))}ec.displayName="Line"},91761:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},92673:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toArray=function(e){return Array.isArray(e)?e:Array.from(e)}},98128:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var n=r(12115),i=r(33308),a=r(1953),l=["axis","item"],o=(0,n.forwardRef)((e,t)=>n.createElement(a.P,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:l,tooltipPayloadSearcher:i.uN,categoricalChartProps:e,ref:t}))}}]);