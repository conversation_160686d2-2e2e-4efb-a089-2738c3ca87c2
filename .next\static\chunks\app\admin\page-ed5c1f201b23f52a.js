(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{3998:(e,t,s)=>{"use strict";s.d(t,{$:()=>c});var a=s(95155),l=s(12115),r=s(83101),n=s(64269);let i=(0,r.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-[#1814F3] text-white shadow hover:bg-[#1814F3]/90",destructive:"bg-red-500 text-white shadow-sm hover:bg-red-500/90",outline:"border border-gray-200 bg-white shadow-sm hover:bg-gray-50 hover:text-gray-900",secondary:"bg-gray-100 text-gray-900 shadow-sm hover:bg-gray-100/80",ghost:"hover:bg-gray-100 hover:text-gray-900",link:"text-[#1814F3] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=l.forwardRef((e,t)=>{let{className:s,variant:l,size:r,asChild:c=!1,...d}=e;return(0,a.jsx)("button",{className:(0,n.cn)(i({variant:l,size:r,className:s})),ref:t,...d})});c.displayName="Button"},37846:(e,t,s)=>{"use strict";s.d(t,{k:()=>o});var a=s(95155),l=s(26991),r=s(12723),n=s(4035),i=s(69386),c=s(11345);let d=["#1814F3","#16DBCC","#FF82AC","#FFBB38"];function o(e){let{data:t,width:s=400,height:o=300,showLegend:x=!0}=e;return(0,a.jsx)(l.u,{width:"100%",height:o,children:(0,a.jsxs)(r.r,{children:[(0,a.jsx)(n.F,{data:t,cx:"50%",cy:"50%",innerRadius:60,outerRadius:100,paddingAngle:5,dataKey:"value",children:t.map((e,t)=>(0,a.jsx)(i.f,{fill:e.color||d[t%d.length]},"cell-".concat(t)))}),x&&(0,a.jsx)(c.s,{verticalAlign:"bottom",height:36,formatter:(e,t)=>(0,a.jsx)("span",{style:{color:t.color},children:e})})]})})}},47845:(e,t,s)=>{"use strict";s.d(t,{Y:()=>o});var a=s(95155),l=s(65142),r=s(3998),n=s(86651),i=s(85998),c=s(15870),d=s(15239);function o(e){let{title:t,userAvatar:s,userName:o}=e;return(0,a.jsx)("header",{className:"bg-white border-b border-gray-200 px-4 md:px-8 py-4 md:py-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-xl md:text-2xl font-semibold text-[#343C6A]",children:t}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-6",children:[(0,a.jsxs)("div",{className:"relative hidden md:block",children:[(0,a.jsx)(n.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)(l.p,{placeholder:"Search for something",className:"pl-10 w-60 lg:w-80 bg-[#F5F7FA] border-none"})]}),(0,a.jsxs)(r.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,a.jsx)(i.A,{className:"h-5 w-5 text-[#718EBF]"}),(0,a.jsx)("span",{className:"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"})]}),(0,a.jsx)(r.$,{variant:"ghost",size:"icon",children:(0,a.jsx)(c.A,{className:"h-5 w-5 text-[#718EBF]"})}),(0,a.jsx)("div",{className:"flex items-center space-x-3",children:(0,a.jsx)("div",{className:"w-10 h-10 rounded-full overflow-hidden bg-gray-200",children:s?(0,a.jsx)(d.default,{src:s,alt:o||"User",width:40,height:40,className:"w-full h-full object-cover"}):(0,a.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center text-white font-semibold",children:(null==o?void 0:o.charAt(0))||"U"})})})]})]})})}},53931:(e,t,s)=>{Promise.resolve().then(s.bind(s,79652))},64269:(e,t,s)=>{"use strict";s.d(t,{Yq:()=>i,cn:()=>r,sL:()=>c,vv:()=>n});var a=s(2821),l=s(75889);function r(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,l.QP)((0,a.$)(t))}function n(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e)}function i(e){return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric"}).format(new Date(e))}function c(e){return new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric"}).format(new Date(e))}},65142:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var a=s(95155),l=s(12115),r=s(64269);let n=l.forwardRef((e,t)=>{let{className:s,type:l,...n}=e;return(0,a.jsx)("input",{type:l,className:(0,r.cn)("flex h-9 w-full rounded-md border border-gray-200 bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-[#1814F3] disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...n})});n.displayName="Input"},79652:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>Y});var a=s(95155),l=s(47845),r=s(86948),n=s(1524),i=s(26921),c=s(91169),d=s(50906),o=s(91761),x=s(78519),m=s(64269);function h(e){let{title:t,value:s,change:l,icon:c,color:d="blue"}=e,o=l&&l>0;return(0,a.jsx)(r.Zp,{className:"bg-white rounded-[25px] border-none shadow-lg",children:(0,a.jsx)(r.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,a.jsx)("span",{className:"text-sm text-[#718EBF] font-medium",children:t}),(0,a.jsx)("span",{className:"text-2xl font-bold text-[#232323]",children:"number"==typeof s?(0,m.vv)(s):s}),l&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[o?(0,a.jsx)(n.A,{className:"h-4 w-4 text-green-500"}):(0,a.jsx)(i.A,{className:"h-4 w-4 text-red-500"}),(0,a.jsxs)("span",{className:"text-sm font-medium ".concat(o?"text-green-500":"text-red-500"),children:[Math.abs(l),"%"]})]})]}),(0,a.jsx)("div",{className:"p-4 rounded-full ".concat({blue:"bg-blue-100 text-blue-600",green:"bg-green-100 text-green-600",orange:"bg-orange-100 text-orange-600",purple:"bg-purple-100 text-purple-600"}[d]),children:c})]})})})}function u(e){let{totalUsers:t,totalTransactions:s,totalRevenue:l,activeAgents:r}=e;return(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)(h,{title:"Total Users",value:t.toLocaleString(),change:12.5,icon:(0,a.jsx)(c.A,{className:"h-6 w-6"}),color:"blue"}),(0,a.jsx)(h,{title:"Total Transactions",value:s.toLocaleString(),change:8.2,icon:(0,a.jsx)(d.A,{className:"h-6 w-6"}),color:"green"}),(0,a.jsx)(h,{title:"Total Revenue",value:l,change:-2.1,icon:(0,a.jsx)(o.A,{className:"h-6 w-6"}),color:"orange"}),(0,a.jsx)(h,{title:"Active Agents",value:r.toLocaleString(),change:5.7,icon:(0,a.jsx)(x.A,{className:"h-6 w-6"}),color:"purple"})]})}var p=s(26991),f=s(98128),g=s(68425),j=s(47734),b=s(74595),N=s(11345),v=s(58155);function y(e){let{data:t,height:s=300}=e;return(0,a.jsx)(p.u,{width:"100%",height:s,children:(0,a.jsxs)(f.E,{data:t,margin:{top:20,right:30,left:20,bottom:5},barCategoryGap:"20%",children:[(0,a.jsx)(g.d,{strokeDasharray:"3 3",stroke:"#E6EFF5"}),(0,a.jsx)(j.W,{dataKey:"day",axisLine:!1,tickLine:!1,tick:{fontSize:12,fill:"#718EBF"}}),(0,a.jsx)(b.h,{axisLine:!1,tickLine:!1,tick:{fontSize:12,fill:"#718EBF"}}),(0,a.jsx)(N.s,{wrapperStyle:{paddingTop:"20px"},iconType:"circle"}),(0,a.jsx)(v.y,{dataKey:"cashIn",fill:"#1814F3",name:"Cash In",radius:[30,30,30,30],barSize:15}),(0,a.jsx)(v.y,{dataKey:"cashOut",fill:"#16DBCC",name:"Cash Out",radius:[30,30,30,30],barSize:15})]})})}function w(e){let{data:t}=e;return(0,a.jsxs)(r.Zp,{className:"bg-white rounded-[25px] border-none shadow-lg",children:[(0,a.jsx)(r.aR,{children:(0,a.jsx)(r.ZB,{className:"text-[#343C6A] text-xl font-semibold",children:"Weekly Activity"})}),(0,a.jsx)(r.Wu,{children:(0,a.jsx)(y,{data:t,height:250})})]})}var F=s(37846);function A(e){let{data:t}=e,s=t.map(e=>({name:e.label,value:e.value,color:e.color}));return(0,a.jsxs)(r.Zp,{className:"bg-white rounded-[25px] border-none shadow-lg",children:[(0,a.jsx)(r.aR,{children:(0,a.jsx)(r.ZB,{className:"text-[#343C6A] text-xl font-semibold",children:"Expense Statistics"})}),(0,a.jsx)(r.Wu,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(F.k,{data:s,height:200,showLegend:!1})}),(0,a.jsx)("div",{className:"flex flex-col space-y-4 ml-8",children:t.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color}}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-white",children:[e.percentage,"%"]}),(0,a.jsx)("span",{className:"text-xs text-white/80",children:e.label})]})]},t))})]})})]})}var B=s(3998),C=s(65142),k=s(89559),E=s(93341),S=s(17215),T=s(18085),R=s(12115);function D(){let[e,t]=(0,R.useState)("525.50"),s=[{id:"refund",label:"Refund transaction",icon:k.A,color:"bg-orange-100 text-orange-600"},{id:"fund-agent",label:"Fund agent",icon:E.A,color:"bg-blue-100 text-blue-600"},{id:"settle-merchant",label:"Settle Merchant",icon:S.A,color:"bg-green-100 text-green-600"}];return(0,a.jsxs)(r.Zp,{className:"bg-white rounded-[25px] border-none shadow-lg",children:[(0,a.jsx)(r.aR,{children:(0,a.jsx)(r.ZB,{className:"text-[#343C6A] text-xl font-semibold",children:"Quick Transfer"})}),(0,a.jsxs)(r.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"flex -space-x-2",children:[1,2,3].map(e=>(0,a.jsxs)("div",{className:"w-12 h-12 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 border-2 border-white flex items-center justify-center text-white font-semibold",children:["U",e]},e))}),(0,a.jsx)(B.$,{variant:"outline",size:"icon",className:"rounded-full",children:(0,a.jsx)(T.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-[#718EBF]",children:"Write Amount"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"flex-1 relative",children:(0,a.jsx)(C.p,{value:e,onChange:e=>t(e.target.value),className:"bg-[#EDF1F7] border-none text-lg font-medium h-12"})}),(0,a.jsxs)(B.$,{className:"bg-[#1814F3] hover:bg-[#1814F3]/90 h-12 px-6",children:[(0,a.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"Send"]})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-3 gap-4",children:s.map(e=>{let t=e.icon;return(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("div",{className:"w-16 h-16 rounded-full ".concat(e.color," flex items-center justify-center mx-auto"),children:(0,a.jsx)(t,{className:"h-6 w-6"})}),(0,a.jsx)("span",{className:"text-xs text-[#718EBF] font-medium",children:e.label})]},e.id)})})]})]})}var W=s(80019),z=s(89585);function I(e){let{data:t,height:s=300,color:l="#1814F3"}=e;return(0,a.jsx)(p.u,{width:"100%",height:s,children:(0,a.jsxs)(W.b,{data:t,margin:{top:20,right:30,left:20,bottom:5},children:[(0,a.jsx)(g.d,{strokeDasharray:"3 3",stroke:"#E6EFF5"}),(0,a.jsx)(j.W,{dataKey:"month",axisLine:!1,tickLine:!1,tick:{fontSize:12,fill:"#718EBF"}}),(0,a.jsx)(b.h,{axisLine:!1,tickLine:!1,tick:{fontSize:12,fill:"#718EBF"}}),(0,a.jsx)(z.N,{type:"monotone",dataKey:"value",stroke:l,strokeWidth:3,dot:{fill:l,strokeWidth:2,r:4},activeDot:{r:6,stroke:l,strokeWidth:2}})]})})}function L(e){let{data:t}=e;return(0,a.jsxs)(r.Zp,{className:"bg-white rounded-[25px] border-none shadow-lg",children:[(0,a.jsx)(r.aR,{children:(0,a.jsx)(r.ZB,{className:"text-[#343C6A] text-xl font-semibold",children:"Total transaction over time"})}),(0,a.jsx)(r.Wu,{children:(0,a.jsx)(I,{data:t,height:250,color:"#1814F3"})})]})}var Z=s(39867),_=s(57828),O=s(37772);function U(e){let{transactions:t}=e;return(0,a.jsxs)(r.Zp,{className:"bg-white rounded-[25px] border-none shadow-lg",children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between",children:[(0,a.jsx)(r.ZB,{className:"text-[#343C6A] text-xl font-semibold",children:"Recent Transaction"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(B.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(Z.A,{className:"h-4 w-4 mr-2"}),"Export"]}),(0,a.jsxs)(B.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"View All"]})]})]}),(0,a.jsx)(r.Wu,{children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-[#718EBF] text-sm",children:"ID"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-[#718EBF] text-sm",children:"Description"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-[#718EBF] text-sm",children:"Type"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-[#718EBF] text-sm",children:"Amount"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-[#718EBF] text-sm",children:"Date"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-[#718EBF] text-sm",children:"Status"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-[#718EBF] text-sm",children:"Action"})]})}),(0,a.jsx)("tbody",{children:t.map((e,t)=>(0,a.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,a.jsxs)("td",{className:"py-4 px-4 text-sm text-[#232323]",children:["#",e.id.slice(0,8)]}),(0,a.jsx)("td",{className:"py-4 px-4 text-sm text-[#232323] font-medium",children:e.description}),(0,a.jsx)("td",{className:"py-4 px-4 text-sm text-[#718EBF] capitalize",children:e.type.replace("_"," ")}),(0,a.jsx)("td",{className:"py-4 px-4 text-sm font-medium",children:(0,a.jsxs)("span",{className:"cash_out"===e.type||"loan"===e.type?"text-red-600":"text-green-600",children:["cash_out"===e.type||"loan"===e.type?"-":"+",(0,m.vv)(e.amount)]})}),(0,a.jsx)("td",{className:"py-4 px-4 text-sm text-[#718EBF]",children:(0,m.Yq)(e.date)}),(0,a.jsx)("td",{className:"py-4 px-4",children:(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(function(e){switch(e){case"completed":return"text-green-600 bg-green-100";case"pending":return"text-yellow-600 bg-yellow-100";case"failed":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}}(e.status)),children:e.status})}),(0,a.jsx)("td",{className:"py-4 px-4",children:(0,a.jsx)(B.$,{variant:"ghost",size:"icon",children:(0,a.jsx)(O.A,{className:"h-4 w-4"})})})]},e.id))})]})})})]})}let $=[{day:"Sat",cashIn:480,cashOut:320},{day:"Sun",cashIn:350,cashOut:280},{day:"Mon",cashIn:400,cashOut:300},{day:"Tue",cashIn:300,cashOut:200},{day:"Wed",cashIn:450,cashOut:350},{day:"Thu",cashIn:380,cashOut:280},{day:"Fri",cashIn:420,cashOut:320}],K=[{label:"User Wallets",value:30,percentage:30,color:"#1814F3"},{label:"Agent Float",value:20,percentage:20,color:"#16DBCC"},{label:"Merchant Payouts",value:15,percentage:15,color:"#FF82AC"},{label:"Commission",value:35,percentage:35,color:"#FFBB38"}],M=[{month:"Jul",value:200},{month:"Aug",value:300},{month:"Sep",value:250},{month:"Oct",value:400},{month:"Nov",value:350},{month:"Dec",value:450},{month:"Jan",value:500}],X=[{id:"TXN001",type:"cash_in",amount:1250,description:"Agent Float Top-up",date:"2024-01-15",status:"completed"},{id:"TXN002",type:"cash_out",amount:850,description:"Merchant Settlement",date:"2024-01-14",status:"completed"},{id:"TXN003",type:"transfer",amount:2500,description:"Commission Transfer",date:"2024-01-13",status:"pending"},{id:"TXN004",type:"cash_in",amount:3200,description:"User Wallet Funding",date:"2024-01-12",status:"completed"},{id:"TXN005",type:"cash_out",amount:1800,description:"Agent Withdrawal",date:"2024-01-11",status:"failed"}];function Y(){return(0,a.jsxs)("div",{className:"min-h-screen bg-[#F5F7FA]",children:[(0,a.jsx)(l.Y,{title:"Admin Dashboard"}),(0,a.jsxs)("main",{className:"p-4 md:p-8",children:[(0,a.jsx)(u,{totalUsers:12450,totalTransactions:8920,totalRevenue:245e4,activeAgents:156}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-12 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-8",children:(0,a.jsx)(w,{data:$})}),(0,a.jsx)("div",{className:"lg:col-span-4",children:(0,a.jsx)(A,{data:K})}),(0,a.jsx)("div",{className:"lg:col-span-4 order-3 lg:order-none",children:(0,a.jsx)(D,{})}),(0,a.jsx)("div",{className:"lg:col-span-8 order-2 lg:order-none",children:(0,a.jsx)(L,{data:M})}),(0,a.jsx)("div",{className:"lg:col-span-12 order-4",children:(0,a.jsx)(U,{transactions:X})})]})]})]})}},86948:(e,t,s)=>{"use strict";s.d(t,{Wu:()=>d,ZB:()=>c,Zp:()=>n,aR:()=>i});var a=s(95155),l=s(12115),r=s(64269);let n=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("rounded-xl border bg-white text-gray-950 shadow",s),...l})});n.displayName="Card";let i=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",s),...l})});i.displayName="CardHeader";let c=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("h3",{ref:t,className:(0,r.cn)("font-semibold leading-none tracking-tight",s),...l})});c.displayName="CardTitle",l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("p",{ref:t,className:(0,r.cn)("text-sm text-gray-500",s),...l})}).displayName="CardDescription";let d=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("p-6 pt-0",s),...l})});d.displayName="CardContent",l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("flex items-center p-6 pt-0",s),...l})}).displayName="CardFooter"}},e=>{e.O(0,[618,306,441,255,358],()=>e(e.s=53931)),_N_E=e.O()}]);