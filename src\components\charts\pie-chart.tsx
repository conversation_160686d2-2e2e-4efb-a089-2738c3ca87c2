"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from 'recharts'
import { ChartData } from '@/types'

interface PieChartComponentProps {
  data: ChartData[]
  width?: number
  height?: number
  showLegend?: boolean
}

const COLORS = ['#1814F3', '#16DBCC', '#FF82AC', '#FFBB38']

export function PieChartComponent({ 
  data, 
  width = 400, 
  height = 300, 
  showLegend = true 
}: PieChartComponentProps) {
  return (
    <ResponsiveContainer width="100%" height={height}>
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          innerRadius={60}
          outerRadius={100}
          paddingAngle={5}
          dataKey="value"
        >
          {data.map((entry, index) => (
            <Cell 
              key={`cell-${index}`} 
              fill={entry.color || COLORS[index % COLORS.length]} 
            />
          ))}
        </Pie>
        {showLegend && (
          <Legend 
            verticalAlign="bottom" 
            height={36}
            formatter={(value, entry) => (
              <span style={{ color: entry.color }}>{value}</span>
            )}
          />
        )}
      </PieChart>
    </ResponsiveContainer>
  )
}

interface CustomLegendProps {
  data: ChartData[]
}

export function CustomPieLegend({ data }: CustomLegendProps) {
  return (
    <div className="flex flex-col space-y-2">
      {data.map((item, index) => (
        <div key={index} className="flex items-center space-x-2">
          <div 
            className="w-3 h-3 rounded-full" 
            style={{ backgroundColor: item.color || COLORS[index % COLORS.length] }}
          />
          <span className="text-sm text-white">{item.name}</span>
        </div>
      ))}
    </div>
  )
}
