"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-up.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/arrow-down-up.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ArrowDownUp)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.542.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m3 16 4 4 4-4\",\n            key: \"1co6wj\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M7 20V4\",\n            key: \"1yoxec\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m21 8-4-4-4 4\",\n            key: \"1c9v7m\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M17 4v16\",\n            key: \"7dpous\"\n        }\n    ]\n];\nconst ArrowDownUp = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"arrow-down-up\", __iconNode);\n //# sourceMappingURL=arrow-down-up.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-up.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/dollar-sign.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ DollarSign)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.542.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"2\",\n            y2: \"22\",\n            key: \"7eqyqh\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\",\n            key: \"1b0p4s\"\n        }\n    ]\n];\nconst DollarSign = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"dollar-sign\", __iconNode);\n //# sourceMappingURL=dollar-sign.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/sidebar.tsx":
/*!**********************************************!*\
  !*** ./src/components/dashboard/sidebar.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,CreditCard,DollarSign,Home,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,CreditCard,DollarSign,Home,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,CreditCard,DollarSign,Home,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,CreditCard,DollarSign,Home,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,CreditCard,DollarSign,Home,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,CreditCard,DollarSign,Home,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,CreditCard,DollarSign,Home,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\nconst menuItems = [\n    {\n        id: 'dashboard',\n        label: 'Dashboard',\n        icon: _barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        href: '/dashboard'\n    },\n    {\n        id: 'transfer',\n        label: 'Transfer',\n        icon: _barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        href: '/dashboard/transfer',\n        subItems: [\n            {\n                id: 'withdraw',\n                label: 'Withdraw',\n                icon: _barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                href: '/dashboard/withdraw'\n            },\n            {\n                id: 'add-funds',\n                label: 'Add funds',\n                icon: _barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                href: '/dashboard/add-funds'\n            },\n            {\n                id: 'bills',\n                label: 'Bills',\n                icon: _barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                href: '/dashboard/bills'\n            },\n            {\n                id: 'loans',\n                label: 'Loans',\n                icon: _barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                href: '/dashboard/loans'\n            }\n        ]\n    },\n    {\n        id: 'settings',\n        label: 'Setting',\n        icon: _barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        href: '/dashboard/settings'\n    }\n];\nfunction Sidebar(param) {\n    let { activeItem = 'dashboard', onItemClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"w-64 bg-white border-r border-gray-200 h-screen flex flex-col hidden lg:flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-[#343C6A]\",\n                    children: \"AeTrust\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"space-y-2\",\n                    children: menuItems.map((item)=>{\n                        const Icon = item.icon;\n                        const isActive = activeItem === item.id;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"w-full justify-start h-12 px-4 text-left font-medium transition-all\", isActive ? \"bg-[#1814F3] text-white hover:bg-[#1814F3]/90\" : \"text-[#718EBF] hover:bg-gray-50 hover:text-[#343C6A]\"),\n                                onClick: ()=>onItemClick === null || onItemClick === void 0 ? void 0 : onItemClick(item.id),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"mr-3 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 19\n                                    }, this),\n                                    item.label,\n                                    isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-0 top-0 bottom-0 w-1 bg-[#1814F3] rounded-l-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 17\n                            }, this)\n                        }, item.id, false, {\n                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"ghost\",\n                        className: \"w-full justify-start h-12 px-4 text-[#718EBF] hover:bg-gray-50 hover:text-[#343C6A]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HelpCircle, {\n                                className: \"mr-3 h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this),\n                            \"Help & Support\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"ghost\",\n                        className: \"w-full justify-start h-12 px-4 text-[#718EBF] hover:bg-gray-50 hover:text-red-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogOut, {\n                                className: \"mr-3 h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this),\n                            \"Logout\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/sidebar.tsx\n"));

/***/ })

});