"use client"

import { useState } from "react"
import { Head<PERSON> } from "@/components/dashboard/header"
import { Sidebar } from "@/components/dashboard/sidebar"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { 
  CheckCircle, 
  Download, 
  Share, 
  Copy, 
  ArrowLeft,
  Calendar,
  Clock,
  User,
  CreditCard
} from "lucide-react"

interface ReceiptData {
  transactionId: string
  amount: number
  recipient: string
  recipientAccount: string
  date: string
  time: string
  status: 'success' | 'pending' | 'failed'
  type: 'transfer' | 'loan' | 'payment'
  fee: number
  reference: string
}

export default function ReceiptPage() {
  const [activeMenuItem, setActiveMenuItem] = useState("transfer")
  
  // Mock receipt data - in real app this would come from URL params or API
  const receiptData: ReceiptData = {
    transactionId: "TXN123456789",
    amount: 3000,
    recipient: "John Doe",
    recipientAccount: "+234 801 234 5678",
    date: "2024-01-15",
    time: "14:30:25",
    status: 'success',
    type: 'transfer',
    fee: 0.50,
    reference: "REF-2024-001-789"
  }

  const handleDownloadReceipt = () => {
    // Download receipt as PDF
    console.log("Downloading receipt...")
  }

  const handleShareReceipt = () => {
    // Share receipt
    console.log("Sharing receipt...")
  }

  const copyTransactionId = () => {
    navigator.clipboard.writeText(receiptData.transactionId)
    // Show toast notification
  }

  return (
    <div className="flex h-screen bg-[#F5F7FA]">
      <Sidebar activeItem={activeMenuItem} onItemClick={setActiveMenuItem} />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header 
          title="Transaction Receipt" 
          userName="James Bond"
        />
        
        <main className="flex-1 overflow-auto p-4 md:p-8">
          <div className="max-w-2xl mx-auto">
            {/* Back Button */}
            <Button 
              variant="ghost" 
              className="mb-6 text-[#343C6A] hover:bg-gray-100"
              onClick={() => window.history.back()}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>

            {/* Receipt Card */}
            <Card className="bg-white shadow-lg">
              <CardContent className="p-8">
                {/* Success Status */}
                <div className="text-center mb-8">
                  <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="h-12 w-12 text-green-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-[#333B69] mb-2">Transaction Successful!</h2>
                  <p className="text-[#6E6E6E]">Your {receiptData.type} has been completed successfully</p>
                </div>

                {/* Amount */}
                <div className="text-center mb-8">
                  <p className="text-[#6E6E6E] mb-2">Amount Transferred</p>
                  <p className="text-4xl font-bold text-[#333B69]">${receiptData.amount.toLocaleString()}</p>
                </div>

                {/* Transaction Details */}
                <div className="space-y-4 mb-8">
                  <div className="flex justify-between items-center py-3 border-b border-gray-100">
                    <span className="text-[#6E6E6E]">Transaction ID</span>
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-[#333B69]">{receiptData.transactionId}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={copyTransactionId}
                        className="p-1 h-auto"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="flex justify-between items-center py-3 border-b border-gray-100">
                    <span className="text-[#6E6E6E]">Recipient</span>
                    <span className="font-medium text-[#333B69]">{receiptData.recipient}</span>
                  </div>

                  <div className="flex justify-between items-center py-3 border-b border-gray-100">
                    <span className="text-[#6E6E6E]">Recipient Account</span>
                    <span className="font-medium text-[#333B69]">{receiptData.recipientAccount}</span>
                  </div>

                  <div className="flex justify-between items-center py-3 border-b border-gray-100">
                    <span className="text-[#6E6E6E]">Date & Time</span>
                    <div className="text-right">
                      <div className="font-medium text-[#333B69]">{receiptData.date}</div>
                      <div className="text-sm text-[#6E6E6E]">{receiptData.time}</div>
                    </div>
                  </div>

                  <div className="flex justify-between items-center py-3 border-b border-gray-100">
                    <span className="text-[#6E6E6E]">Transaction Fee</span>
                    <span className="font-medium text-[#333B69]">${receiptData.fee.toFixed(2)}</span>
                  </div>

                  <div className="flex justify-between items-center py-3 border-b border-gray-100">
                    <span className="text-[#6E6E6E]">Reference Number</span>
                    <span className="font-medium text-[#333B69]">{receiptData.reference}</span>
                  </div>

                  <div className="flex justify-between items-center py-3">
                    <span className="text-[#6E6E6E]">Status</span>
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Completed
                    </span>
                  </div>
                </div>

                {/* Total Amount */}
                <div className="bg-[#F5F7FA] rounded-lg p-4 mb-8">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold text-[#333B69]">Total Debited</span>
                    <span className="text-lg font-bold text-[#333B69]">
                      ${(receiptData.amount + receiptData.fee).toFixed(2)}
                    </span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button 
                    variant="outline" 
                    className="h-12 border-[#059AD1] text-[#059AD1] hover:bg-[#059AD1] hover:text-white"
                    onClick={handleDownloadReceipt}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download Receipt
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="h-12 border-[#059AD1] text-[#059AD1] hover:bg-[#059AD1] hover:text-white"
                    onClick={handleShareReceipt}
                  >
                    <Share className="h-4 w-4 mr-2" />
                    Share Receipt
                  </Button>
                </div>

                {/* Footer Note */}
                <div className="mt-8 text-center">
                  <p className="text-sm text-[#6E6E6E]">
                    Keep this receipt for your records. If you have any questions about this transaction,
                    please contact our support team.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button 
                className="bg-[#059AD1] hover:bg-[#059AD1]/90 text-white h-12"
                onClick={() => window.location.href = '/dashboard/transfer'}
              >
                Make Another Transfer
              </Button>
              
              <Button 
                variant="outline" 
                className="h-12"
                onClick={() => window.location.href = '/dashboard'}
              >
                Back to Dashboard
              </Button>
              
              <Button 
                variant="outline" 
                className="h-12"
                onClick={() => window.location.href = '/dashboard/transactions'}
              >
                View All Transactions
              </Button>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
