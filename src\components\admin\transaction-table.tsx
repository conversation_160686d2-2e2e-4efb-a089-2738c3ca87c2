"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { MoreHorizontal, Download, Eye } from "lucide-react"
import { formatCurrency, formatDate } from "@/lib/utils"
import { Transaction } from "@/types"

interface TransactionTableProps {
  transactions: Transaction[]
}

function getStatusColor(status: Transaction['status']) {
  switch (status) {
    case 'completed':
      return 'text-green-600 bg-green-100'
    case 'pending':
      return 'text-yellow-600 bg-yellow-100'
    case 'failed':
      return 'text-red-600 bg-red-100'
    default:
      return 'text-gray-600 bg-gray-100'
  }
}

export function TransactionTable({ transactions }: TransactionTableProps) {
  return (
    <Card className="bg-white rounded-[25px] border-none shadow-lg">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-[#343C6A] text-xl font-semibold">Recent Transaction</CardTitle>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Eye className="h-4 w-4 mr-2" />
            View All
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-[#718EBF] text-sm">ID</th>
                <th className="text-left py-3 px-4 font-medium text-[#718EBF] text-sm">Description</th>
                <th className="text-left py-3 px-4 font-medium text-[#718EBF] text-sm">Type</th>
                <th className="text-left py-3 px-4 font-medium text-[#718EBF] text-sm">Amount</th>
                <th className="text-left py-3 px-4 font-medium text-[#718EBF] text-sm">Date</th>
                <th className="text-left py-3 px-4 font-medium text-[#718EBF] text-sm">Status</th>
                <th className="text-left py-3 px-4 font-medium text-[#718EBF] text-sm">Action</th>
              </tr>
            </thead>
            <tbody>
              {transactions.map((transaction, index) => (
                <tr key={transaction.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-4 px-4 text-sm text-[#232323]">
                    #{transaction.id.slice(0, 8)}
                  </td>
                  <td className="py-4 px-4 text-sm text-[#232323] font-medium">
                    {transaction.description}
                  </td>
                  <td className="py-4 px-4 text-sm text-[#718EBF] capitalize">
                    {transaction.type.replace('_', ' ')}
                  </td>
                  <td className="py-4 px-4 text-sm font-medium">
                    <span className={transaction.type === 'cash_out' || transaction.type === 'loan' ? 'text-red-600' : 'text-green-600'}>
                      {transaction.type === 'cash_out' || transaction.type === 'loan' ? '-' : '+'}
                      {formatCurrency(transaction.amount)}
                    </span>
                  </td>
                  <td className="py-4 px-4 text-sm text-[#718EBF]">
                    {formatDate(transaction.date)}
                  </td>
                  <td className="py-4 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
                      {transaction.status}
                    </span>
                  </td>
                  <td className="py-4 px-4">
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  )
}
