# jsx-a11y/html-has-lang

💼 This rule is enabled in the following configs: ☑️ `recommended`, 🔒 `strict`.

<!-- end auto-generated rule header -->

<html> elements must have the lang prop. This rule is largely superseded by the [`lang` rule](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/lang.md).

## Rule details

This rule takes no arguments.

### Succeed
```jsx
<html lang="en">
<html lang="en-US">
<html lang={language}>
```

### Fail

```jsx
<html>
```

## Accessibility guidelines
- [WCAG 3.1.1](https://www.w3.org/WAI/WCAG21/Understanding/language-of-page)

### Resources
- [axe-core, html-has-lang](https://dequeuniversity.com/rules/axe/3.2/html-has-lang)
- [axe-core, html-lang-valid](https://dequeuniversity.com/rules/axe/3.2/html-lang-valid)
