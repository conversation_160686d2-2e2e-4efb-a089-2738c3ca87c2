"use client"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Home,
  ArrowUpDown,
  ArrowDownUp,
  Plus,
  CreditCard,
  DollarSign,
  Settings,
  ChevronDown,
  ChevronRight
} from "lucide-react"
import { useState } from "react"

interface SidebarProps {
  activeItem?: string
  onItemClick?: (item: string) => void
}

const menuItems = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: Home,
    href: '/dashboard'
  },
  {
    id: 'transfer',
    label: 'Transfer',
    icon: ArrowUpDown,
    href: '/dashboard/transfer',
    subItems: [
      { id: 'withdraw', label: 'Withdraw', icon: ArrowDownUp, href: '/dashboard/withdraw' },
      { id: 'add-funds', label: 'Add funds', icon: Plus, href: '/dashboard/add-funds' },
      { id: 'bills', label: 'Bills', icon: CreditCard, href: '/dashboard/bills' },
      { id: 'loans', label: 'Loans', icon: DollarSign, href: '/dashboard/loans' },
    ]
  },
  {
    id: 'settings',
    label: 'Setting',
    icon: Settings,
    href: '/dashboard/settings'
  },
]

export function Sidebar({ activeItem = 'dashboard', onItemClick }: SidebarProps) {
  const [expandedItems, setExpandedItems] = useState<string[]>(['transfer'])

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    )
  }

  return (
    <aside className="w-64 bg-white border-r border-[#E6EFF5] h-screen flex flex-col hidden lg:flex">
      {/* Logo */}
      <div className="p-6 border-b border-[#E6EFF5]">
        <h2 className="text-2xl font-bold text-[#343C6A]">AeTrust</h2>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon
            const isActive = activeItem === item.id
            const isExpanded = expandedItems.includes(item.id)
            const hasSubItems = item.subItems && item.subItems.length > 0

            return (
              <li key={item.id}>
                <div className="relative">
                  {isActive && (
                    <div className="absolute -left-4 top-0 bottom-0 w-1 bg-[#2D60FF] rounded-r-lg" />
                  )}
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full justify-start h-12 px-4 text-left font-medium transition-all relative",
                      isActive
                        ? "bg-[#2D60FF] text-white hover:bg-[#2D60FF]/90"
                        : "text-[#B1B1B1] hover:bg-gray-50 hover:text-[#343C6A]"
                    )}
                    onClick={() => {
                      onItemClick?.(item.id)
                      if (hasSubItems) {
                        toggleExpanded(item.id)
                      }
                    }}
                  >
                    <Icon className="mr-3 h-5 w-5" />
                    {item.label}
                    {hasSubItems && (
                      <div className="ml-auto">
                        {isExpanded ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </div>
                    )}
                  </Button>
                </div>

                {/* Sub Items */}
                {hasSubItems && isExpanded && (
                  <ul className="ml-8 mt-2 space-y-1">
                    {item.subItems?.map((subItem) => {
                      const SubIcon = subItem.icon
                      const isSubActive = activeItem === subItem.id

                      return (
                        <li key={subItem.id}>
                          <Button
                            variant="ghost"
                            className={cn(
                              "w-full justify-start h-10 px-3 text-left font-normal transition-all",
                              isSubActive
                                ? "text-[#2D60FF] bg-[#2D60FF]/10"
                                : "text-[#B1B1B1] hover:bg-gray-50 hover:text-[#343C6A]"
                            )}
                            onClick={() => onItemClick?.(subItem.id)}
                          >
                            <SubIcon className="mr-3 h-4 w-4" />
                            {subItem.label}
                          </Button>
                        </li>
                      )
                    })}
                  </ul>
                )}
              </li>
            )
          })}
        </ul>
      </nav>
    </aside>
  )
}
