"use client"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { 
  Home, 
  CreditCard, 
  ArrowUpDown, 
  PieChart, 
  Users, 
  Settings, 
  HelpCircle,
  LogOut
} from "lucide-react"

interface SidebarProps {
  activeItem?: string
  onItemClick?: (item: string) => void
}

const menuItems = [
  { id: 'dashboard', label: 'Dashboard', icon: Home },
  { id: 'transactions', label: 'Transactions', icon: ArrowUpDown },
  { id: 'accounts', label: 'Accounts', icon: CreditCard },
  { id: 'investments', label: 'Investments', icon: PieChart },
  { id: 'credit-cards', label: 'Credit Cards', icon: CreditCard },
  { id: 'loans', label: 'Loans', icon: Users },
  { id: 'services', label: 'Services', icon: Settings },
  { id: 'privileges', label: 'My Privileges', icon: Users },
  { id: 'settings', label: 'Setting', icon: Settings },
]

export function Sidebar({ activeItem = 'dashboard', onItemClick }: SidebarProps) {
  return (
    <aside className="w-64 bg-white border-r border-gray-200 h-screen flex flex-col hidden lg:flex">
      {/* Logo */}
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-2xl font-bold text-[#343C6A]">AeTrust</h2>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon
            const isActive = activeItem === item.id
            
            return (
              <li key={item.id}>
                <Button
                  variant="ghost"
                  className={cn(
                    "w-full justify-start h-12 px-4 text-left font-medium transition-all",
                    isActive 
                      ? "bg-[#1814F3] text-white hover:bg-[#1814F3]/90" 
                      : "text-[#718EBF] hover:bg-gray-50 hover:text-[#343C6A]"
                  )}
                  onClick={() => onItemClick?.(item.id)}
                >
                  <Icon className="mr-3 h-5 w-5" />
                  {item.label}
                  {isActive && (
                    <div className="absolute right-0 top-0 bottom-0 w-1 bg-[#1814F3] rounded-l-lg" />
                  )}
                </Button>
              </li>
            )
          })}
        </ul>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <Button
          variant="ghost"
          className="w-full justify-start h-12 px-4 text-[#718EBF] hover:bg-gray-50 hover:text-[#343C6A]"
        >
          <HelpCircle className="mr-3 h-5 w-5" />
          Help & Support
        </Button>
        <Button
          variant="ghost"
          className="w-full justify-start h-12 px-4 text-[#718EBF] hover:bg-gray-50 hover:text-red-600"
        >
          <LogOut className="mr-3 h-5 w-5" />
          Logout
        </Button>
      </div>
    </aside>
  )
}
