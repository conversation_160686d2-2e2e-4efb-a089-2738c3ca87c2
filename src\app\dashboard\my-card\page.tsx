"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/dashboard/header"
import { Sidebar } from "@/components/dashboard/sidebar"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Eye, Copy, CreditCard, Calendar, Shield } from "lucide-react"

export default function MyCardPage() {
  const [activeMenuItem, setActiveMenuItem] = useState("my-card")
  const [showBalance, setShowBalance] = useState(true)
  const [showCardDetails, setShowCardDetails] = useState(false)

  const cardData = {
    cardNumber: "3778 1234 5678 1234",
    cardHolder: "James Bond",
    validThru: "12/22",
    cvv: "123",
    balance: 35673,
    tier: "Tier 1"
  }

  const toggleBalance = () => {
    setShowBalance(!showBalance)
  }

  const toggleCardDetails = () => {
    setShowCardDetails(!showCardDetails)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // You could add a toast notification here
  }

  return (
    <div className="flex h-screen bg-[#F5F7FA]">
      <Sidebar activeItem={activeMenuItem} onItemClick={setActiveMenuItem} />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header 
          title="My Card" 
          userName="James Bond"
        />
        
        <main className="flex-1 overflow-auto p-4 md:p-8">
          <div className="max-w-4xl mx-auto">
            <div className="mb-8">
              <h1 className="text-2xl font-semibold text-[#333B69] mb-2">My account</h1>
            </div>

            {/* Main Card Display */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              {/* Card Visual */}
              <div>
                <Card className="bg-gradient-to-r from-[#1B263B] to-[#059AD1] text-white overflow-hidden relative">
                  <CardContent className="p-8">
                    <div className="space-y-6">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="text-lg font-medium mb-1">{cardData.cardHolder}</h3>
                          <div className="flex items-center gap-3">
                            <span className="text-lg font-mono">
                              {showCardDetails ? cardData.cardNumber : "•••• •••• •••• 1234"}
                            </span>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              className="text-white hover:bg-white/20 p-1"
                              onClick={() => copyToClipboard(cardData.cardNumber)}
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-white hover:bg-white/20 p-1"
                          onClick={toggleCardDetails}
                        >
                          <Eye className="h-5 w-5" />
                        </Button>
                      </div>
                      
                      <div>
                        <p className="text-sm opacity-80 mb-1">Your balance</p>
                        <div className="flex items-center gap-3">
                          <span className="text-3xl font-bold">
                            {showBalance ? `$${cardData.balance.toLocaleString()}` : "••••••"}
                          </span>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="text-white hover:bg-white/20 p-1"
                            onClick={toggleBalance}
                          >
                            <Eye className="h-5 w-5" />
                          </Button>
                        </div>
                      </div>

                      <div className="flex justify-between items-end">
                        <div className="space-y-1">
                          <p className="text-xs opacity-60">VALID THRU</p>
                          <p className="text-sm font-mono">
                            {showCardDetails ? cardData.validThru : "••/••"}
                          </p>
                        </div>
                        
                        <div className="space-y-1">
                          <p className="text-xs opacity-60">CVV</p>
                          <p className="text-sm font-mono">
                            {showCardDetails ? cardData.cvv : "•••"}
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    {/* Card decoration */}
                    <div className="absolute top-0 right-0 w-32 h-8 bg-[#F2B134] rounded-bl-lg"></div>
                    
                    {/* Tier Badge */}
                    <div className="absolute bottom-6 right-6 flex items-center gap-2">
                      <span className="bg-[#F2B134] text-white px-3 py-1 rounded-full text-sm font-medium">
                        {cardData.tier}
                      </span>
                      <div className="w-6 h-6 bg-[#F2B134] rounded-full flex items-center justify-center">
                        <Shield className="h-4 w-4 text-white" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Card Actions */}
              <div className="space-y-6">
                <Card>
                  <CardContent className="p-6">
                    <h3 className="font-semibold text-[#333B69] mb-4">Card Actions</h3>
                    <div className="space-y-3">
                      <Button 
                        variant="outline" 
                        className="w-full justify-start h-12 border-[#E6EFF5] hover:bg-[#F5F7FA]"
                      >
                        <CreditCard className="mr-3 h-5 w-5 text-[#059AD1]" />
                        View Card Details
                      </Button>
                      
                      <Button 
                        variant="outline" 
                        className="w-full justify-start h-12 border-[#E6EFF5] hover:bg-[#F5F7FA]"
                      >
                        <Shield className="mr-3 h-5 w-5 text-[#059AD1]" />
                        Security Settings
                      </Button>
                      
                      <Button 
                        variant="outline" 
                        className="w-full justify-start h-12 border-[#E6EFF5] hover:bg-[#F5F7FA]"
                      >
                        <Calendar className="mr-3 h-5 w-5 text-[#059AD1]" />
                        Transaction History
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <h3 className="font-semibold text-[#333B69] mb-4">Account Information</h3>
                    <div className="space-y-4 text-sm">
                      <div className="flex justify-between">
                        <span className="text-[#6E6E6E]">Account Type:</span>
                        <span className="font-medium">Personal</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-[#6E6E6E]">Account Status:</span>
                        <span className="font-medium text-green-600">Active</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-[#6E6E6E]">Verification Level:</span>
                        <span className="font-medium">{cardData.tier}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-[#6E6E6E]">Daily Limit:</span>
                        <span className="font-medium">$5,000</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-[#6E6E6E]">Monthly Limit:</span>
                        <span className="font-medium">$50,000</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Quick Actions */}
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold text-[#333B69] mb-4">Quick Actions</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button 
                    className="bg-[#059AD1] hover:bg-[#059AD1]/90 text-white h-12"
                    onClick={() => window.location.href = '/dashboard/transfer'}
                  >
                    Transfer Money
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="border-[#059AD1] text-[#059AD1] hover:bg-[#059AD1] hover:text-white h-12"
                    onClick={() => window.location.href = '/dashboard/add-funds'}
                  >
                    Add Funds
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="border-[#F2B134] text-[#F2B134] hover:bg-[#F2B134] hover:text-white h-12"
                    onClick={() => window.location.href = '/dashboard/loans'}
                  >
                    Request Loan
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  )
}
