"use client"

import { useState } from "react"
import { Header } from "@/components/dashboard/header"
import { StatsCards } from "@/components/admin/stats-cards"
import { WeeklyActivityCard } from "@/components/admin/weekly-activity"
import { ExpenseStatistics } from "@/components/admin/expense-statistics"
import { QuickTransfer } from "@/components/admin/quick-transfer"
import { BalanceHistory } from "@/components/admin/balance-history"
import { TransactionTable } from "@/components/admin/transaction-table"
import { WeeklyActivity, StatisticItem, Transaction } from "@/types"

// Mock data for admin dashboard
const mockWeeklyActivity: WeeklyActivity[] = [
  { day: "Sat", cashIn: 480, cashOut: 320 },
  { day: "Sun", cashIn: 350, cashOut: 280 },
  { day: "Mon", cashIn: 400, cashOut: 300 },
  { day: "Tue", cashIn: 300, cashOut: 200 },
  { day: "Wed", cashIn: 450, cashOut: 350 },
  { day: "Thu", cashIn: 380, cashOut: 280 },
  { day: "Fri", cashIn: 420, cashOut: 320 }
]

const mockExpenseStats: StatisticItem[] = [
  { label: "User Wallets", value: 30, percentage: 30, color: "#1814F3" },
  { label: "Agent Float", value: 20, percentage: 20, color: "#16DBCC" },
  { label: "Merchant Payouts", value: 15, percentage: 15, color: "#FF82AC" },
  { label: "Commission", value: 35, percentage: 35, color: "#FFBB38" }
]

const mockBalanceHistory = [
  { month: "Jul", value: 200 },
  { month: "Aug", value: 300 },
  { month: "Sep", value: 250 },
  { month: "Oct", value: 400 },
  { month: "Nov", value: 350 },
  { month: "Dec", value: 450 },
  { month: "Jan", value: 500 }
]

const mockAdminTransactions: Transaction[] = [
  {
    id: "TXN001",
    type: "cash_in",
    amount: 1250,
    description: "Agent Float Top-up",
    date: "2024-01-15",
    status: "completed"
  },
  {
    id: "TXN002",
    type: "cash_out",
    amount: 850,
    description: "Merchant Settlement",
    date: "2024-01-14",
    status: "completed"
  },
  {
    id: "TXN003",
    type: "transfer",
    amount: 2500,
    description: "Commission Transfer",
    date: "2024-01-13",
    status: "pending"
  },
  {
    id: "TXN004",
    type: "cash_in",
    amount: 3200,
    description: "User Wallet Funding",
    date: "2024-01-12",
    status: "completed"
  },
  {
    id: "TXN005",
    type: "cash_out",
    amount: 1800,
    description: "Agent Withdrawal",
    date: "2024-01-11",
    status: "failed"
  }
]

export default function AdminPage() {
  return (
    <div className="min-h-screen bg-[#F5F7FA]">
      <Header title="Admin Dashboard" />

      <main className="p-4 md:p-8">
        {/* Stats Cards */}
        <StatsCards
          totalUsers={12450}
          totalTransactions={8920}
          totalRevenue={2450000}
          activeAgents={156}
        />

        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          {/* Top Row */}
          <div className="lg:col-span-8">
            <WeeklyActivityCard data={mockWeeklyActivity} />
          </div>
          <div className="lg:col-span-4">
            <ExpenseStatistics data={mockExpenseStats} />
          </div>

          {/* Middle Row */}
          <div className="lg:col-span-4 order-3 lg:order-none">
            <QuickTransfer />
          </div>
          <div className="lg:col-span-8 order-2 lg:order-none">
            <BalanceHistory data={mockBalanceHistory} />
          </div>

          {/* Bottom Row */}
          <div className="lg:col-span-12 order-4">
            <TransactionTable transactions={mockAdminTransactions} />
          </div>
        </div>
      </main>
    </div>
  )
}
