"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ead<PERSON>, CardTitle } from "@/components/ui/card"
import { LineChartComponent } from "@/components/charts/line-chart"

interface BalanceHistoryProps {
  data: Array<{ month: string; value: number }>
}

export function BalanceHistory({ data }: BalanceHistoryProps) {
  return (
    <Card className="bg-white rounded-[25px] border-none shadow-lg">
      <CardHeader>
        <CardTitle className="text-[#343C6A] text-xl font-semibold">
          Total transaction over time
        </CardTitle>
      </CardHeader>
      <CardContent>
        <LineChartComponent data={data} height={250} color="#1814F3" />
      </CardContent>
    </Card>
  )
}
