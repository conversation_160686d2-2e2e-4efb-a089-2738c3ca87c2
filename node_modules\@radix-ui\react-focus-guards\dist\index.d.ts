import * as React from 'react';

interface FocusGuardsProps {
    children?: React.ReactNode;
}
declare function FocusGuards(props: FocusGuardsProps): React.ReactNode;
/**
 * Injects a pair of focus guards at the edges of the whole DOM tree
 * to ensure `focusin` & `focusout` events can be caught consistently.
 */
declare function useFocusGuards(): void;

export { FocusGuards, FocusGuards as Root, useFocusGuards };
