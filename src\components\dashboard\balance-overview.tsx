"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Eye, EyeOff, TrendingUp, TrendingDown, DollarSign } from "lucide-react"

interface BalanceOverviewProps {
  balance: number
  monthlyIncome: number
  monthlyExpenses: number
  savingsGoal?: number
}

export function BalanceOverview({ 
  balance, 
  monthlyIncome, 
  monthlyExpenses, 
  savingsGoal = 50000 
}: BalanceOverviewProps) {
  const [showBalance, setShowBalance] = useState(true)
  
  const netChange = monthlyIncome - monthlyExpenses
  const savingsProgress = (balance / savingsGoal) * 100

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Main Balance Card */}
      <Card className="md:col-span-2">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-[#333B69]">Available Balance</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowBalance(!showBalance)}
              className="p-2"
            >
              {showBalance ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </Button>
          </div>
          
          <div className="space-y-2">
            <p className="text-4xl font-bold text-[#333B69]">
              {showBalance ? `$${balance.toLocaleString()}` : "••••••"}
            </p>
            <div className="flex items-center gap-2">
              {netChange >= 0 ? (
                <TrendingUp className="h-4 w-4 text-green-500" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500" />
              )}
              <span className={`text-sm font-medium ${netChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {netChange >= 0 ? '+' : ''}${netChange.toLocaleString()} this month
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Monthly Income */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-[#6E6E6E] mb-1">Monthly Income</p>
              <p className="text-2xl font-bold text-green-600">
                ${monthlyIncome.toLocaleString()}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <TrendingUp className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Monthly Expenses */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-[#6E6E6E] mb-1">Monthly Expenses</p>
              <p className="text-2xl font-bold text-red-600">
                ${monthlyExpenses.toLocaleString()}
              </p>
            </div>
            <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
              <TrendingDown className="h-6 w-6 text-red-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Savings Goal Progress */}
      <Card className="md:col-span-2 lg:col-span-4">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-[#059AD1]/10 rounded-xl flex items-center justify-center">
                <DollarSign className="h-5 w-5 text-[#059AD1]" />
              </div>
              <div>
                <h3 className="font-semibold text-[#333B69]">Savings Goal Progress</h3>
                <p className="text-sm text-[#6E6E6E]">Target: ${savingsGoal.toLocaleString()}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-[#059AD1]">{savingsProgress.toFixed(1)}%</p>
              <p className="text-sm text-[#6E6E6E]">${(savingsGoal - balance).toLocaleString()} to go</p>
            </div>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className="bg-[#059AD1] h-3 rounded-full transition-all duration-300"
              style={{ width: `${Math.min(savingsProgress, 100)}%` }}
            ></div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
