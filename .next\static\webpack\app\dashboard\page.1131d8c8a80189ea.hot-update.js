"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-down.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChevronDown)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.542.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m6 9 6 6 6-6\",\n            key: \"qrunsl\"\n        }\n    ]\n];\nconst ChevronDown = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chevron-down\", __iconNode);\n //# sourceMappingURL=chevron-down.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi1kb3duLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUdPLE1BQU0sYUFBdUI7SUFBQztRQUFDO1FBQVE7WUFBRSxHQUFHO1lBQWdCLEtBQUs7UUFBQSxDQUFVO0tBQUM7Q0FBQTtBQWFuRixNQUFNLGNBQWMsaUVBQWlCLGdCQUFnQixVQUFVIiwic291cmNlcyI6WyJEOlxcc3JjXFxpY29uc1xcY2hldnJvbi1kb3duLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuaW1wb3J0IHsgSWNvbk5vZGUgfSBmcm9tICcuLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBfX2ljb25Ob2RlOiBJY29uTm9kZSA9IFtbJ3BhdGgnLCB7IGQ6ICdtNiA5IDYgNiA2LTYnLCBrZXk6ICdxcnVuc2wnIH1dXTtcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENoZXZyb25Eb3duXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSnROaUE1SURZZ05pQTJMVFlpSUM4K0Nqd3ZjM1puUGdvPSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvY2hldnJvbi1kb3duXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQ2hldnJvbkRvd24gPSBjcmVhdGVMdWNpZGVJY29uKCdjaGV2cm9uLWRvd24nLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2hldnJvbkRvd247XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-right.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChevronRight)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.542.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m9 18 6-6-6-6\",\n            key: \"mthhwq\"\n        }\n    ]\n];\nconst ChevronRight = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chevron-right\", __iconNode);\n //# sourceMappingURL=chevron-right.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi1yaWdodC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHTyxNQUFNLGFBQXVCO0lBQUM7UUFBQztRQUFRO1lBQUUsR0FBRztZQUFpQixLQUFLO1FBQUEsQ0FBVTtLQUFDO0NBQUE7QUFhcEYsTUFBTSxlQUFlLGlFQUFpQixpQkFBaUIsVUFBVSIsInNvdXJjZXMiOlsiRDpcXHNyY1xcaWNvbnNcXGNoZXZyb24tcmlnaHQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5pbXBvcnQgeyBJY29uTm9kZSB9IGZyb20gJy4uL3R5cGVzJztcblxuZXhwb3J0IGNvbnN0IF9faWNvbk5vZGU6IEljb25Ob2RlID0gW1sncGF0aCcsIHsgZDogJ205IDE4IDYtNi02LTYnLCBrZXk6ICdtdGhod3EnIH1dXTtcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENoZXZyb25SaWdodFxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0p0T1NBeE9DQTJMVFl0TmkwMklpQXZQZ284TDNOMlp6NEspIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2NoZXZyb24tcmlnaHRcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBDaGV2cm9uUmlnaHQgPSBjcmVhdGVMdWNpZGVJY29uKCdjaGV2cm9uLXJpZ2h0JywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IENoZXZyb25SaWdodDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/sidebar.tsx":
/*!**********************************************!*\
  !*** ./src/components/dashboard/sidebar.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_ChevronDown_ChevronRight_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,ChevronDown,ChevronRight,CreditCard,DollarSign,Home,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_ChevronDown_ChevronRight_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,ChevronDown,ChevronRight,CreditCard,DollarSign,Home,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_ChevronDown_ChevronRight_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,ChevronDown,ChevronRight,CreditCard,DollarSign,Home,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_ChevronDown_ChevronRight_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,ChevronDown,ChevronRight,CreditCard,DollarSign,Home,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_ChevronDown_ChevronRight_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,ChevronDown,ChevronRight,CreditCard,DollarSign,Home,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_ChevronDown_ChevronRight_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,ChevronDown,ChevronRight,CreditCard,DollarSign,Home,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_ChevronDown_ChevronRight_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,ChevronDown,ChevronRight,CreditCard,DollarSign,Home,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_ChevronDown_ChevronRight_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,ChevronDown,ChevronRight,CreditCard,DollarSign,Home,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_ChevronDown_ChevronRight_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,ChevronDown,ChevronRight,CreditCard,DollarSign,Home,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst menuItems = [\n    {\n        id: 'dashboard',\n        label: 'Dashboard',\n        icon: _barrel_optimize_names_ArrowDownUp_ArrowUpDown_ChevronDown_ChevronRight_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        href: '/dashboard'\n    },\n    {\n        id: 'transfer',\n        label: 'Transfer',\n        icon: _barrel_optimize_names_ArrowDownUp_ArrowUpDown_ChevronDown_ChevronRight_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        href: '/dashboard/transfer',\n        subItems: [\n            {\n                id: 'withdraw',\n                label: 'Withdraw',\n                icon: _barrel_optimize_names_ArrowDownUp_ArrowUpDown_ChevronDown_ChevronRight_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                href: '/dashboard/withdraw'\n            },\n            {\n                id: 'add-funds',\n                label: 'Add funds',\n                icon: _barrel_optimize_names_ArrowDownUp_ArrowUpDown_ChevronDown_ChevronRight_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                href: '/dashboard/add-funds'\n            },\n            {\n                id: 'bills',\n                label: 'Bills',\n                icon: _barrel_optimize_names_ArrowDownUp_ArrowUpDown_ChevronDown_ChevronRight_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                href: '/dashboard/bills'\n            },\n            {\n                id: 'loans',\n                label: 'Loans',\n                icon: _barrel_optimize_names_ArrowDownUp_ArrowUpDown_ChevronDown_ChevronRight_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                href: '/dashboard/loans'\n            }\n        ]\n    },\n    {\n        id: 'settings',\n        label: 'Setting',\n        icon: _barrel_optimize_names_ArrowDownUp_ArrowUpDown_ChevronDown_ChevronRight_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        href: '/dashboard/settings'\n    }\n];\nfunction Sidebar(param) {\n    let { activeItem = 'dashboard', onItemClick } = param;\n    _s();\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([\n        'transfer'\n    ]);\n    const toggleExpanded = (itemId)=>{\n        setExpandedItems((prev)=>prev.includes(itemId) ? prev.filter((id)=>id !== itemId) : [\n                ...prev,\n                itemId\n            ]);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"w-64 bg-white border-r border-[#E6EFF5] h-screen flex flex-col hidden lg:flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-[#E6EFF5]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-[#343C6A]\",\n                    children: \"AeTrust\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"space-y-2\",\n                    children: menuItems.map((item)=>{\n                        var _item_subItems;\n                        const Icon = item.icon;\n                        const isActive = activeItem === item.id;\n                        const isExpanded = expandedItems.includes(item.id);\n                        const hasSubItems = item.subItems && item.subItems.length > 0;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -left-4 top-0 bottom-0 w-1 bg-[#2D60FF] rounded-r-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"w-full justify-start h-12 px-4 text-left font-medium transition-all relative\", isActive ? \"bg-[#2D60FF] text-white hover:bg-[#2D60FF]/90\" : \"text-[#B1B1B1] hover:bg-gray-50 hover:text-[#343C6A]\"),\n                                            onClick: ()=>{\n                                                onItemClick === null || onItemClick === void 0 ? void 0 : onItemClick(item.id);\n                                                if (hasSubItems) {\n                                                    toggleExpanded(item.id);\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"mr-3 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 21\n                                                }, this),\n                                                item.label,\n                                                hasSubItems && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-auto\",\n                                                    children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownUp_ArrowUpDown_ChevronDown_ChevronRight_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownUp_ArrowUpDown_ChevronDown_ChevronRight_CreditCard_DollarSign_Home_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 17\n                                }, this),\n                                hasSubItems && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"ml-8 mt-2 space-y-1\",\n                                    children: (_item_subItems = item.subItems) === null || _item_subItems === void 0 ? void 0 : _item_subItems.map((subItem)=>{\n                                        const SubIcon = subItem.icon;\n                                        const isSubActive = activeItem === subItem.id;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"w-full justify-start h-10 px-3 text-left font-normal transition-all\", isSubActive ? \"text-[#2D60FF] bg-[#2D60FF]/10\" : \"text-[#B1B1B1] hover:bg-gray-50 hover:text-[#343C6A]\"),\n                                                onClick: ()=>onItemClick === null || onItemClick === void 0 ? void 0 : onItemClick(subItem.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SubIcon, {\n                                                        className: \"mr-3 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    subItem.label\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, subItem.id, false, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 25\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, item.id, true, {\n                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"32yQtCLIu9HYVUDaccatjJ+xdt4=\");\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/sidebar.tsx\n"));

/***/ })

});