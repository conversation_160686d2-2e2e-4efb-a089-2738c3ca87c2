"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/eye-off.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ EyeOff)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.542.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49\",\n            key: \"ct8e1f\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14.084 14.158a3 3 0 0 1-4.242-4.242\",\n            key: \"151rxh\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143\",\n            key: \"13bj9a\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m2 2 20 20\",\n            key: \"1ooewy\"\n        }\n    ]\n];\nconst EyeOff = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"eye-off\", __iconNode);\n //# sourceMappingURL=eye-off.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/receipt.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Receipt)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.542.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z\",\n            key: \"q3az6g\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8\",\n            key: \"1h4pet\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 17.5v-11\",\n            key: \"1jc1ny\"\n        }\n    ]\n];\nconst Receipt = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"receipt\", __iconNode);\n //# sourceMappingURL=receipt.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trending-down.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ TrendingDown)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.542.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M16 17h6v-6\",\n            key: \"t6n2it\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m22 17-8.5-8.5-5 5L2 7\",\n            key: \"x473p\"\n        }\n    ]\n];\nconst TrendingDown = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"trending-down\", __iconNode);\n //# sourceMappingURL=trending-down.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trending-up.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ TrendingUp)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.542.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M16 7h6v6\",\n            key: \"box55l\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m22 7-8.5 8.5-5-5L2 17\",\n            key: \"1t1m79\"\n        }\n    ]\n];\nconst TrendingUp = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"trending-up\", __iconNode);\n //# sourceMappingURL=trending-up.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/header */ \"(app-pages-browser)/./src/components/dashboard/header.tsx\");\n/* harmony import */ var _components_dashboard_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/sidebar */ \"(app-pages-browser)/./src/components/dashboard/sidebar.tsx\");\n/* harmony import */ var _components_dashboard_account_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/account-card */ \"(app-pages-browser)/./src/components/dashboard/account-card.tsx\");\n/* harmony import */ var _components_dashboard_statistics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/statistics */ \"(app-pages-browser)/./src/components/dashboard/statistics.tsx\");\n/* harmony import */ var _components_dashboard_recent_transactions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/recent-transactions */ \"(app-pages-browser)/./src/components/dashboard/recent-transactions.tsx\");\n/* harmony import */ var _components_dashboard_transfer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/transfer */ \"(app-pages-browser)/./src/components/dashboard/transfer.tsx\");\n/* harmony import */ var _components_dashboard_quick_actions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/quick-actions */ \"(app-pages-browser)/./src/components/dashboard/quick-actions.tsx\");\n/* harmony import */ var _components_dashboard_balance_overview__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/balance-overview */ \"(app-pages-browser)/./src/components/dashboard/balance-overview.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Mock data\nconst mockUser = {\n    id: \"1\",\n    name: \"James Bond\",\n    email: \"<EMAIL>\",\n    balance: 35673,\n    cardNumber: \"3778123456781234\",\n    cardHolder: \"James Bond\",\n    validThru: \"12/22\",\n    tier: \"Tier 1\"\n};\nconst mockTransactions = [\n    {\n        id: \"1\",\n        type: \"cash_in\",\n        amount: 850,\n        description: \"Cash in (Agent yusuff)\",\n        date: \"2021-01-28\",\n        status: \"completed\",\n        agent: \"Agent yusuff\"\n    },\n    {\n        id: \"2\",\n        type: \"cash_out\",\n        amount: 2500,\n        description: \"Cash out\",\n        date: \"2021-01-25\",\n        status: \"completed\"\n    },\n    {\n        id: \"3\",\n        type: \"transfer\",\n        amount: 5400,\n        description: \"Transfer\",\n        date: \"2021-01-21\",\n        status: \"completed\"\n    }\n];\nconst mockStatistics = [\n    {\n        label: \"Cash in\",\n        value: 30,\n        percentage: 30,\n        color: \"#1814F3\"\n    },\n    {\n        label: \"Loan\",\n        value: 20,\n        percentage: 20,\n        color: \"#16DBCC\"\n    },\n    {\n        label: \"Cash out\",\n        value: 15,\n        percentage: 15,\n        color: \"#FF82AC\"\n    },\n    {\n        label: \"Others\",\n        value: 35,\n        percentage: 35,\n        color: \"#FFBB38\"\n    }\n];\nfunction DashboardPage() {\n    _s();\n    const [activeMenuItem, setActiveMenuItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"dashboard\");\n    const handleMenuItemClick = (item)=>{\n        setActiveMenuItem(item);\n        // Navigate to different pages based on menu item\n        switch(item){\n            case 'transfer':\n                window.location.href = '/dashboard/transfer';\n                break;\n            case 'withdraw':\n                window.location.href = '/dashboard/withdraw';\n                break;\n            case 'add-funds':\n                window.location.href = '/dashboard/add-funds';\n                break;\n            case 'bills':\n                window.location.href = '/dashboard/bills';\n                break;\n            case 'loans':\n                window.location.href = '/dashboard/loans';\n                break;\n            case 'settings':\n                window.location.href = '/dashboard/settings';\n                break;\n            default:\n                break;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-[#F5F7FA]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                activeItem: activeMenuItem,\n                onItemClick: handleMenuItemClick\n            }, void 0, false, {\n                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_header__WEBPACK_IMPORTED_MODULE_2__.Header, {\n                        title: \"Overview\",\n                        userName: mockUser.name\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-auto p-4 md:p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_balance_overview__WEBPACK_IMPORTED_MODULE_9__.BalanceOverview, {\n                                    balance: mockUser.balance,\n                                    monthlyIncome: 25000,\n                                    monthlyExpenses: 15000,\n                                    savingsGoal: 100000\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-12 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"lg:col-span-8 space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_account_card__WEBPACK_IMPORTED_MODULE_4__.AccountCard, {\n                                                    user: mockUser\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_quick_actions__WEBPACK_IMPORTED_MODULE_8__.QuickActions, {}, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_recent_transactions__WEBPACK_IMPORTED_MODULE_6__.RecentTransactions, {\n                                                    transactions: mockTransactions\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"lg:col-span-4 space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_transfer__WEBPACK_IMPORTED_MODULE_7__.Transfer, {}, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_statistics__WEBPACK_IMPORTED_MODULE_5__.Statistics, {\n                                                    data: mockStatistics\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"rXZr4XtoNSi2YkrftYtC3LNNsX8=\");\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRWdDO0FBQ3NCO0FBQ0U7QUFDUztBQUNIO0FBQ2lCO0FBQ3JCO0FBQ1M7QUFDTTtBQUd6RSxZQUFZO0FBQ1osTUFBTVMsV0FBaUI7SUFDckJDLElBQUk7SUFDSkMsTUFBTTtJQUNOQyxPQUFPO0lBQ1BDLFNBQVM7SUFDVEMsWUFBWTtJQUNaQyxZQUFZO0lBQ1pDLFdBQVc7SUFDWEMsTUFBTTtBQUNSO0FBRUEsTUFBTUMsbUJBQWtDO0lBQ3RDO1FBQ0VSLElBQUk7UUFDSlMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLGFBQWE7UUFDYkMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLE9BQU87SUFDVDtJQUNBO1FBQ0VkLElBQUk7UUFDSlMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLGFBQWE7UUFDYkMsTUFBTTtRQUNOQyxRQUFRO0lBQ1Y7SUFDQTtRQUNFYixJQUFJO1FBQ0pTLE1BQU07UUFDTkMsUUFBUTtRQUNSQyxhQUFhO1FBQ2JDLE1BQU07UUFDTkMsUUFBUTtJQUNWO0NBQ0Q7QUFFRCxNQUFNRSxpQkFBa0M7SUFDdEM7UUFBRUMsT0FBTztRQUFXQyxPQUFPO1FBQUlDLFlBQVk7UUFBSUMsT0FBTztJQUFVO0lBQ2hFO1FBQUVILE9BQU87UUFBUUMsT0FBTztRQUFJQyxZQUFZO1FBQUlDLE9BQU87SUFBVTtJQUM3RDtRQUFFSCxPQUFPO1FBQVlDLE9BQU87UUFBSUMsWUFBWTtRQUFJQyxPQUFPO0lBQVU7SUFDakU7UUFBRUgsT0FBTztRQUFVQyxPQUFPO1FBQUlDLFlBQVk7UUFBSUMsT0FBTztJQUFVO0NBQ2hFO0FBRWMsU0FBU0M7O0lBQ3RCLE1BQU0sQ0FBQ0MsZ0JBQWdCQyxrQkFBa0IsR0FBR2hDLCtDQUFRQSxDQUFDO0lBRXJELE1BQU1pQyxzQkFBc0IsQ0FBQ0M7UUFDM0JGLGtCQUFrQkU7UUFFbEIsaURBQWlEO1FBQ2pELE9BQVFBO1lBQ04sS0FBSztnQkFDSEMsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7Z0JBQ3ZCO1lBQ0YsS0FBSztnQkFDSEYsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7Z0JBQ3ZCO1lBQ0YsS0FBSztnQkFDSEYsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7Z0JBQ3ZCO1lBQ0YsS0FBSztnQkFDSEYsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7Z0JBQ3ZCO1lBQ0YsS0FBSztnQkFDSEYsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7Z0JBQ3ZCO1lBQ0YsS0FBSztnQkFDSEYsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7Z0JBQ3ZCO1lBQ0Y7Z0JBRUU7UUFDSjtJQUNGO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDckMsa0VBQU9BO2dCQUFDc0MsWUFBWVQ7Z0JBQWdCVSxhQUFhUjs7Ozs7OzBCQUVsRCw4REFBQ0s7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDdEMsZ0VBQU1BO3dCQUNMeUMsT0FBTTt3QkFDTkMsVUFBVWxDLFNBQVNFLElBQUk7Ozs7OztrQ0FHekIsOERBQUNpQzt3QkFBS0wsV0FBVTtrQ0FDZCw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUViLDhEQUFDL0IsbUZBQWVBO29DQUNkSyxTQUFTSixTQUFTSSxPQUFPO29DQUN6QmdDLGVBQWU7b0NBQ2ZDLGlCQUFpQjtvQ0FDakJDLGFBQWE7Ozs7Ozs4Q0FHZiw4REFBQ1Q7b0NBQUlDLFdBQVU7O3NEQUViLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBRWIsOERBQUNwQywyRUFBV0E7b0RBQUM2QyxNQUFNdkM7Ozs7Ozs4REFHbkIsOERBQUNGLDZFQUFZQTs7Ozs7OERBR2IsOERBQUNGLHlGQUFrQkE7b0RBQUM0QyxjQUFjL0I7Ozs7Ozs7Ozs7OztzREFJcEMsOERBQUNvQjs0Q0FBSUMsV0FBVTs7OERBRWIsOERBQUNqQyxvRUFBUUE7Ozs7OzhEQUdULDhEQUFDRix3RUFBVUE7b0RBQUM4QyxNQUFNekI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUWxDO0dBL0V3Qks7S0FBQUEiLCJzb3VyY2VzIjpbIkQ6XFx3b3Jrc3BhY2VcXC50eXBlc2NyaXB0XFxhZXRydXN0ZnJvbnRlbmRcXHNyY1xcYXBwXFxkYXNoYm9hcmRcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IEhlYWRlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvZGFzaGJvYXJkL2hlYWRlclwiXG5pbXBvcnQgeyBTaWRlYmFyIH0gZnJvbSBcIkAvY29tcG9uZW50cy9kYXNoYm9hcmQvc2lkZWJhclwiXG5pbXBvcnQgeyBBY2NvdW50Q2FyZCB9IGZyb20gXCJAL2NvbXBvbmVudHMvZGFzaGJvYXJkL2FjY291bnQtY2FyZFwiXG5pbXBvcnQgeyBTdGF0aXN0aWNzIH0gZnJvbSBcIkAvY29tcG9uZW50cy9kYXNoYm9hcmQvc3RhdGlzdGljc1wiXG5pbXBvcnQgeyBSZWNlbnRUcmFuc2FjdGlvbnMgfSBmcm9tIFwiQC9jb21wb25lbnRzL2Rhc2hib2FyZC9yZWNlbnQtdHJhbnNhY3Rpb25zXCJcbmltcG9ydCB7IFRyYW5zZmVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy9kYXNoYm9hcmQvdHJhbnNmZXJcIlxuaW1wb3J0IHsgUXVpY2tBY3Rpb25zIH0gZnJvbSBcIkAvY29tcG9uZW50cy9kYXNoYm9hcmQvcXVpY2stYWN0aW9uc1wiXG5pbXBvcnQgeyBCYWxhbmNlT3ZlcnZpZXcgfSBmcm9tIFwiQC9jb21wb25lbnRzL2Rhc2hib2FyZC9iYWxhbmNlLW92ZXJ2aWV3XCJcbmltcG9ydCB7IFVzZXIsIFRyYW5zYWN0aW9uLCBTdGF0aXN0aWNJdGVtIH0gZnJvbSBcIkAvdHlwZXNcIlxuXG4vLyBNb2NrIGRhdGFcbmNvbnN0IG1vY2tVc2VyOiBVc2VyID0ge1xuICBpZDogXCIxXCIsXG4gIG5hbWU6IFwiSmFtZXMgQm9uZFwiLFxuICBlbWFpbDogXCJqYW1lcy5ib25kQGV4YW1wbGUuY29tXCIsXG4gIGJhbGFuY2U6IDM1NjczLFxuICBjYXJkTnVtYmVyOiBcIjM3NzgxMjM0NTY3ODEyMzRcIixcbiAgY2FyZEhvbGRlcjogXCJKYW1lcyBCb25kXCIsXG4gIHZhbGlkVGhydTogXCIxMi8yMlwiLFxuICB0aWVyOiBcIlRpZXIgMVwiXG59XG5cbmNvbnN0IG1vY2tUcmFuc2FjdGlvbnM6IFRyYW5zYWN0aW9uW10gPSBbXG4gIHtcbiAgICBpZDogXCIxXCIsXG4gICAgdHlwZTogXCJjYXNoX2luXCIsXG4gICAgYW1vdW50OiA4NTAsXG4gICAgZGVzY3JpcHRpb246IFwiQ2FzaCBpbiAoQWdlbnQgeXVzdWZmKVwiLFxuICAgIGRhdGU6IFwiMjAyMS0wMS0yOFwiLFxuICAgIHN0YXR1czogXCJjb21wbGV0ZWRcIixcbiAgICBhZ2VudDogXCJBZ2VudCB5dXN1ZmZcIlxuICB9LFxuICB7XG4gICAgaWQ6IFwiMlwiLFxuICAgIHR5cGU6IFwiY2FzaF9vdXRcIixcbiAgICBhbW91bnQ6IDI1MDAsXG4gICAgZGVzY3JpcHRpb246IFwiQ2FzaCBvdXRcIixcbiAgICBkYXRlOiBcIjIwMjEtMDEtMjVcIixcbiAgICBzdGF0dXM6IFwiY29tcGxldGVkXCJcbiAgfSxcbiAge1xuICAgIGlkOiBcIjNcIixcbiAgICB0eXBlOiBcInRyYW5zZmVyXCIsXG4gICAgYW1vdW50OiA1NDAwLFxuICAgIGRlc2NyaXB0aW9uOiBcIlRyYW5zZmVyXCIsXG4gICAgZGF0ZTogXCIyMDIxLTAxLTIxXCIsXG4gICAgc3RhdHVzOiBcImNvbXBsZXRlZFwiXG4gIH1cbl1cblxuY29uc3QgbW9ja1N0YXRpc3RpY3M6IFN0YXRpc3RpY0l0ZW1bXSA9IFtcbiAgeyBsYWJlbDogXCJDYXNoIGluXCIsIHZhbHVlOiAzMCwgcGVyY2VudGFnZTogMzAsIGNvbG9yOiBcIiMxODE0RjNcIiB9LFxuICB7IGxhYmVsOiBcIkxvYW5cIiwgdmFsdWU6IDIwLCBwZXJjZW50YWdlOiAyMCwgY29sb3I6IFwiIzE2REJDQ1wiIH0sXG4gIHsgbGFiZWw6IFwiQ2FzaCBvdXRcIiwgdmFsdWU6IDE1LCBwZXJjZW50YWdlOiAxNSwgY29sb3I6IFwiI0ZGODJBQ1wiIH0sXG4gIHsgbGFiZWw6IFwiT3RoZXJzXCIsIHZhbHVlOiAzNSwgcGVyY2VudGFnZTogMzUsIGNvbG9yOiBcIiNGRkJCMzhcIiB9XG5dXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERhc2hib2FyZFBhZ2UoKSB7XG4gIGNvbnN0IFthY3RpdmVNZW51SXRlbSwgc2V0QWN0aXZlTWVudUl0ZW1dID0gdXNlU3RhdGUoXCJkYXNoYm9hcmRcIilcblxuICBjb25zdCBoYW5kbGVNZW51SXRlbUNsaWNrID0gKGl0ZW06IHN0cmluZykgPT4ge1xuICAgIHNldEFjdGl2ZU1lbnVJdGVtKGl0ZW0pXG5cbiAgICAvLyBOYXZpZ2F0ZSB0byBkaWZmZXJlbnQgcGFnZXMgYmFzZWQgb24gbWVudSBpdGVtXG4gICAgc3dpdGNoIChpdGVtKSB7XG4gICAgICBjYXNlICd0cmFuc2Zlcic6XG4gICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9kYXNoYm9hcmQvdHJhbnNmZXInXG4gICAgICAgIGJyZWFrXG4gICAgICBjYXNlICd3aXRoZHJhdyc6XG4gICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9kYXNoYm9hcmQvd2l0aGRyYXcnXG4gICAgICAgIGJyZWFrXG4gICAgICBjYXNlICdhZGQtZnVuZHMnOlxuICAgICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvZGFzaGJvYXJkL2FkZC1mdW5kcydcbiAgICAgICAgYnJlYWtcbiAgICAgIGNhc2UgJ2JpbGxzJzpcbiAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnL2Rhc2hib2FyZC9iaWxscydcbiAgICAgICAgYnJlYWtcbiAgICAgIGNhc2UgJ2xvYW5zJzpcbiAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnL2Rhc2hib2FyZC9sb2FucydcbiAgICAgICAgYnJlYWtcbiAgICAgIGNhc2UgJ3NldHRpbmdzJzpcbiAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnL2Rhc2hib2FyZC9zZXR0aW5ncydcbiAgICAgICAgYnJlYWtcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIC8vIFN0YXkgb24gZGFzaGJvYXJkXG4gICAgICAgIGJyZWFrXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaC1zY3JlZW4gYmctWyNGNUY3RkFdXCI+XG4gICAgICA8U2lkZWJhciBhY3RpdmVJdGVtPXthY3RpdmVNZW51SXRlbX0gb25JdGVtQ2xpY2s9e2hhbmRsZU1lbnVJdGVtQ2xpY2t9IC8+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggZmxleC1jb2wgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgIDxIZWFkZXJcbiAgICAgICAgICB0aXRsZT1cIk92ZXJ2aWV3XCJcbiAgICAgICAgICB1c2VyTmFtZT17bW9ja1VzZXIubmFtZX1cbiAgICAgICAgLz5cblxuICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3ctYXV0byBwLTQgbWQ6cC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgIHsvKiBCYWxhbmNlIE92ZXJ2aWV3ICovfVxuICAgICAgICAgICAgPEJhbGFuY2VPdmVydmlld1xuICAgICAgICAgICAgICBiYWxhbmNlPXttb2NrVXNlci5iYWxhbmNlfVxuICAgICAgICAgICAgICBtb250aGx5SW5jb21lPXsyNTAwMH1cbiAgICAgICAgICAgICAgbW9udGhseUV4cGVuc2VzPXsxNTAwMH1cbiAgICAgICAgICAgICAgc2F2aW5nc0dvYWw9ezEwMDAwMH1cbiAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMTIgZ2FwLTZcIj5cbiAgICAgICAgICAgICAgey8qIExlZnQgQ29sdW1uICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTggc3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAgey8qIEFjY291bnQgQ2FyZCAqL31cbiAgICAgICAgICAgICAgICA8QWNjb3VudENhcmQgdXNlcj17bW9ja1VzZXJ9IC8+XG5cbiAgICAgICAgICAgICAgICB7LyogUXVpY2sgQWN0aW9ucyAqL31cbiAgICAgICAgICAgICAgICA8UXVpY2tBY3Rpb25zIC8+XG5cbiAgICAgICAgICAgICAgICB7LyogUmVjZW50IFRyYW5zYWN0aW9ucyAqL31cbiAgICAgICAgICAgICAgICA8UmVjZW50VHJhbnNhY3Rpb25zIHRyYW5zYWN0aW9ucz17bW9ja1RyYW5zYWN0aW9uc30gLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIFJpZ2h0IENvbHVtbiAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi00IHNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICAgIHsvKiBUcmFuc2ZlciBBY3Rpb25zICovfVxuICAgICAgICAgICAgICAgIDxUcmFuc2ZlciAvPlxuXG4gICAgICAgICAgICAgICAgey8qIFN0YXRpc3RpY3MgKi99XG4gICAgICAgICAgICAgICAgPFN0YXRpc3RpY3MgZGF0YT17bW9ja1N0YXRpc3RpY3N9IC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvbWFpbj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJIZWFkZXIiLCJTaWRlYmFyIiwiQWNjb3VudENhcmQiLCJTdGF0aXN0aWNzIiwiUmVjZW50VHJhbnNhY3Rpb25zIiwiVHJhbnNmZXIiLCJRdWlja0FjdGlvbnMiLCJCYWxhbmNlT3ZlcnZpZXciLCJtb2NrVXNlciIsImlkIiwibmFtZSIsImVtYWlsIiwiYmFsYW5jZSIsImNhcmROdW1iZXIiLCJjYXJkSG9sZGVyIiwidmFsaWRUaHJ1IiwidGllciIsIm1vY2tUcmFuc2FjdGlvbnMiLCJ0eXBlIiwiYW1vdW50IiwiZGVzY3JpcHRpb24iLCJkYXRlIiwic3RhdHVzIiwiYWdlbnQiLCJtb2NrU3RhdGlzdGljcyIsImxhYmVsIiwidmFsdWUiLCJwZXJjZW50YWdlIiwiY29sb3IiLCJEYXNoYm9hcmRQYWdlIiwiYWN0aXZlTWVudUl0ZW0iLCJzZXRBY3RpdmVNZW51SXRlbSIsImhhbmRsZU1lbnVJdGVtQ2xpY2siLCJpdGVtIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwiZGl2IiwiY2xhc3NOYW1lIiwiYWN0aXZlSXRlbSIsIm9uSXRlbUNsaWNrIiwidGl0bGUiLCJ1c2VyTmFtZSIsIm1haW4iLCJtb250aGx5SW5jb21lIiwibW9udGhseUV4cGVuc2VzIiwic2F2aW5nc0dvYWwiLCJ1c2VyIiwidHJhbnNhY3Rpb25zIiwiZGF0YSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/balance-overview.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/balance-overview.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BalanceOverview: () => (/* binding */ BalanceOverview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Eye_EyeOff_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Eye,EyeOff,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Eye_EyeOff_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Eye,EyeOff,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Eye_EyeOff_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Eye,EyeOff,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Eye_EyeOff_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Eye,EyeOff,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Eye_EyeOff_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Eye,EyeOff,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* __next_internal_client_entry_do_not_use__ BalanceOverview auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction BalanceOverview(param) {\n    let { balance, monthlyIncome, monthlyExpenses, savingsGoal = 50000 } = param;\n    _s();\n    const [showBalance, setShowBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const netChange = monthlyIncome - monthlyExpenses;\n    const savingsProgress = balance / savingsGoal * 100;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"md:col-span-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-[#333B69]\",\n                                    children: \"Available Balance\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: ()=>setShowBalance(!showBalance),\n                                    className: \"p-2\",\n                                    children: showBalance ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_EyeOff_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 30\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_EyeOff_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 63\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-4xl font-bold text-[#333B69]\",\n                                    children: showBalance ? \"$\".concat(balance.toLocaleString()) : \"••••••\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        netChange >= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_EyeOff_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4 text-green-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_EyeOff_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium \".concat(netChange >= 0 ? 'text-green-600' : 'text-red-600'),\n                                            children: [\n                                                netChange >= 0 ? '+' : '',\n                                                \"$\",\n                                                netChange.toLocaleString(),\n                                                \" this month\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-[#6E6E6E] mb-1\",\n                                        children: \"Monthly Income\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: [\n                                            \"$\",\n                                            monthlyIncome.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_EyeOff_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-6 w-6 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-[#6E6E6E] mb-1\",\n                                        children: \"Monthly Expenses\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-red-600\",\n                                        children: [\n                                            \"$\",\n                                            monthlyExpenses.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_EyeOff_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-6 w-6 text-red-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"md:col-span-2 lg:col-span-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-[#059AD1]/10 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_EyeOff_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5 text-[#059AD1]\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-[#333B69]\",\n                                                    children: \"Savings Goal Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-[#6E6E6E]\",\n                                                    children: [\n                                                        \"Target: $\",\n                                                        savingsGoal.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-[#059AD1]\",\n                                            children: [\n                                                savingsProgress.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-[#6E6E6E]\",\n                                            children: [\n                                                \"$\",\n                                                (savingsGoal - balance).toLocaleString(),\n                                                \" to go\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-200 rounded-full h-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-[#059AD1] h-3 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: \"\".concat(Math.min(savingsProgress, 100), \"%\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\balance-overview.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n_s(BalanceOverview, \"8U3DFj+PNBywDY8VvBAlsZHlwbA=\");\n_c = BalanceOverview;\nvar _c;\n$RefreshReg$(_c, \"BalanceOverview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/balance-overview.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/quick-actions.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/quick-actions.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuickActions: () => (/* binding */ QuickActions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Plus_Receipt_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,CreditCard,DollarSign,Plus,Receipt!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Plus_Receipt_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,CreditCard,DollarSign,Plus,Receipt!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Plus_Receipt_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,CreditCard,DollarSign,Plus,Receipt!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Plus_Receipt_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,CreditCard,DollarSign,Plus,Receipt!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Plus_Receipt_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,CreditCard,DollarSign,Plus,Receipt!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Plus_Receipt_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownUp,ArrowUpDown,CreditCard,DollarSign,Plus,Receipt!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* __next_internal_client_entry_do_not_use__ QuickActions auto */ \n\n\n\nconst quickActions = [\n    {\n        id: 'transfer',\n        title: 'Transfer Money',\n        description: 'Send money to anyone',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Plus_Receipt_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n            lineNumber: 30,\n            columnNumber: 11\n        }, undefined),\n        color: '#059AD1',\n        href: '/dashboard/transfer'\n    },\n    {\n        id: 'add-funds',\n        title: 'Add Funds',\n        description: 'Top up your wallet',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Plus_Receipt_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n            lineNumber: 38,\n            columnNumber: 11\n        }, undefined),\n        color: '#16DBCC',\n        href: '/dashboard/add-funds'\n    },\n    {\n        id: 'withdraw',\n        title: 'Withdraw',\n        description: 'Cash out your money',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Plus_Receipt_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n            lineNumber: 46,\n            columnNumber: 11\n        }, undefined),\n        color: '#FF6B6B',\n        href: '/dashboard/withdraw'\n    },\n    {\n        id: 'bills',\n        title: 'Pay Bills',\n        description: 'Pay your bills easily',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Plus_Receipt_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n            lineNumber: 54,\n            columnNumber: 11\n        }, undefined),\n        color: '#F2B134',\n        href: '/dashboard/bills'\n    },\n    {\n        id: 'loans',\n        title: 'Get Loan',\n        description: 'Apply for instant loan',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Plus_Receipt_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n            lineNumber: 62,\n            columnNumber: 11\n        }, undefined),\n        color: '#9B59B6',\n        href: '/dashboard/loans'\n    },\n    {\n        id: 'transactions',\n        title: 'Transactions',\n        description: 'View transaction history',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownUp_ArrowUpDown_CreditCard_DollarSign_Plus_Receipt_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n            lineNumber: 70,\n            columnNumber: 11\n        }, undefined),\n        color: '#4CAF50',\n        href: '/dashboard/transactions'\n    }\n];\nfunction QuickActions() {\n    const handleActionClick = (href)=>{\n        window.location.href = href;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-[#333B69] mb-4\",\n                    children: \"Quick Actions\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 gap-4\",\n                    children: quickActions.map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"outline\",\n                            className: \"h-auto p-4 flex flex-col items-center gap-3 hover:shadow-md transition-shadow border-gray-200\",\n                            onClick: ()=>handleActionClick(action.href),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 rounded-xl flex items-center justify-center\",\n                                    style: {\n                                        backgroundColor: \"\".concat(action.color, \"20\"),\n                                        color: action.color\n                                    },\n                                    children: action.icon\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium text-[#333B69] text-sm\",\n                                            children: action.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-[#6E6E6E] mt-1\",\n                                            children: action.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, action.id, true, {\n                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n_c = QuickActions;\nvar _c;\n$RefreshReg$(_c, \"QuickActions\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/quick-actions.tsx\n"));

/***/ })

});