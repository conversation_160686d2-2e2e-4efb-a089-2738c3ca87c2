"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { 
  ArrowUpDown, 
  Plus, 
  ArrowDownUp, 
  CreditCard, 
  DollarSign,
  Receipt,
  Users,
  Settings
} from "lucide-react"

interface QuickAction {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  color: string
  href: string
}

const quickActions: QuickAction[] = [
  {
    id: 'transfer',
    title: 'Transfer Money',
    description: 'Send money to anyone',
    icon: <ArrowUpDown className="h-6 w-6" />,
    color: '#059AD1',
    href: '/dashboard/transfer'
  },
  {
    id: 'add-funds',
    title: 'Add Funds',
    description: 'Top up your wallet',
    icon: <Plus className="h-6 w-6" />,
    color: '#16DBCC',
    href: '/dashboard/add-funds'
  },
  {
    id: 'withdraw',
    title: 'Withdraw',
    description: 'Cash out your money',
    icon: <ArrowDownUp className="h-6 w-6" />,
    color: '#FF6B6B',
    href: '/dashboard/withdraw'
  },
  {
    id: 'bills',
    title: 'Pay Bills',
    description: 'Pay your bills easily',
    icon: <CreditCard className="h-6 w-6" />,
    color: '#F2B134',
    href: '/dashboard/bills'
  },
  {
    id: 'loans',
    title: 'Get Loan',
    description: 'Apply for instant loan',
    icon: <DollarSign className="h-6 w-6" />,
    color: '#9B59B6',
    href: '/dashboard/loans'
  },
  {
    id: 'transactions',
    title: 'Transactions',
    description: 'View transaction history',
    icon: <Receipt className="h-6 w-6" />,
    color: '#4CAF50',
    href: '/dashboard/transactions'
  }
]

export function QuickActions() {
  const handleActionClick = (href: string) => {
    window.location.href = href
  }

  return (
    <Card>
      <CardContent className="p-6">
        <h3 className="text-lg font-semibold text-[#333B69] mb-4">Quick Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {quickActions.map((action) => (
            <Button
              key={action.id}
              variant="outline"
              className="h-auto p-4 flex flex-col items-center gap-3 hover:shadow-md transition-shadow border-gray-200"
              onClick={() => handleActionClick(action.href)}
            >
              <div 
                className="w-12 h-12 rounded-xl flex items-center justify-center"
                style={{ backgroundColor: `${action.color}20`, color: action.color }}
              >
                {action.icon}
              </div>
              <div className="text-center">
                <p className="font-medium text-[#333B69] text-sm">{action.title}</p>
                <p className="text-xs text-[#6E6E6E] mt-1">{action.description}</p>
              </div>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
