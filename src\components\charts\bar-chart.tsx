"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from 'recharts'
import { WeeklyActivity } from '@/types'

interface BarChartComponentProps {
  data: WeeklyActivity[]
  height?: number
}

export function BarChartComponent({ data, height = 300 }: BarChartComponentProps) {
  return (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart
        data={data}
        margin={{
          top: 20,
          right: 30,
          left: 20,
          bottom: 5,
        }}
        barCategoryGap="20%"
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#E6EFF5" />
        <XAxis 
          dataKey="day" 
          axisLine={false}
          tickLine={false}
          tick={{ fontSize: 12, fill: '#718EBF' }}
        />
        <YAxis 
          axisLine={false}
          tickLine={false}
          tick={{ fontSize: 12, fill: '#718EBF' }}
        />
        <Legend 
          wrapperStyle={{ paddingTop: '20px' }}
          iconType="circle"
        />
        <Bar 
          dataKey="cashIn" 
          fill="#1814F3" 
          name="Cash In"
          radius={[30, 30, 30, 30]}
          barSize={15}
        />
        <Bar 
          dataKey="cashOut" 
          fill="#16DBCC" 
          name="Cash Out"
          radius={[30, 30, 30, 30]}
          barSize={15}
        />
      </BarChart>
    </ResponsiveContainer>
  )
}
