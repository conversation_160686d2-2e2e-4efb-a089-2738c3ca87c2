// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/dist/lib/metadata/types/metadata-interface.js"

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}


// Validate ../../src/app/admin/page.tsx
{
  const handler = {} as typeof import("../../src/app/admin/page.js")
  handler satisfies AppPageConfig<"/admin">
}

// Validate ../../src/app/dashboard/add-funds/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/add-funds/page.js")
  handler satisfies AppPageConfig<"/dashboard/add-funds">
}

// Validate ../../src/app/dashboard/bills/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/bills/page.js")
  handler satisfies AppPageConfig<"/dashboard/bills">
}

// Validate ../../src/app/dashboard/loans/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/loans/page.js")
  handler satisfies AppPageConfig<"/dashboard/loans">
}

// Validate ../../src/app/dashboard/my-card/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/my-card/page.js")
  handler satisfies AppPageConfig<"/dashboard/my-card">
}

// Validate ../../src/app/dashboard/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/page.js")
  handler satisfies AppPageConfig<"/dashboard">
}

// Validate ../../src/app/dashboard/receipt/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/receipt/page.js")
  handler satisfies AppPageConfig<"/dashboard/receipt">
}

// Validate ../../src/app/dashboard/settings/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/settings/page.js")
  handler satisfies AppPageConfig<"/dashboard/settings">
}

// Validate ../../src/app/dashboard/transactions/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/transactions/page.js")
  handler satisfies AppPageConfig<"/dashboard/transactions">
}

// Validate ../../src/app/dashboard/transfer/p2p/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/transfer/p2p/page.js")
  handler satisfies AppPageConfig<"/dashboard/transfer/p2p">
}

// Validate ../../src/app/dashboard/transfer/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/transfer/page.js")
  handler satisfies AppPageConfig<"/dashboard/transfer">
}

// Validate ../../src/app/dashboard/wallet-to-wallet/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/wallet-to-wallet/page.js")
  handler satisfies AppPageConfig<"/dashboard/wallet-to-wallet">
}

// Validate ../../src/app/dashboard/withdraw/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/withdraw/page.js")
  handler satisfies AppPageConfig<"/dashboard/withdraw">
}

// Validate ../../src/app/page.tsx
{
  const handler = {} as typeof import("../../src/app/page.js")
  handler satisfies AppPageConfig<"/">
}







// Validate ../../src/app/layout.tsx
{
  const handler = {} as typeof import("../../src/app/layout.js")
  handler satisfies LayoutConfig<"/">
}
