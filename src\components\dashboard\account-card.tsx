"use client"

import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Eye, Copy, Plus, Badge } from "lucide-react"
import { formatCurrency } from "@/lib/utils"
import { User } from "@/types"

interface AccountCardProps {
  user: User
}

export function AccountCard({ user }: AccountCardProps) {
  return (
    <div className="relative">
      {/* Background decorative elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 rounded-[25px]" />
      <div className="absolute top-0 right-0 w-32 h-32 bg-blue-500/20 rounded-full -translate-y-8 translate-x-8" />
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-blue-400/20 rounded-full translate-y-4 -translate-x-4" />
      <div className="absolute top-1/2 right-8 w-16 h-16 bg-yellow-400/30 rounded-full" />
      
      <Card className="relative bg-transparent border-none shadow-none text-white p-8">
        <div className="flex flex-col space-y-6">
          {/* Header with name and account number */}
          <div className="flex flex-col space-y-2">
            <h3 className="text-xl font-semibold">{user.name}</h3>
            <div className="flex items-center space-x-2">
              <span className="text-sm opacity-80">*** **** * {user.cardNumber.slice(-3)}</span>
              <Button variant="ghost" size="icon" className="h-6 w-6 text-white hover:bg-white/20">
                <Copy className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {/* Balance section */}
          <div className="flex flex-col space-y-2">
            <span className="text-sm opacity-80">Your balance</span>
            <div className="flex items-center space-x-2">
              <span className="text-2xl font-bold">{formatCurrency(user.balance)}</span>
              <Button variant="ghost" size="icon" className="h-6 w-6 text-white hover:bg-white/20">
                <Eye className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Add money button */}
          <div className="flex justify-end">
            <Button 
              variant="ghost" 
              size="icon" 
              className="h-10 w-10 bg-white/20 hover:bg-white/30 rounded-full"
            >
              <Plus className="h-5 w-5" />
            </Button>
          </div>
        </div>

        {/* Tier badge */}
        <div className="absolute bottom-6 right-6 flex items-center space-x-2 bg-white/20 rounded-full px-3 py-1">
          <Badge className="h-4 w-4 text-yellow-400" />
          <span className="text-sm font-medium">{user.tier}</span>
        </div>
      </Card>
    </div>
  )
}
