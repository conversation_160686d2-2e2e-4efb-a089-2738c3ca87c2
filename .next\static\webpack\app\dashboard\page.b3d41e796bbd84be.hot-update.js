"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/header */ \"(app-pages-browser)/./src/components/dashboard/header.tsx\");\n/* harmony import */ var _components_dashboard_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/sidebar */ \"(app-pages-browser)/./src/components/dashboard/sidebar.tsx\");\n/* harmony import */ var _components_dashboard_account_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/account-card */ \"(app-pages-browser)/./src/components/dashboard/account-card.tsx\");\n/* harmony import */ var _components_dashboard_statistics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/statistics */ \"(app-pages-browser)/./src/components/dashboard/statistics.tsx\");\n/* harmony import */ var _components_dashboard_recent_transactions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/recent-transactions */ \"(app-pages-browser)/./src/components/dashboard/recent-transactions.tsx\");\n/* harmony import */ var _components_dashboard_transfer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/transfer */ \"(app-pages-browser)/./src/components/dashboard/transfer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Mock data\nconst mockUser = {\n    id: \"1\",\n    name: \"James Bond\",\n    email: \"<EMAIL>\",\n    balance: 35673,\n    cardNumber: \"****************\",\n    cardHolder: \"James Bond\",\n    validThru: \"12/22\",\n    tier: \"Tier 1\"\n};\nconst mockTransactions = [\n    {\n        id: \"1\",\n        type: \"cash_in\",\n        amount: 850,\n        description: \"Cash in (Agent yusuff)\",\n        date: \"2021-01-28\",\n        status: \"completed\",\n        agent: \"Agent yusuff\"\n    },\n    {\n        id: \"2\",\n        type: \"cash_out\",\n        amount: 2500,\n        description: \"Cash out\",\n        date: \"2021-01-25\",\n        status: \"completed\"\n    },\n    {\n        id: \"3\",\n        type: \"transfer\",\n        amount: 5400,\n        description: \"Transfer\",\n        date: \"2021-01-21\",\n        status: \"completed\"\n    }\n];\nconst mockStatistics = [\n    {\n        label: \"Cash in\",\n        value: 30,\n        percentage: 30,\n        color: \"#1814F3\"\n    },\n    {\n        label: \"Loan\",\n        value: 20,\n        percentage: 20,\n        color: \"#16DBCC\"\n    },\n    {\n        label: \"Cash out\",\n        value: 15,\n        percentage: 15,\n        color: \"#FF82AC\"\n    },\n    {\n        label: \"Others\",\n        value: 35,\n        percentage: 35,\n        color: \"#FFBB38\"\n    }\n];\nfunction DashboardPage() {\n    _s();\n    const [activeMenuItem, setActiveMenuItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"dashboard\");\n    const handleMenuItemClick = (item)=>{\n        setActiveMenuItem(item);\n        // Navigate to different pages based on menu item\n        switch(item){\n            case 'transfer':\n                window.location.href = '/dashboard/transfer';\n                break;\n            case 'withdraw':\n                window.location.href = '/dashboard/withdraw';\n                break;\n            case 'add-funds':\n                window.location.href = '/dashboard/add-funds';\n                break;\n            case 'bills':\n                window.location.href = '/dashboard/bills';\n                break;\n            case 'loans':\n                window.location.href = '/dashboard/loans';\n                break;\n            case 'settings':\n                window.location.href = '/dashboard/settings';\n                break;\n            default:\n                break;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-[#F5F7FA]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                activeItem: activeMenuItem,\n                onItemClick: handleMenuItemClick\n            }, void 0, false, {\n                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_header__WEBPACK_IMPORTED_MODULE_2__.Header, {\n                        title: \"Overview\",\n                        userName: mockUser.name\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-auto p-4 md:p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-12 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-8 space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_account_card__WEBPACK_IMPORTED_MODULE_4__.AccountCard, {\n                                            user: mockUser\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_recent_transactions__WEBPACK_IMPORTED_MODULE_6__.RecentTransactions, {\n                                            transactions: mockTransactions\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-4 space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_transfer__WEBPACK_IMPORTED_MODULE_7__.Transfer, {}, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_statistics__WEBPACK_IMPORTED_MODULE_5__.Statistics, {\n                                            data: mockStatistics\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"rXZr4XtoNSi2YkrftYtC3LNNsX8=\");\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});