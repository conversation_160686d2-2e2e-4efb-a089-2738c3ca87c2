(()=>{var a={};a.id=698,a.ids=[698],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1829:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.last=function(a){return a[a.length-1]}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7107:()=>{},9069:(a,b,c)=>{Promise.resolve().then(c.bind(c,55013))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18769:(a,b,c)=>{a.exports=c(21450).last},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21450:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(1829),e=c(91143),f=c(77542);b.last=function(a){if(f.isArrayLike(a))return d.last(e.toArray(a))}},26589:(a,b,c)=>{"use strict";c.d(b,{Y:()=>k});var d=c(21124),e=c(93758),f=c(35284),g=c(88285),h=c(47268),i=c(94684),j=c(24515);function k({title:a,userAvatar:b,userName:c}){return(0,d.jsx)("header",{className:"bg-white border-b border-gray-200 px-4 md:px-8 py-4 md:py-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("h1",{className:"text-xl md:text-2xl font-semibold text-[#343C6A]",children:a}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-6",children:[(0,d.jsxs)("div",{className:"relative hidden md:block",children:[(0,d.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,d.jsx)(e.p,{placeholder:"Search for something",className:"pl-10 w-60 lg:w-80 bg-[#F5F7FA] border-none"})]}),(0,d.jsxs)(f.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,d.jsx)(h.A,{className:"h-5 w-5 text-[#718EBF]"}),(0,d.jsx)("span",{className:"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"})]}),(0,d.jsx)(f.$,{variant:"ghost",size:"icon",children:(0,d.jsx)(i.A,{className:"h-5 w-5 text-[#718EBF]"})}),(0,d.jsx)("div",{className:"flex items-center space-x-3",children:(0,d.jsx)("div",{className:"w-10 h-10 rounded-full overflow-hidden bg-gray-200",children:b?(0,d.jsx)(j.default,{src:b,alt:c||"User",width:40,height:40,className:"w-full h-full object-cover"}):(0,d.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center text-white font-semibold",children:c?.charAt(0)||"U"})})})]})]})})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29643:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,54160,23)),Promise.resolve().then(c.t.bind(c,31603,23)),Promise.resolve().then(c.t.bind(c,68495,23)),Promise.resolve().then(c.t.bind(c,75170,23)),Promise.resolve().then(c.t.bind(c,77526,23)),Promise.resolve().then(c.t.bind(c,78922,23)),Promise.resolve().then(c.t.bind(c,29234,23)),Promise.resolve().then(c.t.bind(c,12263,23)),Promise.resolve().then(c.bind(c,82146))},33873:a=>{"use strict";a.exports=require("path")},35002:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,88699)),"D:\\workspace\\.typescript\\aetrustfrontend\\src\\app\\admin\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,51472)),"D:\\workspace\\.typescript\\aetrustfrontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,87028,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,I=["D:\\workspace\\.typescript\\aetrustfrontend\\src\\app\\admin\\page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/admin/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},35284:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(21124),e=c(38301),f=c(26691),g=c(44943);let h=(0,f.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-[#1814F3] text-white shadow hover:bg-[#1814F3]/90",destructive:"bg-red-500 text-white shadow-sm hover:bg-red-500/90",outline:"border border-gray-200 bg-white shadow-sm hover:bg-gray-50 hover:text-gray-900",secondary:"bg-gray-100 text-gray-900 shadow-sm hover:bg-gray-100/80",ghost:"hover:bg-gray-100 hover:text-gray-900",link:"text-[#1814F3] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),i=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...f},i)=>(0,d.jsx)("button",{className:(0,g.cn)(h({variant:b,size:c,className:a})),ref:i,...f}));i.displayName="Button"},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44943:(a,b,c)=>{"use strict";c.d(b,{Yq:()=>h,cn:()=>f,sL:()=>i,vv:()=>g});var d=c(43249),e=c(58829);function f(...a){return(0,e.QP)((0,d.$)(a))}function g(a){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a)}function h(a){return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric"}).format(new Date(a))}function i(a){return new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric"}).format(new Date(a))}},51472:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(75338),e=c(94294),f=c.n(e),g=c(81464),h=c.n(g);c(61135);let i={title:"Create Next App",description:"Generated by create next app"};function j({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:a})})}},55013:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>c1});var d=c(21124),e=c(26589),f=c(62186),g=c(23339);let h=(0,g.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),i=(0,g.A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]);var j=c(65893);let k=(0,g.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]),l=(0,g.A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);var m=c(14343),n=c(44943);function o({title:a,value:b,change:c,icon:e,color:g="blue"}){let j=c&&c>0;return(0,d.jsx)(f.Zp,{className:"bg-white rounded-[25px] border-none shadow-lg",children:(0,d.jsx)(f.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,d.jsx)("span",{className:"text-sm text-[#718EBF] font-medium",children:a}),(0,d.jsx)("span",{className:"text-2xl font-bold text-[#232323]",children:"number"==typeof b?(0,n.vv)(b):b}),c&&(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[j?(0,d.jsx)(h,{className:"h-4 w-4 text-green-500"}):(0,d.jsx)(i,{className:"h-4 w-4 text-red-500"}),(0,d.jsxs)("span",{className:`text-sm font-medium ${j?"text-green-500":"text-red-500"}`,children:[Math.abs(c),"%"]})]})]}),(0,d.jsx)("div",{className:`p-4 rounded-full ${{blue:"bg-blue-100 text-blue-600",green:"bg-green-100 text-green-600",orange:"bg-orange-100 text-orange-600",purple:"bg-purple-100 text-purple-600"}[g]}`,children:e})]})})})}function p({totalUsers:a,totalTransactions:b,totalRevenue:c,activeAgents:e}){return(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,d.jsx)(o,{title:"Total Users",value:a.toLocaleString(),change:12.5,icon:(0,d.jsx)(j.A,{className:"h-6 w-6"}),color:"blue"}),(0,d.jsx)(o,{title:"Total Transactions",value:b.toLocaleString(),change:8.2,icon:(0,d.jsx)(k,{className:"h-6 w-6"}),color:"green"}),(0,d.jsx)(o,{title:"Total Revenue",value:c,change:-2.1,icon:(0,d.jsx)(l,{className:"h-6 w-6"}),color:"orange"}),(0,d.jsx)(o,{title:"Active Agents",value:e.toLocaleString(),change:5.7,icon:(0,d.jsx)(m.A,{className:"h-6 w-6"}),color:"purple"})]})}var q=c(6077),r=c(38301),s=c(29072),t=c(35538),u=c(18189),v=c(20853),w=c(11267),x=c(20888),y=c(65764),z=c(53053),A=["width","height"];function B(){return(B=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var C={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},D=(0,r.forwardRef)(function(a,b){var c,d=(0,y.e)(a.categoricalChartProps,C),{width:e,height:f}=d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(d,A);if(!(0,z.F)(e)||!(0,z.F)(f))return null;var{chartName:h,defaultTooltipEventType:i,validateTooltipEventTypes:j,tooltipPayloadSearcher:k,categoricalChartProps:l}=a;return r.createElement(t.J,{preloadedState:{options:{chartName:h,defaultTooltipEventType:i,validateTooltipEventTypes:j,tooltipPayloadSearcher:k,eventEmitter:void 0}},reduxStoreName:null!=(c=l.id)?c:h},r.createElement(u.TK,{chartData:l.data}),r.createElement(v.s,{width:e,height:f,layout:d.layout,margin:d.margin}),r.createElement(w.p,{accessibilityLayer:d.accessibilityLayer,barCategoryGap:d.barCategoryGap,maxBarSize:d.maxBarSize,stackOffset:d.stackOffset,barGap:d.barGap,barSize:d.barSize,syncId:d.syncId,syncMethod:d.syncMethod,className:d.className}),r.createElement(x.L,B({},g,{width:e,height:f,ref:b})))}),E=["axis","item"],F=(0,r.forwardRef)((a,b)=>r.createElement(D,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:E,tooltipPayloadSearcher:s.uN,categoricalChartProps:a,ref:b})),G=c(64214),H=c(22688),I=c(79241),J=c(98700),K=c(4702);class L{static create(a){return new L(a)}constructor(a){this.scale=a}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(a){var{bandAware:b,position:c}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==a){if(c)switch(c){case"start":default:return this.scale(a);case"middle":var d=this.bandwidth?this.bandwidth()/2:0;return this.scale(a)+d;case"end":var e=this.bandwidth?this.bandwidth():0;return this.scale(a)+e}if(b){var f=this.bandwidth?this.bandwidth()/2:0;return this.scale(a)+f}return this.scale(a)}}isInRange(a){var b=this.range(),c=b[0],d=b[b.length-1];return c<=d?a>=c&&a<=d:a>=d&&a<=c}}!function(a,b,c){var d;(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:1e-4,enumerable:!0,configurable:!0,writable:!0}):a[b]=1e-4}(L,"EPS",1e-4);var M=function(a){var{width:b,height:c}=a,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,e=(d%180+180)%180*Math.PI/180,f=Math.atan(c/b);return Math.abs(e>f&&e<Math.PI-f?c/Math.sin(e):b/Math.cos(e))};function N(a,b,c){if(b<1)return[];if(1===b&&void 0===c)return a;for(var d=[],e=0;e<a.length;e+=b)if(void 0!==c&&!0!==c(a[e]))return;else d.push(a[e]);return d}function O(a,b,c,d,e){if(a*b<a*d||a*b>a*e)return!1;var f=c();return a*(b-a*f/2-d)>=0&&a*(b+a*f/2-e)<=0}function P(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function Q(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?P(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):P(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function R(a,b,c){var d,{tick:e,ticks:f,viewBox:g,minTickGap:h,orientation:i,interval:j,tickFormatter:k,unit:l,angle:m}=a;if(!f||!f.length||!e)return[];if((0,H.Et)(j)||K.m.isSsr)return null!=(d=N(f,((0,H.Et)(j)?j:0)+1))?d:[];var n="top"===i||"bottom"===i?"width":"height",o=l&&"width"===n?(0,J.Pu)(l,{fontSize:b,letterSpacing:c}):{width:0,height:0},p=(a,d)=>{var e,f="function"==typeof k?k(a.value,d):a.value;return"width"===n?(e=(0,J.Pu)(f,{fontSize:b,letterSpacing:c}),M({width:e.width+o.width,height:e.height+o.height},m)):(0,J.Pu)(f,{fontSize:b,letterSpacing:c})[n]},q=f.length>=2?(0,H.sA)(f[1].coordinate-f[0].coordinate):1,r=function(a,b,c){var d="width"===c,{x:e,y:f,width:g,height:h}=a;return 1===b?{start:d?e:f,end:d?e+g:f+h}:{start:d?e+g:f+h,end:d?e:f}}(g,q,n);return"equidistantPreserveStart"===j?function(a,b,c,d,e){for(var f,g=(d||[]).slice(),{start:h,end:i}=b,j=0,k=1,l=h;k<=g.length;)if(f=function(){var b,f=null==d?void 0:d[j];if(void 0===f)return{v:N(d,k)};var g=j,m=()=>(void 0===b&&(b=c(f,g)),b),n=f.coordinate,o=0===j||O(a,n,m,l,i);o||(j=0,l=h,k+=1),o&&(l=n+a*(m()/2+e),j+=k)}())return f.v;return[]}(q,r,p,f,h):("preserveStart"===j||"preserveStartEnd"===j?function(a,b,c,d,e,f){var g=(d||[]).slice(),h=g.length,{start:i,end:j}=b;if(f){var k=d[h-1],l=c(k,h-1),m=a*(k.coordinate+a*l/2-j);g[h-1]=k=Q(Q({},k),{},{tickCoord:m>0?k.coordinate-m*a:k.coordinate}),O(a,k.tickCoord,()=>l,i,j)&&(j=k.tickCoord-a*(l/2+e),g[h-1]=Q(Q({},k),{},{isShow:!0}))}for(var n=f?h-1:h,o=function(b){var d,f=g[b],h=()=>(void 0===d&&(d=c(f,b)),d);if(0===b){var k=a*(f.coordinate-a*h()/2-i);g[b]=f=Q(Q({},f),{},{tickCoord:k<0?f.coordinate-k*a:f.coordinate})}else g[b]=f=Q(Q({},f),{},{tickCoord:f.coordinate});O(a,f.tickCoord,h,i,j)&&(i=f.tickCoord+a*(h()/2+e),g[b]=Q(Q({},f),{},{isShow:!0}))},p=0;p<n;p++)o(p);return g}(q,r,p,f,h,"preserveStartEnd"===j):function(a,b,c,d,e){for(var f=(d||[]).slice(),g=f.length,{start:h}=b,{end:i}=b,j=function(b){var d,j=f[b],k=()=>(void 0===d&&(d=c(j,b)),d);if(b===g-1){var l=a*(j.coordinate+a*k()/2-i);f[b]=j=Q(Q({},j),{},{tickCoord:l>0?j.coordinate-l*a:j.coordinate})}else f[b]=j=Q(Q({},j),{},{tickCoord:j.coordinate});O(a,j.tickCoord,k,h,i)&&(i=j.tickCoord-a*(k()/2+e),f[b]=Q(Q({},j),{},{isShow:!0}))},k=g-1;k>=0;k--)j(k);return f}(q,r,p,f,h)).filter(a=>a.isShow)}var S=c(37177),T=c.n(S),U=c(43249);function V(a,b){for(var c in a)if(({}).hasOwnProperty.call(a,c)&&(!({}).hasOwnProperty.call(b,c)||a[c]!==b[c]))return!1;for(var d in b)if(({}).hasOwnProperty.call(b,d)&&!({}).hasOwnProperty.call(a,d))return!1;return!0}var W=c(55413),X=c(81249),Y=c(72677),Z=c(29160),$=c(15379),_=c(56998),aa=c(65168),ab=["offset"],ac=["labelRef"];function ad(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function ae(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function af(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?ae(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):ae(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function ag(){return(ag=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var ah=a=>null!=a&&"function"==typeof a;function ai(a){var b,{offset:c=5}=a,d=af({offset:c},ad(a,ab)),{viewBox:e,position:f,value:g,children:h,content:i,className:j="",textBreakAll:k,labelRef:l}=d,m=(0,_.G)(aa.D0),n=(0,$.sk)(),o=e||("center"===f?n:null!=m?m:n);if(!o||(0,H.uy)(g)&&(0,H.uy)(h)&&!(0,r.isValidElement)(i)&&"function"!=typeof i)return null;var p=af(af({},d),{},{viewBox:o});if((0,r.isValidElement)(i)){var{labelRef:q}=p,s=ad(p,ac);return(0,r.cloneElement)(i,s)}if("function"==typeof i){if(b=(0,r.createElement)(i,p),(0,r.isValidElement)(b))return b}else b=(a=>{var{value:b,formatter:c}=a,d=(0,H.uy)(a.children)?b:a.children;return"function"==typeof c?c(d):d})(d);var t="cx"in o&&(0,H.Et)(o.cx),u=(0,Y.J9)(d,!0);if(t&&("insideStart"===f||"insideEnd"===f||"end"===f))return((a,b,c,d)=>{let e,f;var g,h,{position:i,offset:j,className:k}=a,{cx:l,cy:m,innerRadius:n,outerRadius:o,startAngle:p,endAngle:q,clockWise:s}=d,t=(n+o)/2,u=(e=p,f=q,(0,H.sA)(f-e)*Math.min(Math.abs(f-e),360)),v=u>=0?1:-1;"insideStart"===i?(g=p+v*j,h=s):"insideEnd"===i?(g=q-v*j,h=!s):"end"===i&&(g=q+v*j,h=s),h=u<=0?h:!h;var w=(0,Z.IZ)(l,m,t,g),x=(0,Z.IZ)(l,m,t,g+(h?1:-1)*359),y="M".concat(w.x,",").concat(w.y,"\n    A").concat(t,",").concat(t,",0,1,").concat(+!h,",\n    ").concat(x.x,",").concat(x.y),z=(0,H.uy)(a.id)?(0,H.NF)("recharts-radial-line-"):a.id;return r.createElement("text",ag({},c,{dominantBaseline:"central",className:(0,U.$)("recharts-radial-bar-label",k)}),r.createElement("defs",null,r.createElement("path",{id:z,d:y})),r.createElement("textPath",{xlinkHref:"#".concat(z)},b))})(d,b,u,o);var v=t?((a,b,c)=>{var{cx:d,cy:e,innerRadius:f,outerRadius:g,startAngle:h,endAngle:i}=a,j=(h+i)/2;if("outside"===c){var{x:k,y:l}=(0,Z.IZ)(d,e,g+b,j);return{x:k,y:l,textAnchor:k>=d?"start":"end",verticalAnchor:"middle"}}if("center"===c)return{x:d,y:e,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===c)return{x:d,y:e,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===c)return{x:d,y:e,textAnchor:"middle",verticalAnchor:"end"};var{x:m,y:n}=(0,Z.IZ)(d,e,(f+g)/2,j);return{x:m,y:n,textAnchor:"middle",verticalAnchor:"middle"}})(o,d.offset,d.position):((a,b)=>{var{parentViewBox:c,offset:d,position:e}=a,{x:f,y:g,width:h,height:i}=b,j=i>=0?1:-1,k=j*d,l=j>0?"end":"start",m=j>0?"start":"end",n=h>=0?1:-1,o=n*d,p=n>0?"end":"start",q=n>0?"start":"end";if("top"===e)return af(af({},{x:f+h/2,y:g-j*d,textAnchor:"middle",verticalAnchor:l}),c?{height:Math.max(g-c.y,0),width:h}:{});if("bottom"===e)return af(af({},{x:f+h/2,y:g+i+k,textAnchor:"middle",verticalAnchor:m}),c?{height:Math.max(c.y+c.height-(g+i),0),width:h}:{});if("left"===e){var r={x:f-o,y:g+i/2,textAnchor:p,verticalAnchor:"middle"};return af(af({},r),c?{width:Math.max(r.x-c.x,0),height:i}:{})}if("right"===e){var s={x:f+h+o,y:g+i/2,textAnchor:q,verticalAnchor:"middle"};return af(af({},s),c?{width:Math.max(c.x+c.width-s.x,0),height:i}:{})}var t=c?{width:h,height:i}:{};return"insideLeft"===e?af({x:f+o,y:g+i/2,textAnchor:q,verticalAnchor:"middle"},t):"insideRight"===e?af({x:f+h-o,y:g+i/2,textAnchor:p,verticalAnchor:"middle"},t):"insideTop"===e?af({x:f+h/2,y:g+k,textAnchor:"middle",verticalAnchor:m},t):"insideBottom"===e?af({x:f+h/2,y:g+i-k,textAnchor:"middle",verticalAnchor:l},t):"insideTopLeft"===e?af({x:f+o,y:g+k,textAnchor:q,verticalAnchor:m},t):"insideTopRight"===e?af({x:f+h-o,y:g+k,textAnchor:p,verticalAnchor:m},t):"insideBottomLeft"===e?af({x:f+o,y:g+i-k,textAnchor:q,verticalAnchor:l},t):"insideBottomRight"===e?af({x:f+h-o,y:g+i-k,textAnchor:p,verticalAnchor:l},t):e&&"object"==typeof e&&((0,H.Et)(e.x)||(0,H._3)(e.x))&&((0,H.Et)(e.y)||(0,H._3)(e.y))?af({x:f+(0,H.F4)(e.x,h),y:g+(0,H.F4)(e.y,i),textAnchor:"end",verticalAnchor:"end"},t):af({x:f+h/2,y:g+i/2,textAnchor:"middle",verticalAnchor:"middle"},t)})(d,o);return r.createElement(X.E,ag({ref:l,className:(0,U.$)("recharts-label",j)},u,v,{breakAll:k}),b)}ai.displayName="Label";var aj=a=>{var{cx:b,cy:c,angle:d,startAngle:e,endAngle:f,r:g,radius:h,innerRadius:i,outerRadius:j,x:k,y:l,top:m,left:n,width:o,height:p,clockWise:q,labelViewBox:r}=a;if(r)return r;if((0,H.Et)(o)&&(0,H.Et)(p)){if((0,H.Et)(k)&&(0,H.Et)(l))return{x:k,y:l,width:o,height:p};if((0,H.Et)(m)&&(0,H.Et)(n))return{x:m,y:n,width:o,height:p}}return(0,H.Et)(k)&&(0,H.Et)(l)?{x:k,y:l,width:0,height:0}:(0,H.Et)(b)&&(0,H.Et)(c)?{cx:b,cy:c,startAngle:e||d||0,endAngle:f||d||0,innerRadius:i||0,outerRadius:j||h||g||0,clockWise:q}:a.viewBox?a.viewBox:void 0};ai.parseViewBox=aj,ai.renderCallByParent=function(a,b){var c=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!a||!a.children&&c&&!a.label)return null;var{children:d,labelRef:e}=a,f=aj(a),g=(0,Y.aS)(d,ai).map((a,c)=>(0,r.cloneElement)(a,{viewBox:b||f,key:"label-".concat(c)}));return c?[((a,b,c)=>{if(!a)return null;var d={viewBox:b,labelRef:c};return!0===a?r.createElement(ai,ag({key:"label-implicit"},d)):(0,H.vh)(a)?r.createElement(ai,ag({key:"label-implicit",value:a},d)):(0,r.isValidElement)(a)?a.type===ai?(0,r.cloneElement)(a,af({key:"label-implicit"},d)):r.createElement(ai,ag({key:"label-implicit",content:a},d)):ah(a)?r.createElement(ai,ag({key:"label-implicit",content:a},d)):a&&"object"==typeof a?r.createElement(ai,ag({},a,{key:"label-implicit"},d)):null})(a.label,b||f,e),...g]:g};var ak=c(61188),al=c(41800),am=["viewBox"],an=["viewBox"];function ao(){return(ao=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function ap(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function aq(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?ap(Object(c),!0).forEach(function(b){as(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):ap(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function ar(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function as(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class at extends r.Component{constructor(a){super(a),this.tickRefs=r.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(a,b){var{viewBox:c}=a,d=ar(a,am),e=this.props,{viewBox:f}=e,g=ar(e,an);return!V(c,f)||!V(d,g)||!V(b,this.state)}getTickLineCoord(a){var b,c,d,e,f,g,{x:h,y:i,width:j,height:k,orientation:l,tickSize:m,mirror:n,tickMargin:o}=this.props,p=n?-1:1,q=a.tickSize||m,r=(0,H.Et)(a.tickCoord)?a.tickCoord:a.coordinate;switch(l){case"top":b=c=a.coordinate,g=(d=(e=i+!n*k)-p*q)-p*o,f=r;break;case"left":d=e=a.coordinate,f=(b=(c=h+!n*j)-p*q)-p*o,g=r;break;case"right":d=e=a.coordinate,f=(b=(c=h+n*j)+p*q)+p*o,g=r;break;default:b=c=a.coordinate,g=(d=(e=i+n*k)+p*q)+p*o,f=r}return{line:{x1:b,y1:d,x2:c,y2:e},tick:{x:f,y:g}}}getTickTextAnchor(){var a,{orientation:b,mirror:c}=this.props;switch(b){case"left":a=c?"start":"end";break;case"right":a=c?"end":"start";break;default:a="middle"}return a}getTickVerticalAnchor(){var{orientation:a,mirror:b}=this.props;switch(a){case"left":case"right":return"middle";case"top":return b?"start":"end";default:return b?"end":"start"}}renderAxisLine(){var{x:a,y:b,width:c,height:d,orientation:e,mirror:f,axisLine:g}=this.props,h=aq(aq(aq({},(0,Y.J9)(this.props,!1)),(0,Y.J9)(g,!1)),{},{fill:"none"});if("top"===e||"bottom"===e){var i=+("top"===e&&!f||"bottom"===e&&f);h=aq(aq({},h),{},{x1:a,y1:b+i*d,x2:a+c,y2:b+i*d})}else{var j=+("left"===e&&!f||"right"===e&&f);h=aq(aq({},h),{},{x1:a+j*c,y1:b,x2:a+j*c,y2:b+d})}return r.createElement("line",ao({},h,{className:(0,U.$)("recharts-cartesian-axis-line",T()(g,"className"))}))}static renderTickItem(a,b,c){var d,e=(0,U.$)(b.className,"recharts-cartesian-axis-tick-value");if(r.isValidElement(a))d=r.cloneElement(a,aq(aq({},b),{},{className:e}));else if("function"==typeof a)d=a(aq(aq({},b),{},{className:e}));else{var f="recharts-cartesian-axis-tick-value";"boolean"!=typeof a&&(f=(0,U.$)(f,a.className)),d=r.createElement(X.E,ao({},b,{className:f}),c)}return d}renderTicks(a,b){var c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:d,stroke:e,tick:f,tickFormatter:g,unit:h,padding:i}=this.props,j=R(aq(aq({},this.props),{},{ticks:c}),a,b),k=this.getTickTextAnchor(),l=this.getTickVerticalAnchor(),m=(0,al.u)(this.props),n=(0,Y.J9)(f,!1),o=aq(aq({},m),{},{fill:"none"},(0,Y.J9)(d,!1)),p=j.map((a,b)=>{var{line:c,tick:p}=this.getTickLineCoord(a),q=aq(aq(aq(aq({textAnchor:k,verticalAnchor:l},m),{},{stroke:"none",fill:e},n),p),{},{index:b,payload:a,visibleTicksCount:j.length,tickFormatter:g,padding:i});return r.createElement(W.W,ao({className:"recharts-cartesian-axis-tick",key:"tick-".concat(a.value,"-").concat(a.coordinate,"-").concat(a.tickCoord)},(0,ak.XC)(this.props,a,b)),d&&r.createElement("line",ao({},o,c,{className:(0,U.$)("recharts-cartesian-axis-tick-line",T()(d,"className"))})),f&&at.renderTickItem(f,q,"".concat("function"==typeof g?g(a.value,b):a.value).concat(h||"")))});return p.length>0?r.createElement("g",{className:"recharts-cartesian-axis-ticks"},p):null}render(){var{axisLine:a,width:b,height:c,className:d,hide:e}=this.props;if(e)return null;var{ticks:f}=this.props;return null!=b&&b<=0||null!=c&&c<=0?null:r.createElement(W.W,{className:(0,U.$)("recharts-cartesian-axis",d),ref:a=>{if(a){var b=a.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(b);var c=b[0];if(c){var d=window.getComputedStyle(c).fontSize,e=window.getComputedStyle(c).letterSpacing;(d!==this.state.fontSize||e!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(c).fontSize,letterSpacing:window.getComputedStyle(c).letterSpacing})}}}},a&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,f),ai.renderCallByParent(this.props))}}as(at,"displayName","CartesianAxis"),as(at,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var au=c(60343),av=c(53530),aw=["x1","y1","x2","y2","key"],ax=["offset"],ay=["xAxisId","yAxisId"],az=["xAxisId","yAxisId"];function aA(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function aB(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?aA(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):aA(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function aC(){return(aC=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function aD(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var aE=a=>{var{fill:b}=a;if(!b||"none"===b)return null;var{fillOpacity:c,x:d,y:e,width:f,height:g,ry:h}=a;return r.createElement("rect",{x:d,y:e,ry:h,width:f,height:g,stroke:"none",fill:b,fillOpacity:c,className:"recharts-cartesian-grid-bg"})};function aF(a,b){var c;if(r.isValidElement(a))c=r.cloneElement(a,b);else if("function"==typeof a)c=a(b);else{var{x1:d,y1:e,x2:f,y2:g,key:h}=b,i=aD(b,aw),j=(0,al.u)(i),{offset:k}=j,l=aD(j,ax);c=r.createElement("line",aC({},l,{x1:d,y1:e,x2:f,y2:g,fill:"none",key:h}))}return c}function aG(a){var{x:b,width:c,horizontal:d=!0,horizontalPoints:e}=a;if(!d||!e||!e.length)return null;var{xAxisId:f,yAxisId:g}=a,h=aD(a,ay),i=e.map((a,e)=>aF(d,aB(aB({},h),{},{x1:b,y1:a,x2:b+c,y2:a,key:"line-".concat(e),index:e})));return r.createElement("g",{className:"recharts-cartesian-grid-horizontal"},i)}function aH(a){var{y:b,height:c,vertical:d=!0,verticalPoints:e}=a;if(!d||!e||!e.length)return null;var{xAxisId:f,yAxisId:g}=a,h=aD(a,az),i=e.map((a,e)=>aF(d,aB(aB({},h),{},{x1:a,y1:b,x2:a,y2:b+c,key:"line-".concat(e),index:e})));return r.createElement("g",{className:"recharts-cartesian-grid-vertical"},i)}function aI(a){var{horizontalFill:b,fillOpacity:c,x:d,y:e,width:f,height:g,horizontalPoints:h,horizontal:i=!0}=a;if(!i||!b||!b.length)return null;var j=h.map(a=>Math.round(a+e-e)).sort((a,b)=>a-b);e!==j[0]&&j.unshift(0);var k=j.map((a,h)=>{var i=j[h+1]?j[h+1]-a:e+g-a;if(i<=0)return null;var k=h%b.length;return r.createElement("rect",{key:"react-".concat(h),y:a,x:d,height:i,width:f,stroke:"none",fill:b[k],fillOpacity:c,className:"recharts-cartesian-grid-bg"})});return r.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},k)}function aJ(a){var{vertical:b=!0,verticalFill:c,fillOpacity:d,x:e,y:f,width:g,height:h,verticalPoints:i}=a;if(!b||!c||!c.length)return null;var j=i.map(a=>Math.round(a+e-e)).sort((a,b)=>a-b);e!==j[0]&&j.unshift(0);var k=j.map((a,b)=>{var i=j[b+1]?j[b+1]-a:e+g-a;if(i<=0)return null;var k=b%c.length;return r.createElement("rect",{key:"react-".concat(b),x:a,y:f,width:i,height:h,stroke:"none",fill:c[k],fillOpacity:d,className:"recharts-cartesian-grid-bg"})});return r.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},k)}var aK=(a,b)=>{var{xAxis:c,width:d,height:e,offset:f}=a;return(0,I.PW)(R(aB(aB(aB({},at.defaultProps),c),{},{ticks:(0,I.Rh)(c,!0),viewBox:{x:0,y:0,width:d,height:e}})),f.left,f.left+f.width,b)},aL=(a,b)=>{var{yAxis:c,width:d,height:e,offset:f}=a;return(0,I.PW)(R(aB(aB(aB({},at.defaultProps),c),{},{ticks:(0,I.Rh)(c,!0),viewBox:{x:0,y:0,width:d,height:e}})),f.top,f.top+f.height,b)},aM={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function aN(a){var b=(0,$.yi)(),c=(0,$.rY)(),d=(0,$.W7)(),e=aB(aB({},(0,y.e)(a,aM)),{},{x:(0,H.Et)(a.x)?a.x:d.left,y:(0,H.Et)(a.y)?a.y:d.top,width:(0,H.Et)(a.width)?a.width:d.width,height:(0,H.Et)(a.height)?a.height:d.height}),{xAxisId:f,yAxisId:g,x:h,y:i,width:j,height:k,syncWithTicks:l,horizontalValues:m,verticalValues:n}=e,o=(0,av.r)(),p=(0,_.G)(a=>(0,au.ZB)(a,"xAxis",f,o)),q=(0,_.G)(a=>(0,au.ZB)(a,"yAxis",g,o));if(!(0,H.Et)(j)||j<=0||!(0,H.Et)(k)||k<=0||!(0,H.Et)(h)||h!==+h||!(0,H.Et)(i)||i!==+i)return null;var s=e.verticalCoordinatesGenerator||aK,t=e.horizontalCoordinatesGenerator||aL,{horizontalPoints:u,verticalPoints:v}=e;if((!u||!u.length)&&"function"==typeof t){var w=m&&m.length,x=t({yAxis:q?aB(aB({},q),{},{ticks:w?m:q.ticks}):void 0,width:b,height:c,offset:d},!!w||l);(0,G.R)(Array.isArray(x),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof x,"]")),Array.isArray(x)&&(u=x)}if((!v||!v.length)&&"function"==typeof s){var z=n&&n.length,A=s({xAxis:p?aB(aB({},p),{},{ticks:z?n:p.ticks}):void 0,width:b,height:c,offset:d},!!z||l);(0,G.R)(Array.isArray(A),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof A,"]")),Array.isArray(A)&&(v=A)}return r.createElement("g",{className:"recharts-cartesian-grid"},r.createElement(aE,{fill:e.fill,fillOpacity:e.fillOpacity,x:e.x,y:e.y,width:e.width,height:e.height,ry:e.ry}),r.createElement(aI,aC({},e,{horizontalPoints:u})),r.createElement(aJ,aC({},e,{verticalPoints:v})),r.createElement(aG,aC({},e,{offset:d,horizontalPoints:u,xAxis:p,yAxis:q})),r.createElement(aH,aC({},e,{offset:d,verticalPoints:v,xAxis:p,yAxis:q})))}aN.displayName="CartesianGrid";var aO=c(55111),aP=c(92173),aQ=["children"],aR=["dangerouslySetInnerHTML","ticks"];function aS(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function aT(){return(aT=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function aU(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function aV(a){(0,_.j)();var b=(0,r.useMemo)(()=>{var{children:b}=a;return aU(a,aQ)},[a]),c=(0,_.G)(a=>(0,au.Rl)(a,b.id));return b===c?a.children:null}var aW=a=>{var{xAxisId:b,className:c}=a,d=(0,_.G)(aP.c2),e=(0,av.r)(),f="xAxis",g=(0,_.G)(a=>(0,au.iV)(a,f,b,e)),h=(0,_.G)(a=>(0,au.Zi)(a,f,b,e)),i=(0,_.G)(a=>(0,au.Lw)(a,b)),j=(0,_.G)(a=>(0,au.L$)(a,b));if(null==i||null==j)return null;var{dangerouslySetInnerHTML:k,ticks:l}=a,m=aU(a,aR);return r.createElement(at,aT({},m,{scale:g,x:j.x,y:j.y,width:i.width,height:i.height,className:(0,U.$)("recharts-".concat(f," ").concat(f),c),viewBox:d,ticks:h}))},aX=a=>{var b,c,d,e,f;return r.createElement(aV,{interval:null!=(b=a.interval)?b:"preserveEnd",id:a.xAxisId,scale:a.scale,type:a.type,padding:a.padding,allowDataOverflow:a.allowDataOverflow,domain:a.domain,dataKey:a.dataKey,allowDuplicatedCategory:a.allowDuplicatedCategory,allowDecimals:a.allowDecimals,tickCount:a.tickCount,includeHidden:null!=(c=a.includeHidden)&&c,reversed:a.reversed,ticks:a.ticks,height:a.height,orientation:a.orientation,mirror:a.mirror,hide:a.hide,unit:a.unit,name:a.name,angle:null!=(d=a.angle)?d:0,minTickGap:null!=(e=a.minTickGap)?e:5,tick:null==(f=a.tick)||f,tickFormatter:a.tickFormatter},r.createElement(aW,a))};class aY extends r.Component{render(){return r.createElement(aX,this.props)}}aS(aY,"displayName","XAxis"),aS(aY,"defaultProps",{allowDataOverflow:au.PU.allowDataOverflow,allowDecimals:au.PU.allowDecimals,allowDuplicatedCategory:au.PU.allowDuplicatedCategory,height:au.PU.height,hide:!1,mirror:au.PU.mirror,orientation:au.PU.orientation,padding:au.PU.padding,reversed:au.PU.reversed,scale:au.PU.scale,tickCount:au.PU.tickCount,type:au.PU.type,xAxisId:0});var aZ=["dangerouslySetInnerHTML","ticks"];function a$(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function a_(){return(a_=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function a0(a){return(0,_.j)(),null}var a1=a=>{var b,{yAxisId:c,className:d,width:e,label:f}=a,g=(0,r.useRef)(null),h=(0,r.useRef)(null),i=(0,_.G)(aP.c2),j=(0,av.r)(),k=(0,_.j)(),l="yAxis",m=(0,_.G)(a=>(0,au.iV)(a,l,c,j)),n=(0,_.G)(a=>(0,au.wP)(a,c)),o=(0,_.G)(a=>(0,au.KR)(a,c)),p=(0,_.G)(a=>(0,au.Zi)(a,l,c,j));if((0,r.useLayoutEffect)(()=>{if(!("auto"!==e||!n||ah(f)||(0,r.isValidElement)(f))){var a,b=g.current,d=null==b||null==(a=b.tickRefs)?void 0:a.current,{tickSize:i,tickMargin:j}=b.props,l=(a=>{var{ticks:b,label:c,labelGapWithTick:d=5,tickSize:e=0,tickMargin:f=0}=a,g=0;if(b){b.forEach(a=>{if(a){var b=a.getBoundingClientRect();b.width>g&&(g=b.width)}});var h=c?c.getBoundingClientRect().width:0;return Math.round(g+(e+f)+h+(c?d:0))}return 0})({ticks:d,label:h.current,labelGapWithTick:5,tickSize:i,tickMargin:j});Math.round(n.width)!==Math.round(l)&&k((0,aO.QG)({id:c,width:l}))}},[g,null==g||null==(b=g.current)||null==(b=b.tickRefs)?void 0:b.current,null==n?void 0:n.width,n,k,f,c,e]),null==n||null==o)return null;var{dangerouslySetInnerHTML:q,ticks:s}=a,t=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,aZ);return r.createElement(at,a_({},t,{ref:g,labelRef:h,scale:m,x:o.x,y:o.y,width:n.width,height:n.height,className:(0,U.$)("recharts-".concat(l," ").concat(l),d),viewBox:i,ticks:p}))},a2=a=>{var b,c,d,e,f;return r.createElement(r.Fragment,null,r.createElement(a0,{interval:null!=(b=a.interval)?b:"preserveEnd",id:a.yAxisId,scale:a.scale,type:a.type,domain:a.domain,allowDataOverflow:a.allowDataOverflow,dataKey:a.dataKey,allowDuplicatedCategory:a.allowDuplicatedCategory,allowDecimals:a.allowDecimals,tickCount:a.tickCount,padding:a.padding,includeHidden:null!=(c=a.includeHidden)&&c,reversed:a.reversed,ticks:a.ticks,width:a.width,orientation:a.orientation,mirror:a.mirror,hide:a.hide,unit:a.unit,name:a.name,angle:null!=(d=a.angle)?d:0,minTickGap:null!=(e=a.minTickGap)?e:5,tick:null==(f=a.tick)||f,tickFormatter:a.tickFormatter}),r.createElement(a1,a))},a3={allowDataOverflow:au.cd.allowDataOverflow,allowDecimals:au.cd.allowDecimals,allowDuplicatedCategory:au.cd.allowDuplicatedCategory,hide:!1,mirror:au.cd.mirror,orientation:au.cd.orientation,padding:au.cd.padding,reversed:au.cd.reversed,scale:au.cd.scale,tickCount:au.cd.tickCount,type:au.cd.type,width:au.cd.width,yAxisId:0};class a4 extends r.Component{render(){return r.createElement(a2,this.props)}}a$(a4,"displayName","YAxis"),a$(a4,"defaultProps",a3);var a5=c(70522),a6=c(83790),a7=c(18769),a8=c.n(a7),a9=["valueAccessor"],ba=["data","dataKey","clockWise","id","textBreakAll"];function bb(){return(bb=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function bc(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function bd(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?bc(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):bc(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function be(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var bf=a=>Array.isArray(a.value)?a8()(a.value):a.value;function bg(a){var{valueAccessor:b=bf}=a,c=be(a,a9),{data:d,dataKey:e,clockWise:f,id:g,textBreakAll:h}=c,i=be(c,ba);return d&&d.length?r.createElement(W.W,{className:"recharts-label-list"},d.map((a,c)=>{var d=(0,H.uy)(e)?b(a,c):(0,I.kr)(a&&a.payload,e),j=(0,H.uy)(g)?{}:{id:"".concat(g,"-").concat(c)};return r.createElement(ai,bb({},(0,Y.J9)(a,!0),i,j,{parentViewBox:a.parentViewBox,value:d,textBreakAll:h,viewBox:ai.parseViewBox((0,H.uy)(f)?a:bd(bd({},a),{},{clockWise:f})),key:"label-".concat(c),index:c}))})):null}bg.displayName="LabelList",bg.renderCallByParent=function(a,b){var c,d=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!a||!a.children&&d&&!a.label)return null;var{children:e}=a,f=(0,Y.aS)(e,bg).map((a,c)=>(0,r.cloneElement)(a,{data:b,key:"labelList-".concat(c)}));return d?[(c=a.label,c?!0===c?r.createElement(bg,{key:"labelList-implicit",data:b}):r.isValidElement(c)||ah(c)?r.createElement(bg,{key:"labelList-implicit",data:b,content:c}):"object"==typeof c?r.createElement(bg,bb({data:b},c,{key:"labelList-implicit"})):null:null),...f]:f};var bh=c(86852),bi=["x","y"];function bj(){return(bj=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function bk(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function bl(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?bk(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):bk(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function bm(a,b){var{x:c,y:d}=a,e=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,bi),f=parseInt("".concat(c),10),g=parseInt("".concat(d),10),h=parseInt("".concat(b.height||e.height),10),i=parseInt("".concat(b.width||e.width),10);return bl(bl(bl(bl(bl({},b),e),f?{x:f}:{}),g?{y:g}:{}),{},{height:h,width:i,name:b.name,radius:b.radius})}function bn(a){return r.createElement(bh.y,bj({shapeType:"rectangle",propTransformer:bm,activeClassName:"recharts-active-bar"},a))}var bo=function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(c,d)=>{if((0,H.Et)(a))return a;var e=(0,H.Et)(c)||(0,H.uy)(c);return e?a(c,d):(e||function(a,b){if(!a)throw Error("Invariant failed")}(!1),b)}},bp=c(96136),bq=c(51846),br=["children"],bs=(0,r.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function bt(a){var{children:b}=a,c=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,br);return r.createElement(bs.Provider,{value:c},b)}var bu=c(58306);function bv(a,b){var c,d,e=(0,_.G)(b=>(0,au.Rl)(b,a)),f=(0,_.G)(a=>(0,au.sf)(a,b)),g=null!=(c=null==e?void 0:e.allowDataOverflow)?c:au.PU.allowDataOverflow,h=null!=(d=null==f?void 0:f.allowDataOverflow)?d:au.cd.allowDataOverflow;return{needClip:g||h,needClipX:g,needClipY:h}}function bw(a){var{xAxisId:b,yAxisId:c,clipPathId:d}=a,e=(0,bu.oM)(),{needClipX:f,needClipY:g,needClip:h}=bv(b,c);if(!h)return null;var{x:i,y:j,width:k,height:l}=e;return r.createElement("clipPath",{id:"clipPath-".concat(d)},r.createElement("rect",{x:f?i:i-k/2,y:g?j:j-l/2,width:f?k:2*k,height:g?l:2*l}))}var bx=c(54985),by=c(8693),bz=c(14851),bA=c(75309),bB=c(61711);function bC(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function bD(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?bC(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):bC(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var bE=(0,bx.Mz)([au.ld,(a,b,c,d,e)=>e],(a,b)=>a.filter(a=>"bar"===a.type).find(a=>a.id===b)),bF=(0,bx.Mz)([bE],a=>null==a?void 0:a.maxBarSize),bG=(a,b,c)=>{var d=null!=c?c:a;if(!(0,H.uy)(d))return(0,H.F4)(d,b,0)},bH=(0,bx.Mz)([$.fz,au.ld,(a,b)=>b,(a,b,c)=>c,(a,b,c,d)=>d],(a,b,c,d,e)=>b.filter(b=>"horizontal"===a?b.xAxisId===c:b.yAxisId===d).filter(a=>a.isPanorama===e).filter(a=>!1===a.hide).filter(a=>"bar"===a.type)),bI=(0,bx.Mz)([bH,bz.x3,(a,b,c)=>"horizontal"===(0,$.fz)(a)?(0,au.BQ)(a,"xAxis",b):(0,au.BQ)(a,"yAxis",c)],(a,b,c)=>{var d=a.filter(bB.g),e=a.filter(a=>null==a.stackId);return[...Object.entries(d.reduce((a,b)=>(a[b.stackId]||(a[b.stackId]=[]),a[b.stackId].push(b),a),{})).map(a=>{var[d,e]=a;return{stackId:d,dataKeys:e.map(a=>a.dataKey),barSize:bG(b,c,e[0].barSize)}}),...e.map(a=>({stackId:void 0,dataKeys:[a.dataKey].filter(a=>null!=a),barSize:bG(b,c,a.barSize)}))]}),bJ=(a,b,c,d)=>{var e,f;return"horizontal"===(0,$.fz)(a)?(e=(0,au.Gx)(a,"xAxis",b,d),f=(0,au.CR)(a,"xAxis",b,d)):(e=(0,au.Gx)(a,"yAxis",c,d),f=(0,au.CR)(a,"yAxis",c,d)),(0,I.Hj)(e,f)},bK=(0,bx.Mz)([bI,bz.JN,bz._5,bz.gY,(a,b,c,d,e)=>{var f,g,h,i,j=bE(a,b,c,d,e);if(null!=j){var k=(0,$.fz)(a),l=(0,bz.JN)(a),{maxBarSize:m}=j,n=(0,H.uy)(m)?l:m;return"horizontal"===k?(h=(0,au.Gx)(a,"xAxis",b,d),i=(0,au.CR)(a,"xAxis",b,d)):(h=(0,au.Gx)(a,"yAxis",c,d),i=(0,au.CR)(a,"yAxis",c,d)),null!=(f=null!=(g=(0,I.Hj)(h,i,!0))?g:n)?f:0}},bJ,bF],(a,b,c,d,e,f,g)=>{var h=function(a,b,c,d,e){var f,g=d.length;if(!(g<1)){var h=(0,H.F4)(a,c,0,!0),i=[];if((0,z.H)(d[0].barSize)){var j=!1,k=c/g,l=d.reduce((a,b)=>a+(b.barSize||0),0);(l+=(g-1)*h)>=c&&(l-=(g-1)*h,h=0),l>=c&&k>0&&(j=!0,k*=.9,l=g*k);var m={offset:((c-l)/2|0)-h,size:0};f=d.reduce((a,b)=>{var c,d=[...a,{stackId:b.stackId,dataKeys:b.dataKeys,position:{offset:m.offset+m.size+h,size:j?k:null!=(c=b.barSize)?c:0}}];return m=d[d.length-1].position,d},i)}else{var n=(0,H.F4)(b,c,0,!0);c-2*n-(g-1)*h<=0&&(h=0);var o=(c-2*n-(g-1)*h)/g;o>1&&(o>>=0);var p=(0,z.H)(e)?Math.min(o,e):o;f=d.reduce((a,b,c)=>[...a,{stackId:b.stackId,dataKeys:b.dataKeys,position:{offset:n+(o+h)*c+(o-p)/2,size:p}}],i)}return f}}(c,d,e!==f?e:f,a,(0,H.uy)(g)?b:g);return e!==f&&null!=h&&(h=h.map(a=>bD(bD({},a),{},{position:bD(bD({},a.position),{},{offset:a.position.offset-e/2})}))),h}),bL=(0,bx.Mz)([bK,bE],(a,b)=>{if(null!=a&&null!=b){var c=a.find(a=>a.stackId===b.stackId&&null!=b.dataKey&&a.dataKeys.includes(b.dataKey));if(null!=c)return c.position}}),bM=(0,bx.Mz)([(a,b,c,d)=>"horizontal"===(0,$.fz)(a)?(0,au.TC)(a,"yAxis",c,d):(0,au.TC)(a,"xAxis",b,d),bE],(a,b)=>{var c=(0,bA.x)(b);if(!a||null==c||null==b)return;var{stackId:d}=b;if(null!=d){var e=a[d];if(e){var{stackedData:f}=e;if(f)return f.find(a=>a.key===c)}}}),bN=(0,bx.Mz)([aP.HZ,(a,b,c,d)=>(0,au.Gx)(a,"xAxis",b,d),(a,b,c,d)=>(0,au.Gx)(a,"yAxis",c,d),(a,b,c,d)=>(0,au.CR)(a,"xAxis",b,d),(a,b,c,d)=>(0,au.CR)(a,"yAxis",c,d),bL,$.fz,by.HS,bJ,bM,bE,(a,b,c,d,e,f)=>f],(a,b,c,d,e,f,g,h,i,j,k,l)=>{var m,{chartData:n,dataStartIndex:o,dataEndIndex:p}=h;if(null!=k&&null!=f&&("horizontal"===g||"vertical"===g)&&null!=b&&null!=c&&null!=d&&null!=e&&null!=i){var{data:q}=k;if(null!=(m=null!=q&&q.length>0?q:null==n?void 0:n.slice(o,p+1)))return function(a){var{layout:b,barSettings:{dataKey:c,minPointSize:d},pos:e,bandSize:f,xAxis:g,yAxis:h,xAxisTicks:i,yAxisTicks:j,stackedData:k,displayedData:l,offset:m,cells:n}=a,o="horizontal"===b?h:g,p=k?o.scale.domain():null,q=(0,I.DW)({numericAxis:o});return l.map((a,l)=>{k?r=(0,I._f)(k[l],p):Array.isArray(r=(0,I.kr)(a,c))||(r=[q,r]);var o=bo(d,0)(r[1],l);if("horizontal"===b){var r,s,t,u,v,w,x,[y,z]=[h.scale(r[0]),h.scale(r[1])];s=(0,I.y2)({axis:g,ticks:i,bandSize:f,offset:e.offset,entry:a,index:l}),t=null!=(x=null!=z?z:y)?x:void 0,u=e.size;var A=y-z;if(v=(0,H.M8)(A)?0:A,w={x:s,y:m.top,width:u,height:m.height},Math.abs(o)>0&&Math.abs(v)<Math.abs(o)){var B=(0,H.sA)(v||o)*(Math.abs(o)-Math.abs(v));t-=B,v+=B}}else{var[C,D]=[g.scale(r[0]),g.scale(r[1])];if(s=C,t=(0,I.y2)({axis:h,ticks:j,bandSize:f,offset:e.offset,entry:a,index:l}),u=D-C,v=e.size,w={x:m.left,y:t,width:m.width,height:v},Math.abs(o)>0&&Math.abs(u)<Math.abs(o)){var E=(0,H.sA)(u||o)*(Math.abs(o)-Math.abs(u));u+=E}}return null==s||null==t||null==u||null==v?null:b$(b$({},a),{},{x:s,y:t,width:u,height:v,value:k?r:r[1],payload:a,background:w,tooltipPosition:{x:s+u/2,y:t+v/2}},n&&n[l]&&n[l].props)}).filter(Boolean)}({layout:g,barSettings:k,pos:f,bandSize:i,xAxis:b,yAxis:c,xAxisTicks:d,yAxisTicks:e,stackedData:j,displayedData:m,offset:a,cells:l})}}),bO=c(76265),bP=c(76341),bQ=c(27007),bR=c(33344),bS=c(72650),bT=c(25283),bU=["onMouseEnter","onMouseLeave","onClick"],bV=["value","background","tooltipPosition"],bW=["id"],bX=["onMouseEnter","onClick","onMouseLeave"];function bY(){return(bY=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function bZ(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function b$(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?bZ(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):bZ(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function b_(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function b0(a){var{dataKey:b,stroke:c,strokeWidth:d,fill:e,name:f,hide:g,unit:h}=a;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:c,strokeWidth:d,fill:e,dataKey:b,nameKey:void 0,name:(0,I.uM)(f,b),hide:g,type:a.tooltipType,color:a.fill,unit:h}}}function b1(a){var b=(0,_.G)(bO.A2),{data:c,dataKey:d,background:e,allOtherBarProps:f}=a,{onMouseEnter:g,onMouseLeave:h,onClick:i}=f,j=b_(f,bU),k=(0,bp.Cj)(g,d),l=(0,bp.Pg)(h),m=(0,bp.Ub)(i,d);if(!e||null==c)return null;var n=(0,Y.J9)(e,!1);return r.createElement(r.Fragment,null,c.map((a,c)=>{var{value:f,background:g,tooltipPosition:h}=a,i=b_(a,bV);if(!g)return null;var o=k(a,c),p=l(a,c),q=m(a,c),s=b$(b$(b$(b$(b$({option:e,isActive:String(c)===b},i),{},{fill:"#eee"},g),n),(0,ak.XC)(j,a,c)),{},{onMouseEnter:o,onMouseLeave:p,onClick:q,dataKey:d,index:c,className:"recharts-bar-background-rectangle"});return r.createElement(bn,bY({key:"background-bar-".concat(c)},s))}))}function b2(a){var{data:b,props:c,showLabels:d}=a,e=(0,al.u)(c),{id:f}=e,g=b_(e,bW),{shape:h,dataKey:i,activeBar:j}=c,k=(0,_.G)(bO.A2),l=(0,_.G)(bO.Xb),{onMouseEnter:m,onClick:n,onMouseLeave:o}=c,p=b_(c,bX),q=(0,bp.Cj)(m,i),s=(0,bp.Pg)(o),t=(0,bp.Ub)(n,i);return b?r.createElement(r.Fragment,null,b.map((a,b)=>{var c=j&&String(b)===k&&(null==l||i===l),d=b$(b$(b$({},g),a),{},{isActive:c,option:c?j:h,index:b,dataKey:i});return r.createElement(W.W,bY({className:"recharts-bar-rectangle"},(0,ak.XC)(p,a,b),{onMouseEnter:q(a,b),onMouseLeave:s(a,b),onClick:t(a,b),key:"rectangle-".concat(null==a?void 0:a.x,"-").concat(null==a?void 0:a.y,"-").concat(null==a?void 0:a.value,"-").concat(b)}),r.createElement(bn,d))}),d&&bg.renderCallByParent(c,b)):null}function b3(a){var{props:b,previousRectanglesRef:c}=a,{data:d,layout:e,isAnimationActive:f,animationBegin:g,animationDuration:h,animationEasing:i,onAnimationEnd:j,onAnimationStart:k}=b,l=c.current,m=(0,bQ.n)(b,"recharts-bar-"),[n,o]=(0,r.useState)(!1),p=(0,r.useCallback)(()=>{"function"==typeof j&&j(),o(!1)},[j]),q=(0,r.useCallback)(()=>{"function"==typeof k&&k(),o(!0)},[k]);return r.createElement(bT.J,{begin:g,duration:h,isActive:f,easing:i,onAnimationEnd:p,onAnimationStart:q,key:m},a=>{var f=1===a?d:null==d?void 0:d.map((b,c)=>{var d=l&&l[c];if(d)return b$(b$({},b),{},{x:(0,H.GW)(d.x,b.x,a),y:(0,H.GW)(d.y,b.y,a),width:(0,H.GW)(d.width,b.width,a),height:(0,H.GW)(d.height,b.height,a)});if("horizontal"===e){var f=(0,H.GW)(0,b.height,a);return b$(b$({},b),{},{y:b.y+b.height-f,height:f})}var g=(0,H.GW)(0,b.width,a);return b$(b$({},b),{},{width:g})});return(a>0&&(c.current=null!=f?f:null),null==f)?null:r.createElement(W.W,null,r.createElement(b2,{props:b,data:f,showLabels:!n}))})}function b4(a){var{data:b,isAnimationActive:c}=a,d=(0,r.useRef)(null);return c&&b&&b.length&&(null==d.current||d.current!==b)?r.createElement(b3,{previousRectanglesRef:d,props:a}):r.createElement(b2,{props:a,data:b,showLabels:!0})}var b5=(a,b)=>{var c=Array.isArray(a.value)?a.value[1]:a.value;return{x:a.x,y:a.y,value:c,errorVal:(0,I.kr)(a,b)}};class b6 extends r.PureComponent{render(){var{hide:a,data:b,dataKey:c,className:d,xAxisId:e,yAxisId:f,needClip:g,background:h,id:i}=this.props;if(a)return null;var j=(0,U.$)("recharts-bar",d);return r.createElement(W.W,{className:j,id:i},g&&r.createElement("defs",null,r.createElement(bw,{clipPathId:i,xAxisId:e,yAxisId:f})),r.createElement(W.W,{className:"recharts-bar-rectangles",clipPath:g?"url(#clipPath-".concat(i,")"):void 0},r.createElement(b1,{data:b,dataKey:c,background:h,allOtherBarProps:this.props}),r.createElement(b4,this.props)),this.props.children)}}var b7={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!K.m.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function b8(a){var b,{xAxisId:c,yAxisId:d,hide:e,legendType:f,minPointSize:g,activeBar:h,animationBegin:i,animationDuration:j,animationEasing:k,isAnimationActive:l}=a,{needClip:m}=bv(c,d),n=(0,$.WX)(),o=(0,av.r)(),p=(0,Y.aS)(a.children,a6.f),q=(0,_.G)(b=>bN(b,c,d,o,a.id,p));if("vertical"!==n&&"horizontal"!==n)return null;var s=null==q?void 0:q[0];return b=null==s||null==s.height||null==s.width?0:"vertical"===n?s.height/2:s.width/2,r.createElement(bt,{xAxisId:c,yAxisId:d,data:q,dataPointFormatter:b5,errorBarOffset:b},r.createElement(b6,bY({},a,{layout:n,needClip:m,data:q,xAxisId:c,yAxisId:d,hide:e,legendType:f,minPointSize:g,activeBar:h,animationBegin:i,animationDuration:j,animationEasing:k,isAnimationActive:l})))}function b9(a){var b=(0,y.e)(a,b7),c=(0,av.r)();return r.createElement(bR.x,{id:b.id,type:"bar"},a=>r.createElement(r.Fragment,null,r.createElement(bP.A,{legendPayload:(a=>{var{dataKey:b,name:c,fill:d,legendType:e,hide:f}=a;return[{inactive:f,dataKey:b,type:e,color:d,value:(0,I.uM)(c,b),payload:a}]})(b)}),r.createElement(bq.r,{fn:b0,args:b}),r.createElement(bS.p,{type:"bar",id:a,data:void 0,xAxisId:b.xAxisId,yAxisId:b.yAxisId,zAxisId:0,dataKey:b.dataKey,stackId:(0,I.$8)(b.stackId),hide:b.hide,barSize:b.barSize,minPointSize:b.minPointSize,maxBarSize:b.maxBarSize,isPanorama:c}),r.createElement(b8,bY({},b,{id:a}))))}function ca({data:a,height:b=300}){return(0,d.jsx)(q.u,{width:"100%",height:b,children:(0,d.jsxs)(F,{data:a,margin:{top:20,right:30,left:20,bottom:5},barCategoryGap:"20%",children:[(0,d.jsx)(aN,{strokeDasharray:"3 3",stroke:"#E6EFF5"}),(0,d.jsx)(aY,{dataKey:"day",axisLine:!1,tickLine:!1,tick:{fontSize:12,fill:"#718EBF"}}),(0,d.jsx)(a4,{axisLine:!1,tickLine:!1,tick:{fontSize:12,fill:"#718EBF"}}),(0,d.jsx)(a5.s,{wrapperStyle:{paddingTop:"20px"},iconType:"circle"}),(0,d.jsx)(b9,{dataKey:"cashIn",fill:"#1814F3",name:"Cash In",radius:[30,30,30,30],barSize:15}),(0,d.jsx)(b9,{dataKey:"cashOut",fill:"#16DBCC",name:"Cash Out",radius:[30,30,30,30],barSize:15})]})})}function cb({data:a}){return(0,d.jsxs)(f.Zp,{className:"bg-white rounded-[25px] border-none shadow-lg",children:[(0,d.jsx)(f.aR,{children:(0,d.jsx)(f.ZB,{className:"text-[#343C6A] text-xl font-semibold",children:"Weekly Activity"})}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)(ca,{data:a,height:250})})]})}b9.displayName="Bar";var cc=c(65346);function cd({data:a}){let b=a.map(a=>({name:a.label,value:a.value,color:a.color}));return(0,d.jsxs)(f.Zp,{className:"bg-white rounded-[25px] border-none shadow-lg",children:[(0,d.jsx)(f.aR,{children:(0,d.jsx)(f.ZB,{className:"text-[#343C6A] text-xl font-semibold",children:"Expense Statistics"})}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("div",{className:"flex-1",children:(0,d.jsx)(cc.k,{data:b,height:200,showLegend:!1})}),(0,d.jsx)("div",{className:"flex flex-col space-y-4 ml-8",children:a.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:a.color}}),(0,d.jsxs)("div",{className:"flex flex-col",children:[(0,d.jsxs)("span",{className:"text-sm font-medium text-white",children:[a.percentage,"%"]}),(0,d.jsx)("span",{className:"text-xs text-white/80",children:a.label})]})]},b))})]})})]})}var ce=c(35284),cf=c(93758);let cg=(0,g.A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);var ch=c(86773),ci=c(45807);let cj=(0,g.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);function ck(){let[a,b]=(0,r.useState)("525.50"),c=[{id:"refund",label:"Refund transaction",icon:cg,color:"bg-orange-100 text-orange-600"},{id:"fund-agent",label:"Fund agent",icon:ch.A,color:"bg-blue-100 text-blue-600"},{id:"settle-merchant",label:"Settle Merchant",icon:ci.A,color:"bg-green-100 text-green-600"}];return(0,d.jsxs)(f.Zp,{className:"bg-white rounded-[25px] border-none shadow-lg",children:[(0,d.jsx)(f.aR,{children:(0,d.jsx)(f.ZB,{className:"text-[#343C6A] text-xl font-semibold",children:"Quick Transfer"})}),(0,d.jsxs)(f.Wu,{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"flex -space-x-2",children:[1,2,3].map(a=>(0,d.jsxs)("div",{className:"w-12 h-12 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 border-2 border-white flex items-center justify-center text-white font-semibold",children:["U",a]},a))}),(0,d.jsx)(ce.$,{variant:"outline",size:"icon",className:"rounded-full",children:(0,d.jsx)(cj,{className:"h-4 w-4"})})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-[#718EBF]",children:"Write Amount"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"flex-1 relative",children:(0,d.jsx)(cf.p,{value:a,onChange:a=>b(a.target.value),className:"bg-[#EDF1F7] border-none text-lg font-medium h-12"})}),(0,d.jsxs)(ce.$,{className:"bg-[#1814F3] hover:bg-[#1814F3]/90 h-12 px-6",children:[(0,d.jsx)(cj,{className:"h-4 w-4 mr-2"}),"Send"]})]})]}),(0,d.jsx)("div",{className:"grid grid-cols-3 gap-4",children:c.map(a=>{let b=a.icon;return(0,d.jsxs)("div",{className:"text-center space-y-2",children:[(0,d.jsx)("div",{className:`w-16 h-16 rounded-full ${a.color} flex items-center justify-center mx-auto`,children:(0,d.jsx)(b,{className:"h-6 w-6"})}),(0,d.jsx)("span",{className:"text-xs text-[#718EBF] font-medium",children:a.label})]},a.id)})})]})]})}var cl=["axis"],cm=(0,r.forwardRef)((a,b)=>r.createElement(D,{chartName:"LineChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:cl,tooltipPayloadSearcher:s.uN,categoricalChartProps:a,ref:b})),cn=c(6487);function co(){return(co=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var cp=a=>{var{cx:b,cy:c,r:d,className:e}=a,f=(0,U.$)("recharts-dot",e);return b===+b&&c===+c&&d===+d?r.createElement("circle",co({},(0,al.u)(a),(0,ak._U)(a),{className:f,cx:b,cy:c,r:d})):null};function cq(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cr(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?cq(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):cq(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function cs(a){var{points:b,mainColor:c,activeDot:d,itemDataKey:e}=a,f=(0,_.G)(bO.A2),g=(0,bu.EI)();if(null==b||null==g)return null;var h=b.find(a=>g.includes(a.payload));return(0,H.uy)(h)?null:(a=>{var b,{point:c,childIndex:d,mainColor:e,activeDot:f,dataKey:g}=a;if(!1===f||null==c.x||null==c.y)return null;var h=cr(cr({index:d,dataKey:g,cx:c.x,cy:c.y,r:4,fill:null!=e?e:"none",strokeWidth:2,stroke:"#fff",payload:c.payload,value:c.value},(0,Y.J9)(f,!1)),(0,ak._U)(f));return b=(0,r.isValidElement)(f)?(0,r.cloneElement)(f,h):"function"==typeof f?f(h):r.createElement(cp,h),r.createElement(W.W,{className:"recharts-active-dot"},b)})({point:h,childIndex:Number(f),mainColor:c,dataKey:e,activeDot:d})}var ct=(a,b,c,d)=>(0,au.Gx)(a,"xAxis",b,d),cu=(a,b,c,d)=>(0,au.CR)(a,"xAxis",b,d),cv=(a,b,c,d)=>(0,au.Gx)(a,"yAxis",c,d),cw=(a,b,c,d)=>(0,au.CR)(a,"yAxis",c,d),cx=(0,bx.Mz)([$.fz,ct,cv,cu,cw],(a,b,c,d,e)=>(0,I._L)(a,"xAxis")?(0,I.Hj)(b,d,!1):(0,I.Hj)(c,e,!1));function cy(a){return"line"===a.type}var cz=(0,bx.Mz)([au.ld,(a,b,c,d,e)=>e],(a,b)=>a.filter(cy).find(a=>a.id===b)),cA=(0,bx.Mz)([$.fz,ct,cv,cu,cw,cz,cx,by.HS],(a,b,c,d,e,f,g,h)=>{var i,{chartData:j,dataStartIndex:k,dataEndIndex:l}=h;if(null!=f&&null!=b&&null!=c&&null!=d&&null!=e&&0!==d.length&&0!==e.length&&null!=g){var{dataKey:m,data:n}=f;if(null!=(i=null!=n&&n.length>0?n:null==j?void 0:j.slice(k,l+1)))return function(a){var{layout:b,xAxis:c,yAxis:d,xAxisTicks:e,yAxisTicks:f,dataKey:g,bandSize:h,displayedData:i}=a;return i.map((a,i)=>{var j=(0,I.kr)(a,g);if("horizontal"===b)return{x:(0,I.nb)({axis:c,ticks:e,bandSize:h,entry:a,index:i}),y:(0,H.uy)(j)?null:d.scale(j),value:j,payload:a};var k=(0,H.uy)(j)?null:c.scale(j),l=(0,I.nb)({axis:d,ticks:f,bandSize:h,entry:a,index:i});return null==k||null==l?null:{x:k,y:l,value:j,payload:a}}).filter(Boolean)}({layout:a,xAxis:b,yAxis:c,xAxisTicks:d,yAxisTicks:e,dataKey:m,bandSize:g,displayedData:i})}}),cB=["id"],cC=["type","layout","connectNulls","needClip"],cD=["activeDot","animateNewValues","animationBegin","animationDuration","animationEasing","connectNulls","dot","hide","isAnimationActive","label","legendType","xAxisId","yAxisId","id"];function cE(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cF(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?cE(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):cE(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function cG(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function cH(){return(cH=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function cI(a){var{dataKey:b,data:c,stroke:d,strokeWidth:e,fill:f,name:g,hide:h,unit:i}=a;return{dataDefinedOnItem:c,positions:void 0,settings:{stroke:d,strokeWidth:e,fill:f,dataKey:b,nameKey:void 0,name:(0,I.uM)(g,b),hide:h,type:a.tooltipType,color:a.stroke,unit:i}}}var cJ=(a,b)=>"".concat(b,"px ").concat(a-b,"px");function cK(a){var{clipPathId:b,points:c,props:d}=a,{dot:e,dataKey:f,needClip:g}=d;if(null==c||!e&&1!==c.length)return null;var{id:h}=d,i=cG(d,cB),j=(0,Y.y$)(e),k=(0,al.u)(i),l=(0,Y.J9)(e,!0),m=c.map((a,b)=>{var d,g=cF(cF(cF({key:"dot-".concat(b),r:3},k),l),{},{index:b,cx:a.x,cy:a.y,dataKey:f,value:a.value,payload:a.payload,points:c});if(r.isValidElement(e))d=r.cloneElement(e,g);else if("function"==typeof e)d=e(g);else{var h=(0,U.$)("recharts-line-dot","boolean"!=typeof e?e.className:"");d=r.createElement(cp,cH({},g,{className:h}))}return d}),n={clipPath:g?"url(#clipPath-".concat(j?"":"dots-").concat(b,")"):void 0};return r.createElement(W.W,cH({className:"recharts-line-dots",key:"dots"},n),m)}function cL(a){var{clipPathId:b,pathRef:c,points:d,strokeDasharray:e,props:f,showLabels:g}=a,{type:h,layout:i,connectNulls:j,needClip:k}=f,l=cG(f,cC),m=cF(cF({},(0,Y.J9)(l,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:k?"url(#clipPath-".concat(b,")"):void 0,points:d,type:h,layout:i,connectNulls:j,strokeDasharray:null!=e?e:f.strokeDasharray});return r.createElement(r.Fragment,null,(null==d?void 0:d.length)>1&&r.createElement(cn.I,cH({},m,{pathRef:c})),r.createElement(cK,{points:d,clipPathId:b,props:f}),g&&bg.renderCallByParent(f,d))}function cM(a){var{clipPathId:b,props:c,pathRef:d,previousPointsRef:e,longestAnimatedLengthRef:f}=a,{points:g,strokeDasharray:h,isAnimationActive:i,animationBegin:j,animationDuration:k,animationEasing:l,animateNewValues:m,width:n,height:o,onAnimationEnd:p,onAnimationStart:q}=c,s=e.current,t=(0,bQ.n)(c,"recharts-line-"),[u,v]=(0,r.useState)(!1),w=(0,r.useCallback)(()=>{"function"==typeof p&&p(),v(!1)},[p]),x=(0,r.useCallback)(()=>{"function"==typeof q&&q(),v(!0)},[q]),y=function(a){try{return a&&a.getTotalLength&&a.getTotalLength()||0}catch(a){return 0}}(d.current),z=f.current;return r.createElement(bT.J,{begin:j,duration:k,isActive:i,easing:l,onAnimationEnd:w,onAnimationStart:x,key:t},a=>{var i,j=Math.min((0,H.GW)(z,y+z,a),y);if(i=h?((a,b,c)=>{var d=c.reduce((a,b)=>a+b);if(!d)return cJ(b,a);for(var e=Math.floor(a/d),f=a%d,g=b-a,h=[],i=0,j=0;i<c.length;j+=c[i],++i)if(j+c[i]>f){h=[...c.slice(0,i),f-j];break}var k=h.length%2==0?[0,g]:[g];return[...function(a,b){for(var c=a.length%2!=0?[...a,0]:a,d=[],e=0;e<b;++e)d=[...d,...c];return d}(c,e),...h,...k].map(a=>"".concat(a,"px")).join(", ")})(j,y,"".concat(h).split(/[,\s]+/gim).map(a=>parseFloat(a))):cJ(y,j),s){var k=s.length/g.length,l=1===a?g:g.map((b,c)=>{var d=Math.floor(c*k);if(s[d]){var e=s[d];return cF(cF({},b),{},{x:(0,H.GW)(e.x,b.x,a),y:(0,H.GW)(e.y,b.y,a)})}return m?cF(cF({},b),{},{x:(0,H.GW)(2*n,b.x,a),y:(0,H.GW)(o/2,b.y,a)}):cF(cF({},b),{},{x:b.x,y:b.y})});return e.current=l,r.createElement(cL,{props:c,points:l,clipPathId:b,pathRef:d,showLabels:!u,strokeDasharray:i})}return a>0&&y>0&&(e.current=g,f.current=j),r.createElement(cL,{props:c,points:g,clipPathId:b,pathRef:d,showLabels:!u,strokeDasharray:i})})}function cN(a){var{clipPathId:b,props:c}=a,{points:d,isAnimationActive:e}=c,f=(0,r.useRef)(null),g=(0,r.useRef)(0),h=(0,r.useRef)(null),i=f.current;return e&&d&&d.length&&i!==d?r.createElement(cM,{props:c,clipPathId:b,previousPointsRef:f,longestAnimatedLengthRef:g,pathRef:h}):r.createElement(cL,{props:c,points:d,clipPathId:b,pathRef:h,showLabels:!0})}var cO=(a,b)=>({x:a.x,y:a.y,value:a.value,errorVal:(0,I.kr)(a.payload,b)});class cP extends r.Component{render(){var a,{hide:b,dot:c,points:d,className:e,xAxisId:f,yAxisId:g,top:h,left:i,width:j,height:k,id:l,needClip:m}=this.props;if(b)return null;var n=(0,U.$)("recharts-line",e),{r:o=3,strokeWidth:p=2}=null!=(a=(0,Y.J9)(c,!1))?a:{r:3,strokeWidth:2},q=(0,Y.y$)(c),s=2*o+p;return r.createElement(r.Fragment,null,r.createElement(W.W,{className:n},m&&r.createElement("defs",null,r.createElement(bw,{clipPathId:l,xAxisId:f,yAxisId:g}),!q&&r.createElement("clipPath",{id:"clipPath-dots-".concat(l)},r.createElement("rect",{x:i-s/2,y:h-s/2,width:j+s,height:k+s}))),r.createElement(cN,{props:this.props,clipPathId:l}),r.createElement(bt,{xAxisId:f,yAxisId:g,data:d,dataPointFormatter:cO,errorBarOffset:0},this.props.children)),r.createElement(cs,{activeDot:this.props.activeDot,points:d,mainColor:this.props.stroke,itemDataKey:this.props.dataKey}))}}var cQ={activeDot:!0,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!0,fill:"#fff",hide:!1,isAnimationActive:!K.m.isSsr,label:!1,legendType:"line",stroke:"#3182bd",strokeWidth:1,xAxisId:0,yAxisId:0};function cR(a){var b=(0,y.e)(a,cQ),{activeDot:c,animateNewValues:d,animationBegin:e,animationDuration:f,animationEasing:g,connectNulls:h,dot:i,hide:j,isAnimationActive:k,label:l,legendType:m,xAxisId:n,yAxisId:o,id:p}=b,q=cG(b,cD),{needClip:s}=bv(n,o),t=(0,bu.oM)(),u=(0,$.WX)(),v=(0,av.r)(),w=(0,_.G)(a=>cA(a,n,o,v,p));if("horizontal"!==u&&"vertical"!==u||null==w||null==t)return null;var{height:x,width:z,x:A,y:B}=t;return r.createElement(cP,cH({},q,{id:p,connectNulls:h,dot:i,activeDot:c,animateNewValues:d,animationBegin:e,animationDuration:f,animationEasing:g,isAnimationActive:k,hide:j,label:l,legendType:m,xAxisId:n,yAxisId:o,points:w,layout:u,height:x,width:z,left:A,top:B,needClip:s}))}function cS(a){var b=(0,y.e)(a,cQ),c=(0,av.r)();return r.createElement(bR.x,{id:b.id,type:"line"},a=>r.createElement(r.Fragment,null,r.createElement(bP.A,{legendPayload:(a=>{var{dataKey:b,name:c,stroke:d,legendType:e,hide:f}=a;return[{inactive:f,dataKey:b,type:e,color:d,value:(0,I.uM)(c,b),payload:a}]})(b)}),r.createElement(bq.r,{fn:cI,args:b}),r.createElement(bS.p,{type:"line",id:a,data:b.data,xAxisId:b.xAxisId,yAxisId:b.yAxisId,zAxisId:0,dataKey:b.dataKey,hide:b.hide,isPanorama:c}),r.createElement(cR,cH({},b,{id:a}))))}function cT({data:a,height:b=300,color:c="#1814F3"}){return(0,d.jsx)(q.u,{width:"100%",height:b,children:(0,d.jsxs)(cm,{data:a,margin:{top:20,right:30,left:20,bottom:5},children:[(0,d.jsx)(aN,{strokeDasharray:"3 3",stroke:"#E6EFF5"}),(0,d.jsx)(aY,{dataKey:"month",axisLine:!1,tickLine:!1,tick:{fontSize:12,fill:"#718EBF"}}),(0,d.jsx)(a4,{axisLine:!1,tickLine:!1,tick:{fontSize:12,fill:"#718EBF"}}),(0,d.jsx)(cS,{type:"monotone",dataKey:"value",stroke:c,strokeWidth:3,dot:{fill:c,strokeWidth:2,r:4},activeDot:{r:6,stroke:c,strokeWidth:2}})]})})}function cU({data:a}){return(0,d.jsxs)(f.Zp,{className:"bg-white rounded-[25px] border-none shadow-lg",children:[(0,d.jsx)(f.aR,{children:(0,d.jsx)(f.ZB,{className:"text-[#343C6A] text-xl font-semibold",children:"Total transaction over time"})}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)(cT,{data:a,height:250,color:"#1814F3"})})]})}cS.displayName="Line";let cV=(0,g.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);var cW=c(3368);let cX=(0,g.A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);function cY({transactions:a}){return(0,d.jsxs)(f.Zp,{className:"bg-white rounded-[25px] border-none shadow-lg",children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between",children:[(0,d.jsx)(f.ZB,{className:"text-[#343C6A] text-xl font-semibold",children:"Recent Transaction"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsxs)(ce.$,{variant:"outline",size:"sm",children:[(0,d.jsx)(cV,{className:"h-4 w-4 mr-2"}),"Export"]}),(0,d.jsxs)(ce.$,{variant:"outline",size:"sm",children:[(0,d.jsx)(cW.A,{className:"h-4 w-4 mr-2"}),"View All"]})]})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"w-full",children:[(0,d.jsx)("thead",{children:(0,d.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,d.jsx)("th",{className:"text-left py-3 px-4 font-medium text-[#718EBF] text-sm",children:"ID"}),(0,d.jsx)("th",{className:"text-left py-3 px-4 font-medium text-[#718EBF] text-sm",children:"Description"}),(0,d.jsx)("th",{className:"text-left py-3 px-4 font-medium text-[#718EBF] text-sm",children:"Type"}),(0,d.jsx)("th",{className:"text-left py-3 px-4 font-medium text-[#718EBF] text-sm",children:"Amount"}),(0,d.jsx)("th",{className:"text-left py-3 px-4 font-medium text-[#718EBF] text-sm",children:"Date"}),(0,d.jsx)("th",{className:"text-left py-3 px-4 font-medium text-[#718EBF] text-sm",children:"Status"}),(0,d.jsx)("th",{className:"text-left py-3 px-4 font-medium text-[#718EBF] text-sm",children:"Action"})]})}),(0,d.jsx)("tbody",{children:a.map((a,b)=>(0,d.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,d.jsxs)("td",{className:"py-4 px-4 text-sm text-[#232323]",children:["#",a.id.slice(0,8)]}),(0,d.jsx)("td",{className:"py-4 px-4 text-sm text-[#232323] font-medium",children:a.description}),(0,d.jsx)("td",{className:"py-4 px-4 text-sm text-[#718EBF] capitalize",children:a.type.replace("_"," ")}),(0,d.jsx)("td",{className:"py-4 px-4 text-sm font-medium",children:(0,d.jsxs)("span",{className:"cash_out"===a.type||"loan"===a.type?"text-red-600":"text-green-600",children:["cash_out"===a.type||"loan"===a.type?"-":"+",(0,n.vv)(a.amount)]})}),(0,d.jsx)("td",{className:"py-4 px-4 text-sm text-[#718EBF]",children:(0,n.Yq)(a.date)}),(0,d.jsx)("td",{className:"py-4 px-4",children:(0,d.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${function(a){switch(a){case"completed":return"text-green-600 bg-green-100";case"pending":return"text-yellow-600 bg-yellow-100";case"failed":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}}(a.status)}`,children:a.status})}),(0,d.jsx)("td",{className:"py-4 px-4",children:(0,d.jsx)(ce.$,{variant:"ghost",size:"icon",children:(0,d.jsx)(cX,{className:"h-4 w-4"})})})]},a.id))})]})})})]})}let cZ=[{day:"Sat",cashIn:480,cashOut:320},{day:"Sun",cashIn:350,cashOut:280},{day:"Mon",cashIn:400,cashOut:300},{day:"Tue",cashIn:300,cashOut:200},{day:"Wed",cashIn:450,cashOut:350},{day:"Thu",cashIn:380,cashOut:280},{day:"Fri",cashIn:420,cashOut:320}],c$=[{label:"User Wallets",value:30,percentage:30,color:"#1814F3"},{label:"Agent Float",value:20,percentage:20,color:"#16DBCC"},{label:"Merchant Payouts",value:15,percentage:15,color:"#FF82AC"},{label:"Commission",value:35,percentage:35,color:"#FFBB38"}],c_=[{month:"Jul",value:200},{month:"Aug",value:300},{month:"Sep",value:250},{month:"Oct",value:400},{month:"Nov",value:350},{month:"Dec",value:450},{month:"Jan",value:500}],c0=[{id:"TXN001",type:"cash_in",amount:1250,description:"Agent Float Top-up",date:"2024-01-15",status:"completed"},{id:"TXN002",type:"cash_out",amount:850,description:"Merchant Settlement",date:"2024-01-14",status:"completed"},{id:"TXN003",type:"transfer",amount:2500,description:"Commission Transfer",date:"2024-01-13",status:"pending"},{id:"TXN004",type:"cash_in",amount:3200,description:"User Wallet Funding",date:"2024-01-12",status:"completed"},{id:"TXN005",type:"cash_out",amount:1800,description:"Agent Withdrawal",date:"2024-01-11",status:"failed"}];function c1(){return(0,d.jsxs)("div",{className:"min-h-screen bg-[#F5F7FA]",children:[(0,d.jsx)(e.Y,{title:"Admin Dashboard"}),(0,d.jsxs)("main",{className:"p-4 md:p-8",children:[(0,d.jsx)(p,{totalUsers:12450,totalTransactions:8920,totalRevenue:245e4,activeAgents:156}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-12 gap-6",children:[(0,d.jsx)("div",{className:"lg:col-span-8",children:(0,d.jsx)(cb,{data:cZ})}),(0,d.jsx)("div",{className:"lg:col-span-4",children:(0,d.jsx)(cd,{data:c$})}),(0,d.jsx)("div",{className:"lg:col-span-4 order-3 lg:order-none",children:(0,d.jsx)(ck,{})}),(0,d.jsx)("div",{className:"lg:col-span-8 order-2 lg:order-none",children:(0,d.jsx)(cU,{data:c_})}),(0,d.jsx)("div",{className:"lg:col-span-12 order-4",children:(0,d.jsx)(cY,{transactions:c0})})]})]})]})}},61135:()=>{},61805:(a,b,c)=>{Promise.resolve().then(c.bind(c,88699))},62186:(a,b,c)=>{"use strict";c.d(b,{Wu:()=>j,ZB:()=>i,Zp:()=>g,aR:()=>h});var d=c(21124),e=c(38301),f=c(44943);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-xl border bg-white text-gray-950 shadow",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,f.cn)("font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm text-gray-500",a),...b})).displayName="CardDescription";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));j.displayName="CardContent",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b})).displayName="CardFooter"},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65346:(a,b,c)=>{"use strict";c.d(b,{k:()=>k});var d=c(21124),e=c(6077),f=c(86493),g=c(40853),h=c(83790),i=c(70522);let j=["#1814F3","#16DBCC","#FF82AC","#FFBB38"];function k({data:a,width:b=400,height:c=300,showLegend:k=!0}){return(0,d.jsx)(e.u,{width:"100%",height:c,children:(0,d.jsxs)(f.r,{children:[(0,d.jsx)(g.F,{data:a,cx:"50%",cy:"50%",innerRadius:60,outerRadius:100,paddingAngle:5,dataKey:"value",children:a.map((a,b)=>(0,d.jsx)(h.f,{fill:a.color||j[b%j.length]},`cell-${b}`))}),k&&(0,d.jsx)(i.s,{verticalAlign:"bottom",height:36,formatter:(a,b)=>(0,d.jsx)("span",{style:{color:b.color},children:a})})]})})}},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(97523);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},72763:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88699:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\workspace\\\\.typescript\\\\aetrustfrontend\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\workspace\\.typescript\\aetrustfrontend\\src\\app\\admin\\page.tsx","default")},91143:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.toArray=function(a){return Array.isArray(a)?a:Array.from(a)}},92691:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,81170,23)),Promise.resolve().then(c.t.bind(c,23597,23)),Promise.resolve().then(c.t.bind(c,36893,23)),Promise.resolve().then(c.t.bind(c,89748,23)),Promise.resolve().then(c.t.bind(c,6060,23)),Promise.resolve().then(c.t.bind(c,7184,23)),Promise.resolve().then(c.t.bind(c,69576,23)),Promise.resolve().then(c.t.bind(c,73041,23)),Promise.resolve().then(c.t.bind(c,51384,23))},93758:(a,b,c)=>{"use strict";c.d(b,{p:()=>g});var d=c(21124),e=c(38301),f=c(44943);let g=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,f.cn)("flex h-9 w-full rounded-md border border-gray-200 bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-[#1814F3] disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...c}));g.displayName="Input"}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[586,703,303],()=>b(b.s=35002));module.exports=c})();