exports.id=303,exports.ids=[303],exports.modules={456:(a,b,c)=>{"use strict";a.exports=c(56796).vendored.contexts.ImageConfigContext},1162:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isSymbol=function(a){return"symbol"==typeof a||a instanceof Symbol}},2095:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(34548),e=c(38352);b.cloneDeepWith=function(a,b){return d.cloneDeepWith(a,(c,f,g,h)=>{let i=b?.(c,f,g,h);if(void 0!==i)return i;if("object"==typeof a)switch(Object.prototype.toString.call(a)){case e.numberTag:case e.stringTag:case e.booleanTag:{let b=new a.constructor(a?.valueOf());return d.copyProperties(b,a),b}case e.argumentsTag:{let b={};return d.copyProperties(b,a),b.length=a.length,b[Symbol.iterator]=a[Symbol.iterator],b}default:return}})}},2958:(a,b,c)=>{"use strict";var d=c(38301),e=c(99088),f="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},g=e.useSyncExternalStore,h=d.useRef,i=d.useEffect,j=d.useMemo,k=d.useDebugValue;b.useSyncExternalStoreWithSelector=function(a,b,c,d,e){var l=h(null);if(null===l.current){var m={hasValue:!1,value:null};l.current=m}else m=l.current;var n=g(a,(l=j(function(){function a(a){if(!i){if(i=!0,g=a,a=d(a),void 0!==e&&m.hasValue){var b=m.value;if(e(b,a))return h=b}return h=a}if(b=h,f(g,a))return b;var c=d(a);return void 0!==e&&e(b,c)?(g=a,b):(g=a,h=c)}var g,h,i=!1,j=void 0===c?null:c;return[function(){return a(b())},null===j?void 0:function(){return a(j())}]},[b,c,d,e]))[0],l[1]);return i(function(){m.hasValue=!0,m.value=n},[n]),k(n),n}},3001:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{VALID_LOADERS:function(){return c},imageConfigDefault:function(){return d}});let c=["default","imgix","cloudinary","akamai","custom"],d={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},3368:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},4516:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.getSymbols=function(a){return Object.getOwnPropertySymbols(a).filter(b=>Object.prototype.propertyIsEnumerable.call(a,b))}},4702:(a,b,c)=>{"use strict";c.d(b,{m:()=>d});var d={isSsr:!0}},5142:(a,b,c)=>{"use strict";c.d(b,{$g:()=>f,Hw:()=>e,au:()=>g,xH:()=>d});var d=a=>a.options.defaultTooltipEventType,e=a=>a.options.validateTooltipEventTypes;function f(a,b,c){if(null==a)return b;var d=a?"axis":"item";return null==c?b:c.includes(d)?d:b}function g(a,b){return f(b,d(a),e(a))}},6077:(a,b,c)=>{"use strict";c.d(b,{u:()=>l});var d=c(43249),e=c(38301),f=c(50193),g=c.n(f),h=c(22688),i=c(64214);function j(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function k(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?j(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):j(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var l=(0,e.forwardRef)((a,b)=>{var{aspect:c,initialDimension:f={width:-1,height:-1},width:j="100%",height:l="100%",minWidth:m=0,minHeight:n,maxHeight:o,children:p,debounce:q=0,id:r,className:s,onResize:t,style:u={}}=a,v=(0,e.useRef)(null),w=(0,e.useRef)();w.current=t,(0,e.useImperativeHandle)(b,()=>v.current);var[x,y]=(0,e.useState)({containerWidth:f.width,containerHeight:f.height}),z=(0,e.useCallback)((a,b)=>{y(c=>{var d=Math.round(a),e=Math.round(b);return c.containerWidth===d&&c.containerHeight===e?c:{containerWidth:d,containerHeight:e}})},[]);(0,e.useEffect)(()=>{var a=a=>{var b,{width:c,height:d}=a[0].contentRect;z(c,d),null==(b=w.current)||b.call(w,c,d)};q>0&&(a=g()(a,q,{trailing:!0,leading:!1}));var b=new ResizeObserver(a),{width:c,height:d}=v.current.getBoundingClientRect();return z(c,d),b.observe(v.current),()=>{b.disconnect()}},[z,q]);var A=(0,e.useMemo)(()=>{var{containerWidth:a,containerHeight:b}=x;if(a<0||b<0)return null;(0,i.R)((0,h._3)(j)||(0,h._3)(l),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",j,l),(0,i.R)(!c||c>0,"The aspect(%s) must be greater than zero.",c);var d=(0,h._3)(j)?a:j,f=(0,h._3)(l)?b:l;return c&&c>0&&(d?f=d/c:f&&(d=f*c),o&&f>o&&(f=o)),(0,i.R)(d>0||f>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",d,f,j,l,m,n,c),e.Children.map(p,a=>(0,e.cloneElement)(a,{width:d,height:f,style:k({width:d,height:f},a.props.style)}))},[c,p,l,o,n,m,x,j]);return e.createElement("div",{id:r?"".concat(r):void 0,className:(0,d.$)("recharts-responsive-container",s),style:k(k({},u),{},{width:j,height:l,minWidth:m,minHeight:n,maxHeight:o}),ref:v},e.createElement("div",{style:{width:0,height:0,overflow:"visible"}},A))})},6487:(a,b,c)=>{"use strict";c.d(b,{I:()=>P});var d=c(38301);function e(){}function f(a,b,c){a._context.bezierCurveTo((2*a._x0+a._x1)/3,(2*a._y0+a._y1)/3,(a._x0+2*a._x1)/3,(a._y0+2*a._y1)/3,(a._x0+4*a._x1+b)/6,(a._y0+4*a._y1+c)/6)}function g(a){this._context=a}function h(a){this._context=a}function i(a){this._context=a}g.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:f(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:f(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}},h.prototype={areaStart:e,areaEnd:e,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._x2=a,this._y2=b;break;case 1:this._point=2,this._x3=a,this._y3=b;break;case 2:this._point=3,this._x4=a,this._y4=b,this._context.moveTo((this._x0+4*this._x1+a)/6,(this._y0+4*this._y1+b)/6);break;default:f(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}},i.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var c=(this._x0+4*this._x1+a)/6,d=(this._y0+4*this._y1+b)/6;this._line?this._context.lineTo(c,d):this._context.moveTo(c,d);break;case 3:this._point=4;default:f(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}};class j{constructor(a,b){this._context=a,this._x=b}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+a)/2,this._y0,this._x0,b,a,b):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+b)/2,a,this._y0,a,b)}this._x0=a,this._y0=b}}function k(a){this._context=a}function l(a){this._context=a}function m(a){return new l(a)}k.prototype={areaStart:e,areaEnd:e,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(a,b){a*=1,b*=1,this._point?this._context.lineTo(a,b):(this._point=1,this._context.moveTo(a,b))}};function n(a,b,c){var d=a._x1-a._x0,e=b-a._x1,f=(a._y1-a._y0)/(d||e<0&&-0),g=(c-a._y1)/(e||d<0&&-0);return((f<0?-1:1)+(g<0?-1:1))*Math.min(Math.abs(f),Math.abs(g),.5*Math.abs((f*e+g*d)/(d+e)))||0}function o(a,b){var c=a._x1-a._x0;return c?(3*(a._y1-a._y0)/c-b)/2:b}function p(a,b,c){var d=a._x0,e=a._y0,f=a._x1,g=a._y1,h=(f-d)/3;a._context.bezierCurveTo(d+h,e+h*b,f-h,g-h*c,f,g)}function q(a){this._context=a}function r(a){this._context=new s(a)}function s(a){this._context=a}function t(a){this._context=a}function u(a){var b,c,d=a.length-1,e=Array(d),f=Array(d),g=Array(d);for(e[0]=0,f[0]=2,g[0]=a[0]+2*a[1],b=1;b<d-1;++b)e[b]=1,f[b]=4,g[b]=4*a[b]+2*a[b+1];for(e[d-1]=2,f[d-1]=7,g[d-1]=8*a[d-1]+a[d],b=1;b<d;++b)c=e[b]/f[b-1],f[b]-=c,g[b]-=c*g[b-1];for(e[d-1]=g[d-1]/f[d-1],b=d-2;b>=0;--b)e[b]=(g[b]-e[b+1])/f[b];for(b=0,f[d-1]=(a[d]+e[d-1])/2;b<d-1;++b)f[b]=2*a[b+1]-e[b+1];return[e,f]}function v(a,b){this._context=a,this._t=b}l.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:this._context.lineTo(a,b)}}},q.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:p(this,this._t0,o(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){var c=NaN;if(b*=1,(a*=1)!==this._x1||b!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;break;case 2:this._point=3,p(this,o(this,c=n(this,a,b)),c);break;default:p(this,this._t0,c=n(this,a,b))}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b,this._t0=c}}},(r.prototype=Object.create(q.prototype)).point=function(a,b){q.prototype.point.call(this,b,a)},s.prototype={moveTo:function(a,b){this._context.moveTo(b,a)},closePath:function(){this._context.closePath()},lineTo:function(a,b){this._context.lineTo(b,a)},bezierCurveTo:function(a,b,c,d,e,f){this._context.bezierCurveTo(b,a,d,c,f,e)}},t.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var a=this._x,b=this._y,c=a.length;if(c)if(this._line?this._context.lineTo(a[0],b[0]):this._context.moveTo(a[0],b[0]),2===c)this._context.lineTo(a[1],b[1]);else for(var d=u(a),e=u(b),f=0,g=1;g<c;++f,++g)this._context.bezierCurveTo(d[0][f],e[0][f],d[1][f],e[1][f],a[g],b[g]);(this._line||0!==this._line&&1===c)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(a,b){this._x.push(+a),this._y.push(+b)}},v.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,b),this._context.lineTo(a,b);else{var c=this._x*(1-this._t)+a*this._t;this._context.lineTo(c,this._y),this._context.lineTo(c,b)}}this._x=a,this._y=b}};var w=c(72706),x=c(89311),y=c(89196);function z(a){return a[0]}function A(a){return a[1]}function B(a,b){var c=(0,x.A)(!0),d=null,e=m,f=null,g=(0,y.i)(h);function h(h){var i,j,k,l=(h=(0,w.A)(h)).length,m=!1;for(null==d&&(f=e(k=g())),i=0;i<=l;++i)!(i<l&&c(j=h[i],i,h))===m&&((m=!m)?f.lineStart():f.lineEnd()),m&&f.point(+a(j,i,h),+b(j,i,h));if(k)return f=null,k+""||null}return a="function"==typeof a?a:void 0===a?z:(0,x.A)(a),b="function"==typeof b?b:void 0===b?A:(0,x.A)(b),h.x=function(b){return arguments.length?(a="function"==typeof b?b:(0,x.A)(+b),h):a},h.y=function(a){return arguments.length?(b="function"==typeof a?a:(0,x.A)(+a),h):b},h.defined=function(a){return arguments.length?(c="function"==typeof a?a:(0,x.A)(!!a),h):c},h.curve=function(a){return arguments.length?(e=a,null!=d&&(f=e(d)),h):e},h.context=function(a){return arguments.length?(null==a?d=f=null:f=e(d=a),h):d},h}function C(a,b,c){var d=null,e=(0,x.A)(!0),f=null,g=m,h=null,i=(0,y.i)(j);function j(j){var k,l,m,n,o,p=(j=(0,w.A)(j)).length,q=!1,r=Array(p),s=Array(p);for(null==f&&(h=g(o=i())),k=0;k<=p;++k){if(!(k<p&&e(n=j[k],k,j))===q)if(q=!q)l=k,h.areaStart(),h.lineStart();else{for(h.lineEnd(),h.lineStart(),m=k-1;m>=l;--m)h.point(r[m],s[m]);h.lineEnd(),h.areaEnd()}q&&(r[k]=+a(n,k,j),s[k]=+b(n,k,j),h.point(d?+d(n,k,j):r[k],c?+c(n,k,j):s[k]))}if(o)return h=null,o+""||null}function k(){return B().defined(e).curve(g).context(f)}return a="function"==typeof a?a:void 0===a?z:(0,x.A)(+a),b="function"==typeof b?b:void 0===b?(0,x.A)(0):(0,x.A)(+b),c="function"==typeof c?c:void 0===c?A:(0,x.A)(+c),j.x=function(b){return arguments.length?(a="function"==typeof b?b:(0,x.A)(+b),d=null,j):a},j.x0=function(b){return arguments.length?(a="function"==typeof b?b:(0,x.A)(+b),j):a},j.x1=function(a){return arguments.length?(d=null==a?null:"function"==typeof a?a:(0,x.A)(+a),j):d},j.y=function(a){return arguments.length?(b="function"==typeof a?a:(0,x.A)(+a),c=null,j):b},j.y0=function(a){return arguments.length?(b="function"==typeof a?a:(0,x.A)(+a),j):b},j.y1=function(a){return arguments.length?(c=null==a?null:"function"==typeof a?a:(0,x.A)(+a),j):c},j.lineX0=j.lineY0=function(){return k().x(a).y(b)},j.lineY1=function(){return k().x(a).y(c)},j.lineX1=function(){return k().x(d).y(b)},j.defined=function(a){return arguments.length?(e="function"==typeof a?a:(0,x.A)(!!a),j):e},j.curve=function(a){return arguments.length?(g=a,null!=f&&(h=g(f)),j):g},j.context=function(a){return arguments.length?(null==a?f=h=null:h=g(f=a),j):f},j}var D=c(43249),E=c(61188),F=c(22688),G=c(53053),H=c(41800);function I(){return(I=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function J(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function K(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?J(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):J(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var L={curveBasisClosed:function(a){return new h(a)},curveBasisOpen:function(a){return new i(a)},curveBasis:function(a){return new g(a)},curveBumpX:function(a){return new j(a,!0)},curveBumpY:function(a){return new j(a,!1)},curveLinearClosed:function(a){return new k(a)},curveLinear:m,curveMonotoneX:function(a){return new q(a)},curveMonotoneY:function(a){return new r(a)},curveNatural:function(a){return new t(a)},curveStep:function(a){return new v(a,.5)},curveStepAfter:function(a){return new v(a,1)},curveStepBefore:function(a){return new v(a,0)}},M=a=>(0,G.H)(a.x)&&(0,G.H)(a.y),N=a=>a.x,O=a=>a.y,P=a=>{var{className:b,points:c,path:e,pathRef:f}=a;if((!c||!c.length)&&!e)return null;var g=c&&c.length?(a=>{var b,{type:c="linear",points:d=[],baseLine:e,layout:f,connectNulls:g=!1}=a,h=((a,b)=>{if("function"==typeof a)return a;var c="curve".concat((0,F.Zb)(a));return("curveMonotone"===c||"curveBump"===c)&&b?L["".concat(c).concat("vertical"===b?"Y":"X")]:L[c]||m})(c,f),i=g?d.filter(M):d;if(Array.isArray(e)){var j=g?e.filter(a=>M(a)):e,k=i.map((a,b)=>K(K({},a),{},{base:j[b]}));return(b="vertical"===f?C().y(O).x1(N).x0(a=>a.base.x):C().x(N).y1(O).y0(a=>a.base.y)).defined(M).curve(h),b(k)}return(b="vertical"===f&&(0,F.Et)(e)?C().y(O).x1(N).x0(e):(0,F.Et)(e)?C().x(N).y1(O).y0(e):B().x(N).y(O)).defined(M).curve(h),b(i)})(a):e;return d.createElement("path",I({},(0,H.u)(a),(0,E._U)(a),{className:(0,D.$)("recharts-curve",b),d:null===g?void 0:g,ref:f}))}},7440:(a,b,c)=>{"use strict";c.d(b,{$7:()=>l,Ru:()=>k,uZ:()=>j});var d=c(53968),e=c(46279),f=c(76265),g=c(40749),h=c(60343),i=c(33648),j=(0,d.VP)("keyDown"),k=(0,d.VP)("focus"),l=(0,d.Nc)();l.startListening({actionCreator:j,effect:(a,b)=>{var c=b.getState();if(!1!==c.rootProps.accessibilityLayer){var{keyboardInteraction:d}=c.tooltip,j=a.payload;if("ArrowRight"===j||"ArrowLeft"===j||"Enter"===j){var k=Number((0,i.P)(d,(0,f.n4)(c))),l=(0,f.R4)(c);if("Enter"===j){var m=(0,g.pg)(c,"axis","hover",String(d.index));b.dispatch((0,e.o4)({active:!d.active,activeIndex:d.index,activeDataKey:d.dataKey,activeCoordinate:m}));return}var n=k+("ArrowRight"===j?1:-1)*("left-to-right"===(0,h._y)(c)?1:-1);if(null!=l&&!(n>=l.length)&&!(n<0)){var o=(0,g.pg)(c,"axis","hover",String(n));b.dispatch((0,e.o4)({active:!0,activeIndex:n.toString(),activeDataKey:void 0,activeCoordinate:o}))}}}}}),l.startListening({actionCreator:k,effect:(a,b)=>{var c=b.getState();if(!1!==c.rootProps.accessibilityLayer){var{keyboardInteraction:d}=c.tooltip;if(!d.active&&null==d.index){var f=(0,g.pg)(c,"axis","hover",String("0"));b.dispatch((0,e.o4)({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:f}))}}}})},8693:(a,b,c)=>{"use strict";c.d(b,{HS:()=>g,LF:()=>e,z3:()=>f});var d=c(54985),e=a=>a.chartData,f=(0,d.Mz)([e],a=>{var b=null!=a.chartData?a.chartData.length-1:0;return{chartData:a.chartData,computedData:a.computedData,dataEndIndex:b,dataStartIndex:0}}),g=(a,b,c,d)=>d?f(a):e(a)},9356:(a,b,c)=>{a.exports=c(63724).range},10003:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isPlainObject=function(a){if("object"!=typeof a||null==a)return!1;if(null===Object.getPrototypeOf(a))return!0;if("[object Object]"!==Object.prototype.toString.call(a)){let b=a[Symbol.toStringTag];return null!=b&&!!Object.getOwnPropertyDescriptor(a,Symbol.toStringTag)?.writable&&a.toString()===`[object ${b}]`}let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b}},10680:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isDeepKey=function(a){switch(typeof a){case"number":case"symbol":return!1;case"string":return a.includes(".")||a.includes("[")||a.includes("]")}}},11267:(a,b,c)=>{"use strict";c.d(b,{p:()=>e}),c(38301),c(21252);var d=c(56998);function e(a){return(0,d.j)(),null}},11814:(a,b,c)=>{"use strict";c.d(b,{B_:()=>e,JK:()=>f,Vp:()=>i,gX:()=>g,hF:()=>h});var d=(0,c(53968).Z0)({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(a,b){a.layoutType=b.payload},setChartSize(a,b){a.width=b.payload.width,a.height=b.payload.height},setMargin(a,b){a.margin.top=b.payload.top,a.margin.right=b.payload.right,a.margin.bottom=b.payload.bottom,a.margin.left=b.payload.left},setScale(a,b){a.scale=b.payload}}}),{setMargin:e,setLayout:f,setChartSize:g,setScale:h}=d.actions,i=d.reducer},13258:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isObjectLike=function(a){return"object"==typeof a&&null!==a}},13770:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.noop=function(){}},13778:(a,b,c)=>{"use strict";c.d(b,{x:()=>g,y:()=>f});var d=c(53968),e=c(76265),f=(0,d.VP)("externalEvent"),g=(0,d.Nc)();g.startListening({actionCreator:f,effect:(a,b)=>{if(null!=a.payload.handler){var c=b.getState(),d={activeCoordinate:(0,e.eE)(c),activeDataKey:(0,e.Xb)(c),activeIndex:(0,e.A2)(c),activeLabel:(0,e.BZ)(c),activeTooltipIndex:(0,e.A2)(c),isTooltipActive:(0,e.yn)(c)};a.payload.handler(d,a.payload.reactEvent)}}})},14228:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(10680),e=c(96061),f=c(49560),g=c(77241);b.has=function(a,b){let c;if(0===(c=Array.isArray(b)?b:"string"==typeof b&&d.isDeepKey(b)&&a?.[b]==null?g.toPath(b):[b]).length)return!1;let h=a;for(let a=0;a<c.length;a++){let b=c[a];if((null==h||!Object.hasOwn(h,b))&&!((Array.isArray(h)||f.isArguments(h))&&e.isIndex(b)&&b<h.length))return!1;h=h[b]}return!0}},14343:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},14851:(a,b,c)=>{"use strict";c.d(b,{JN:()=>d,_5:()=>e,eC:()=>h,gY:()=>f,hX:()=>k,iO:()=>i,lZ:()=>j,pH:()=>l,x3:()=>g});var d=a=>a.rootProps.maxBarSize,e=a=>a.rootProps.barGap,f=a=>a.rootProps.barCategoryGap,g=a=>a.rootProps.barSize,h=a=>a.rootProps.stackOffset,i=a=>a.options.chartName,j=a=>a.rootProps.syncId,k=a=>a.rootProps.syncMethod,l=a=>a.options.eventEmitter},15217:(a,b)=>{"use strict";function c(a){let{ampFirst:b=!1,hybrid:c=!1,hasQuery:d=!1}=void 0===a?{}:a;return b||c&&d}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"isInAmpMode",{enumerable:!0,get:function(){return c}})},15261:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isLength=function(a){return Number.isSafeInteger(a)&&a>=0}},15379:(a,b,c)=>{"use strict";c.d(b,{Kp:()=>o,W7:()=>k,WX:()=>q,fz:()=>p,rY:()=>m,sk:()=>i,yi:()=>l}),c(38301);var d=c(56998),e=c(92173),f=c(35268),g=c(53530),h=c(86941),i=()=>{var a,b=(0,g.r)(),c=(0,d.G)(e.Ds),f=(0,d.G)(h.U),i=null==(a=(0,d.G)(h.C))?void 0:a.padding;return b&&f&&i?{width:f.width-i.left-i.right,height:f.height-i.top-i.bottom,x:i.left,y:i.top}:c},j={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},k=()=>{var a;return null!=(a=(0,d.G)(e.HZ))?a:j},l=()=>(0,d.G)(f.Lp),m=()=>(0,d.G)(f.A$),n={top:0,right:0,bottom:0,left:0},o=()=>{var a;return null!=(a=(0,d.G)(a=>a.layout.margin))?a:n},p=a=>a.layout.layoutType,q=()=>(0,d.G)(p)},17906:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(63409);function e(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function f(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?e(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):e(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var g=(a,b,c)=>a+(b-a)*c,h=a=>{var{from:b,to:c}=a;return b!==c},i=(a,b,c)=>{var e=(0,d.s8)((b,c)=>{if(h(c)){var[d,e]=a(c.from,c.to,c.velocity);return f(f({},c),{},{from:d,velocity:e})}return c},b);return c<1?(0,d.s8)((a,b)=>h(b)?f(f({},b),{},{velocity:g(b.velocity,e[a].velocity,c),from:g(b.from,e[a].from,c)}):b,b):i(a,e,c-1)};let j=(a,b,c,e,j,k)=>{var l=(0,d.mP)(a,b);return!0===c.isStepper?function(a,b,c,e,g,j){var k,l=e.reduce((c,d)=>f(f({},c),{},{[d]:{from:a[d],velocity:0,to:b[d]}}),{}),m=null,n=e=>{k||(k=e);var o=(e-k)/c.dt;l=i(c,l,o),g(f(f(f({},a),b),(0,d.s8)((a,b)=>b.from,l))),k=e,Object.values(l).filter(h).length&&(m=j.setTimeout(n))};return()=>(m=j.setTimeout(n),()=>{m()})}(a,b,c,l,j,k):function(a,b,c,e,h,i,j){var k,l=null,m=h.reduce((c,d)=>f(f({},c),{},{[d]:[a[d],b[d]]}),{}),n=h=>{k||(k=h);var o=(h-k)/e,p=(0,d.s8)((a,b)=>g(...b,c(o)),m);if(i(f(f(f({},a),b),p)),o<1)l=j.setTimeout(n);else{var q=(0,d.s8)((a,b)=>g(...b,c(1)),m);i(f(f(f({},a),b),q))}};return()=>(l=j.setTimeout(n),()=>{l()})}(a,b,c,e,l,j,k)}},18189:(a,b,c)=>{"use strict";c.d(b,{TK:()=>h});var d=c(38301),e=c(91282),f=c(56998),g=c(53530),h=a=>{var{chartData:b}=a,c=(0,f.j)(),h=(0,g.r)();return(0,d.useEffect)(()=>h?()=>{}:(c((0,e.hq)(b)),()=>{c((0,e.hq)(void 0))}),[b,c,h]),null}},18355:(a,b,c)=>{"use strict";a.exports=c(56796).vendored.contexts.RouterContext},19746:(a,b,c)=>{"use strict";a.exports=c(56796).vendored.contexts.HeadManagerContext},20853:(a,b,c)=>{"use strict";c.d(b,{s:()=>f}),c(38301);var d=c(53530);c(11814);var e=c(56998);function f(a){var{layout:b,width:c,height:f,margin:g}=a;return(0,e.j)(),(0,d.r)(),null}},20888:(a,b,c)=>{"use strict";c.d(b,{L:()=>J});var d=c(38301),e=c(15379),f=c(56998),g=c(53530),h=c(99539),i=c(86941),j=c(53053),k=["children"];function l(){return(l=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var m={width:"100%",height:"100%"},n=(0,d.forwardRef)((a,b)=>{var c,g,i=(0,e.yi)(),k=(0,e.rY)(),n=(0,f.G)(a=>a.rootProps.accessibilityLayer);if(!(0,j.F)(i)||!(0,j.F)(k))return null;var{children:o,otherAttributes:p,title:q,desc:r}=a;return c="number"==typeof p.tabIndex?p.tabIndex:n?0:void 0,g="string"==typeof p.role?p.role:n?"application":void 0,d.createElement(h.u,l({},p,{title:q,desc:r,role:g,tabIndex:c,width:i,height:k,style:m,ref:b}),o)}),o=a=>{var{children:b}=a,c=(0,f.G)(i.U);if(!c)return null;var{width:e,height:g,y:j,x:k}=c;return d.createElement(h.u,{width:e,height:g,x:k,y:j},b)},p=(0,d.forwardRef)((a,b)=>{var{children:c}=a,e=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,k);return(0,g.r)()?d.createElement(o,null,c):d.createElement(n,l({ref:b},e),c)}),q=c(43249),r=c(46279),s=c(67852),t=c(14851);new(c(85964)),c(29072);var u=c(76265);c(91282);var v=c(7440),w=c(35268);c(11814);var x=c(13778),y=c(25996),z=(0,d.createContext)(null),A=c(63258);function B(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}var C=(0,d.forwardRef)((a,b)=>{var{children:c,className:g,height:h,onClick:i,onContextMenu:j,onDoubleClick:k,onMouseDown:l,onMouseEnter:m,onMouseLeave:n,onMouseMove:o,onMouseUp:p,onTouchEnd:C,onTouchMove:D,onTouchStart:E,style:F,width:G}=a,H=(0,f.j)(),[I,J]=(0,d.useState)(null),[K,L]=(0,d.useState)(null);(0,f.j)(),(0,f.G)(t.lZ),(0,f.G)(t.pH),(0,f.j)(),(0,f.G)(t.hX),(0,f.G)(u.R4),(0,e.WX)(),(0,e.sk)(),(0,f.G)(a=>a.rootProps.className),(0,f.G)(t.lZ),(0,f.G)(t.pH),(0,f.j)();var M=function(){(0,f.j)();var[a,b]=(0,d.useState)(null);return(0,f.G)(w.et),b}(),N=(0,d.useCallback)(a=>{M(a),"function"==typeof b&&b(a),J(a),L(a)},[M,b,J,L]),O=(0,d.useCallback)(a=>{H((0,s.ky)(a)),H((0,x.y)({handler:i,reactEvent:a}))},[H,i]),P=(0,d.useCallback)(a=>{H((0,s.dj)(a)),H((0,x.y)({handler:m,reactEvent:a}))},[H,m]),Q=(0,d.useCallback)(a=>{H((0,r.xS)()),H((0,x.y)({handler:n,reactEvent:a}))},[H,n]),R=(0,d.useCallback)(a=>{H((0,s.dj)(a)),H((0,x.y)({handler:o,reactEvent:a}))},[H,o]),S=(0,d.useCallback)(()=>{H((0,v.Ru)())},[H]),T=(0,d.useCallback)(a=>{H((0,v.uZ)(a.key))},[H]),U=(0,d.useCallback)(a=>{H((0,x.y)({handler:j,reactEvent:a}))},[H,j]),V=(0,d.useCallback)(a=>{H((0,x.y)({handler:k,reactEvent:a}))},[H,k]),W=(0,d.useCallback)(a=>{H((0,x.y)({handler:l,reactEvent:a}))},[H,l]),X=(0,d.useCallback)(a=>{H((0,x.y)({handler:p,reactEvent:a}))},[H,p]),Y=(0,d.useCallback)(a=>{H((0,x.y)({handler:E,reactEvent:a}))},[H,E]),Z=(0,d.useCallback)(a=>{H((0,y.e)(a)),H((0,x.y)({handler:D,reactEvent:a}))},[H,D]),$=(0,d.useCallback)(a=>{H((0,x.y)({handler:C,reactEvent:a}))},[H,C]);return d.createElement(z.Provider,{value:I},d.createElement(A.t.Provider,{value:K},d.createElement("div",{className:(0,q.$)("recharts-wrapper",g),style:function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?B(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):B(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({position:"relative",cursor:"default",width:G,height:h},F),onClick:O,onContextMenu:U,onDoubleClick:V,onFocus:S,onKeyDown:T,onMouseDown:W,onMouseEnter:P,onMouseLeave:Q,onMouseMove:R,onMouseUp:X,onTouchEnd:$,onTouchMove:Z,onTouchStart:Y,ref:N},c)))}),D=c(22688),E=c(58306),F=(0,d.createContext)(void 0),G=a=>{var{children:b}=a,[c]=(0,d.useState)("".concat((0,D.NF)("recharts"),"-clip")),e=(0,E.oM)();if(null==e)return null;var{x:f,y:g,width:h,height:i}=e;return d.createElement(F.Provider,{value:c},d.createElement("defs",null,d.createElement("clipPath",{id:c},d.createElement("rect",{x:f,y:g,height:i,width:h}))),b)},H=c(41800),I=["children","className","width","height","style","compact","title","desc"],J=(0,d.forwardRef)((a,b)=>{var{children:c,className:e,width:f,height:g,style:h,compact:i,title:j,desc:k}=a,l=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,I),m=(0,H.u)(l);return i?d.createElement(p,{otherAttributes:m,title:j,desc:k},c):d.createElement(C,{className:e,style:h,width:f,height:g,onClick:a.onClick,onMouseLeave:a.onMouseLeave,onMouseEnter:a.onMouseEnter,onMouseMove:a.onMouseMove,onMouseDown:a.onMouseDown,onMouseUp:a.onMouseUp,onContextMenu:a.onContextMenu,onDoubleClick:a.onDoubleClick,onTouchStart:a.onTouchStart,onTouchMove:a.onTouchMove,onTouchEnd:a.onTouchEnd},d.createElement(p,{otherAttributes:m,title:j,desc:k,ref:b},d.createElement(G,null,c)))})},21252:(a,b,c)=>{"use strict";c.d(b,{mZ:()=>h,vE:()=>g});var d=c(53968),e={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},f=(0,d.Z0)({name:"rootProps",initialState:e,reducers:{updateOptions:(a,b)=>{var c;a.accessibilityLayer=b.payload.accessibilityLayer,a.barCategoryGap=b.payload.barCategoryGap,a.barGap=null!=(c=b.payload.barGap)?c:e.barGap,a.barSize=b.payload.barSize,a.maxBarSize=b.payload.maxBarSize,a.stackOffset=b.payload.stackOffset,a.syncId=b.payload.syncId,a.syncMethod=b.payload.syncMethod,a.className=b.payload.className}}}),g=f.reducer,{updateOptions:h}=f.actions},21581:(a,b,c)=>{"use strict";c.d(b,{W:()=>f,h:()=>e});var d=c(54985),e=(0,d.Mz)(a=>a.cartesianAxis.xAxis,a=>Object.values(a)),f=(0,d.Mz)(a=>a.cartesianAxis.yAxis,a=>Object.values(a))},22263:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(72577),e=c(39115),f=c(30276);b.sortBy=function(a,...b){let c=b.length;return c>1&&f.isIterateeCall(a,b[0],b[1])?b=[]:c>2&&f.isIterateeCall(b[0],b[1],b[2])&&(b=[b[0]]),d.orderBy(a,e.flatten(b),["asc"])}},22580:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(44029),e=c(46193),f=c(51911),g=c(92264),h=c(14228);b.matchesProperty=function(a,b){switch(typeof a){case"object":Object.is(a?.valueOf(),-0)&&(a="-0");break;case"number":a=e.toKey(a)}return b=f.cloneDeep(b),function(c){let e=g.get(c,a);return void 0===e?h.has(c,a):void 0===b?void 0===e:d.isMatch(e,b)}}},22688:(a,b,c)=>{"use strict";c.d(b,{CG:()=>n,Dj:()=>o,Et:()=>i,F4:()=>m,GW:()=>p,M8:()=>g,NF:()=>l,Zb:()=>s,_3:()=>h,eP:()=>q,sA:()=>f,uy:()=>r,vh:()=>j});var d=c(37177),e=c.n(d),f=a=>0===a?0:a>0?1:-1,g=a=>"number"==typeof a&&a!=+a,h=a=>"string"==typeof a&&a.indexOf("%")===a.length-1,i=a=>("number"==typeof a||a instanceof Number)&&!g(a),j=a=>i(a)||"string"==typeof a,k=0,l=a=>{var b=++k;return"".concat(a||"").concat(b)},m=function(a,b){var c,d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,e=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!i(a)&&"string"!=typeof a)return d;if(h(a)){if(null==b)return d;var f=a.indexOf("%");c=b*parseFloat(a.slice(0,f))/100}else c=+a;return g(c)&&(c=d),e&&null!=b&&c>b&&(c=b),c},n=a=>{if(!Array.isArray(a))return!1;for(var b=a.length,c={},d=0;d<b;d++)if(c[a[d]])return!0;else c[a[d]]=!0;return!1},o=(a,b)=>i(a)&&i(b)?c=>a+c*(b-a):()=>b;function p(a,b,c){return i(a)&&i(b)?a+c*(b-a):b}function q(a,b,c){if(a&&a.length)return a.find(a=>a&&("function"==typeof b?b(a):e()(a,b))===c)}var r=a=>null==a,s=a=>r(a)?a:"".concat(a.charAt(0).toUpperCase()).concat(a.slice(1))},23339:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(38301);let e=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},f=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var g={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let h=(0,d.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:e,className:h="",children:i,iconNode:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...g,width:b,height:b,stroke:a,strokeWidth:e?24*Number(c)/Number(b):c,className:f("lucide",h),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(i)?i:[i]])),i=(a,b)=>{let c=(0,d.forwardRef)(({className:c,...g},i)=>(0,d.createElement)(h,{ref:i,iconNode:b,className:f(`lucide-${e(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,c),...g}));return c.displayName=e(a),c}},23541:(a,b,c)=>{a.exports=c(95325).isEqual},24515:(a,b,c)=>{"use strict";c.d(b,{default:()=>e.a});var d=c(87516),e=c.n(d)},25283:(a,b,c)=>{"use strict";c.d(b,{J:()=>j});var d=c(38301),e=c(65764);c(17906),c(84224);var f=c(57682),g={begin:0,duration:1e3,easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}},h={t:0},i={t:1};function j(a){var b=(0,e.e)(a,g),{isActive:c,canBegin:j,duration:k,easing:l,begin:m,onAnimationEnd:n,onAnimationStart:o,children:p}=b;(0,f.L)("JavascriptAnimate",b.animationManager);var[q,r]=(0,d.useState)(c?h:i);return(0,d.useRef)(null),p(q.t)}},25401:(a,b,c)=>{"use strict";c.d(b,{D:()=>g});var d=c(60343),e=c(34729),f=c(50088),g=a=>{var b=(0,e.R)(a),c=(0,f.M)(a);return(0,d.Hd)(a,b,c)}},25610:(a,b,c)=>{"use strict";c.d(b,{E:()=>d});var d=(a,b,c)=>c},25996:(a,b,c)=>{"use strict";c.d(b,{e:()=>o,k:()=>p});var d=c(53968),e=c(46279),f=c(98246),g=c(73604),h=c(5142),i=c(71156),j=c(54985),k=c(32187),l=c(75639),m=(0,j.Mz)([l.J],a=>a.tooltipItemPayloads),n=(0,j.Mz)([m,k.x,(a,b,c)=>b,(a,b,c)=>c],(a,b,c,d)=>{var e=a.find(a=>a.settings.dataKey===d);if(null!=e){var{positions:f}=e;if(null!=f)return b(f,c)}}),o=(0,d.VP)("touchMove"),p=(0,d.Nc)();p.startListening({actionCreator:o,effect:(a,b)=>{var c=a.payload,d=b.getState(),j=(0,h.au)(d,d.tooltip.settings.shared);if("axis"===j){var k=(0,f.g)(d,(0,g.w)({clientX:c.touches[0].clientX,clientY:c.touches[0].clientY,currentTarget:c.currentTarget}));(null==k?void 0:k.activeIndex)!=null&&b.dispatch((0,e.Nt)({activeIndex:k.activeIndex,activeDataKey:void 0,activeCoordinate:k.activeCoordinate}))}else if("item"===j){var l,m=c.touches[0],o=document.elementFromPoint(m.clientX,m.clientY);if(!o||!o.getAttribute)return;var p=o.getAttribute(i.F0),q=null!=(l=o.getAttribute(i.um))?l:void 0,r=n(b.getState(),p,q);b.dispatch((0,e.RD)({activeDataKey:q,activeIndex:p,activeCoordinate:r}))}}})},26140:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isTypedArray=function(a){return ArrayBuffer.isView(a)&&!(a instanceof DataView)}},26609:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(41523);b.toFinite=function(a){return a?(a=d.toNumber(a))===1/0||a===-1/0?(a<0?-1:1)*Number.MAX_VALUE:a==a?a:0:0===a?a:0}},26691:(a,b,c)=>{"use strict";c.d(b,{F:()=>g});var d=c(43249);let e=a=>"boolean"==typeof a?`${a}`:0===a?"0":a,f=d.$,g=(a,b)=>c=>{var d;if((null==b?void 0:b.variants)==null)return f(a,null==c?void 0:c.class,null==c?void 0:c.className);let{variants:g,defaultVariants:h}=b,i=Object.keys(g).map(a=>{let b=null==c?void 0:c[a],d=null==h?void 0:h[a];if(null===b)return null;let f=e(b)||e(d);return g[a][f]}),j=c&&Object.entries(c).reduce((a,b)=>{let[c,d]=b;return void 0===d||(a[c]=d),a},{});return f(a,i,null==b||null==(d=b.compoundVariants)?void 0:d.reduce((a,b)=>{let{class:c,className:d,...e}=b;return Object.entries(e).every(a=>{let[b,c]=a;return Array.isArray(c)?c.includes({...h,...j}[b]):({...h,...j})[b]===c})?[...a,c,d]:a},[]),null==c?void 0:c.class,null==c?void 0:c.className)}},27007:(a,b,c)=>{"use strict";c.d(b,{n:()=>f});var d=c(38301),e=c(22688);function f(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",c=(0,d.useRef)((0,e.NF)(b)),f=(0,d.useRef)(a);return f.current!==a&&(c.current=(0,e.NF)(b),f.current=a),c.current}},29072:(a,b,c)=>{"use strict";c.d(b,{dl:()=>i,lJ:()=>h,uN:()=>f});var d=c(53968),e=c(22688);function f(a,b){if(b){var c=Number.parseInt(b,10);if(!(0,e.M8)(c))return null==a?void 0:a[c]}}var g=(0,d.Z0)({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:a=>{null==a.eventEmitter&&(a.eventEmitter=Symbol("rechartsEventEmitter"))}}}),h=g.reducer,{createEventEmitter:i}=g.actions},29160:(a,b,c)=>{"use strict";function d(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function e(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?d(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):d(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}c.d(b,{IZ:()=>g,Kg:()=>f,lY:()=>h,yy:()=>i}),c(38301);var f=Math.PI/180,g=(a,b,c,d)=>({x:a+Math.cos(-f*d)*c,y:b+Math.sin(-f*d)*c}),h=function(a,b){var c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(a-(c.left||0)-(c.right||0)),Math.abs(b-(c.top||0)-(c.bottom||0)))/2},i=(a,b)=>{var c,{x:d,y:f}=a,{radius:g,angle:h}=((a,b)=>{var{x:c,y:d}=a,{cx:e,cy:f}=b,g=((a,b)=>{var{x:c,y:d}=a,{x:e,y:f}=b;return Math.sqrt((c-e)**2+(d-f)**2)})({x:c,y:d},{x:e,y:f});if(g<=0)return{radius:g,angle:0};var h=Math.acos((c-e)/g);return d>f&&(h=2*Math.PI-h),{radius:g,angle:180*h/Math.PI,angleInRadian:h}})({x:d,y:f},b),{innerRadius:i,outerRadius:j}=b;if(g<i||g>j||0===g)return null;var{startAngle:k,endAngle:l}=(a=>{var{startAngle:b,endAngle:c}=a,d=Math.min(Math.floor(b/360),Math.floor(c/360));return{startAngle:b-360*d,endAngle:c-360*d}})(b),m=h;if(k<=l){for(;m>l;)m-=360;for(;m<k;)m+=360;c=m>=k&&m<=l}else{for(;m>k;)m-=360;for(;m<l;)m+=360;c=m>=l&&m<=k}return c?e(e({},b),{},{radius:g,angle:((a,b)=>{var{startAngle:c,endAngle:d}=b;return a+360*Math.min(Math.floor(c/360),Math.floor(d/360))})(m,b)}):null}},30276:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(96061),e=c(77542),f=c(50497),g=c(73665);b.isIterateeCall=function(a,b,c){return!!f.isObject(c)&&(!!("number"==typeof b&&e.isArrayLike(c)&&d.isIndex(b))&&b<c.length||"string"==typeof b&&b in c)&&g.eq(c[b],a)}},31209:(a,b,c)=>{"use strict";c.d(b,{q:()=>e});var d=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"];function e(a){return"string"==typeof a&&d.includes(a)}},32187:(a,b,c)=>{"use strict";c.d(b,{x:()=>d});var d=a=>a.options.tooltipPayloadSearcher},33344:(a,b,c)=>{"use strict";c.d(b,{x:()=>i});var d,e=c(38301),f=c(22688),g=null!=(d=e["useId".toString()])?d:()=>{var[a]=e.useState(()=>(0,f.NF)("uid-"));return a},h=(0,e.createContext)(void 0),i=a=>{var{id:b,type:c,children:d}=a,f=function(a,b){var c=g();return b||(a?"".concat(a,"-").concat(c):c)}("recharts-".concat(c),b);return e.createElement(h.Provider,{value:f},d(f))}},33648:(a,b,c)=>{"use strict";c.d(b,{P:()=>e});var d=c(53053),e=(a,b)=>{var c=null==a?void 0:a.index;if(null==c)return null;var e=Number(c);if(!(0,d.H)(e))return c;var f=Infinity;return b.length>0&&(f=b.length-1),String(Math.max(0,Math.min(e,f)))}},33780:(a,b)=>{"use strict";function c(a){return"symbol"==typeof a?1:null===a?2:void 0===a?3:4*(a!=a)}Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.compareValues=(a,b,d)=>{if(a!==b){let e=c(a),f=c(b);if(e===f&&0===e){if(a<b)return"desc"===d?1:-1;if(a>b)return"desc"===d?-1:1}return"desc"===d?f-e:e-f}return 0}},34548:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(4516),e=c(38649),f=c(38352),g=c(99032),h=c(26140);function i(a,b,c,d=new Map,k){let l=k?.(a,b,c,d);if(void 0!==l)return l;if(g.isPrimitive(a))return a;if(d.has(a))return d.get(a);if(Array.isArray(a)){let b=Array(a.length);d.set(a,b);for(let e=0;e<a.length;e++)b[e]=i(a[e],e,c,d,k);return Object.hasOwn(a,"index")&&(b.index=a.index),Object.hasOwn(a,"input")&&(b.input=a.input),b}if(a instanceof Date)return new Date(a.getTime());if(a instanceof RegExp){let b=new RegExp(a.source,a.flags);return b.lastIndex=a.lastIndex,b}if(a instanceof Map){let b=new Map;for(let[e,f]of(d.set(a,b),a))b.set(e,i(f,e,c,d,k));return b}if(a instanceof Set){let b=new Set;for(let e of(d.set(a,b),a))b.add(i(e,void 0,c,d,k));return b}if("undefined"!=typeof Buffer&&Buffer.isBuffer(a))return a.subarray();if(h.isTypedArray(a)){let b=new(Object.getPrototypeOf(a)).constructor(a.length);d.set(a,b);for(let e=0;e<a.length;e++)b[e]=i(a[e],e,c,d,k);return b}if(a instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&a instanceof SharedArrayBuffer)return a.slice(0);if(a instanceof DataView){let b=new DataView(a.buffer.slice(0),a.byteOffset,a.byteLength);return d.set(a,b),j(b,a,c,d,k),b}if("undefined"!=typeof File&&a instanceof File){let b=new File([a],a.name,{type:a.type});return d.set(a,b),j(b,a,c,d,k),b}if(a instanceof Blob){let b=new Blob([a],{type:a.type});return d.set(a,b),j(b,a,c,d,k),b}if(a instanceof Error){let b=new a.constructor;return d.set(a,b),b.message=a.message,b.name=a.name,b.stack=a.stack,b.cause=a.cause,j(b,a,c,d,k),b}if("object"==typeof a&&function(a){switch(e.getTag(a)){case f.argumentsTag:case f.arrayTag:case f.arrayBufferTag:case f.dataViewTag:case f.booleanTag:case f.dateTag:case f.float32ArrayTag:case f.float64ArrayTag:case f.int8ArrayTag:case f.int16ArrayTag:case f.int32ArrayTag:case f.mapTag:case f.numberTag:case f.objectTag:case f.regexpTag:case f.setTag:case f.stringTag:case f.symbolTag:case f.uint8ArrayTag:case f.uint8ClampedArrayTag:case f.uint16ArrayTag:case f.uint32ArrayTag:return!0;default:return!1}}(a)){let b=Object.create(Object.getPrototypeOf(a));return d.set(a,b),j(b,a,c,d,k),b}return a}function j(a,b,c=a,e,f){let g=[...Object.keys(b),...d.getSymbols(b)];for(let d=0;d<g.length;d++){let h=g[d],j=Object.getOwnPropertyDescriptor(a,h);(null==j||j.writable)&&(a[h]=i(b[h],h,c,e,f))}}b.cloneDeepWith=function(a,b){return i(a,void 0,a,new Map,b)},b.cloneDeepWithImpl=i,b.copyProperties=j},34729:(a,b,c)=>{"use strict";c.d(b,{R:()=>e});var d=c(15379),e=a=>{var b=(0,d.fz)(a);return"horizontal"===b?"xAxis":"vertical"===b?"yAxis":"centric"===b?"angleAxis":"radiusAxis"}},35268:(a,b,c)=>{"use strict";c.d(b,{A$:()=>e,HK:()=>g,Lp:()=>d,et:()=>f});var d=a=>a.layout.width,e=a=>a.layout.height,f=a=>a.layout.scale,g=a=>a.layout.margin},35538:(a,b,c)=>{"use strict";c.d(b,{J:()=>ab});var d=c(38301);c(90478);var e={notify(){},get:()=>[]},f="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,g="undefined"!=typeof navigator&&"ReactNative"===navigator.product,h=f||g?d.useLayoutEffect:d.useEffect;Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var i=Symbol.for("react-redux-context"),j="undefined"!=typeof globalThis?globalThis:{},k=function(){if(!d.createContext)return{};let a=j[i]??=new Map,b=a.get(d.createContext);return b||(b=d.createContext(null),a.set(d.createContext,b)),b}(),l=function(a){let{children:b,context:c,serverState:f,store:g}=a,i=d.useMemo(()=>{let a=function(a,b){let c,d=e,f=0,g=!1;function h(){k.onStateChange&&k.onStateChange()}function i(){if(f++,!c){let b,e;c=a.subscribe(h),b=null,e=null,d={clear(){b=null,e=null},notify(){let a=b;for(;a;)a.callback(),a=a.next},get(){let a=[],c=b;for(;c;)a.push(c),c=c.next;return a},subscribe(a){let c=!0,d=e={callback:a,next:null,prev:e};return d.prev?d.prev.next=d:b=d,function(){c&&null!==b&&(c=!1,d.next?d.next.prev=d.prev:e=d.prev,d.prev?d.prev.next=d.next:b=d.next)}}}}}function j(){f--,c&&0===f&&(c(),c=void 0,d.clear(),d=e)}let k={addNestedSub:function(a){i();let b=d.subscribe(a),c=!1;return()=>{c||(c=!0,b(),j())}},notifyNestedSubs:function(){d.notify()},handleChangeWrapper:h,isSubscribed:function(){return g},trySubscribe:function(){g||(g=!0,i())},tryUnsubscribe:function(){g&&(g=!1,j())},getListeners:()=>d};return k}(g);return{store:g,subscription:a,getServerState:f?()=>f:void 0}},[g,f]),j=d.useMemo(()=>g.getState(),[g]);return h(()=>{let{subscription:a}=i;return a.onStateChange=a.notifyNestedSubs,a.trySubscribe(),j!==g.getState()&&a.notifyNestedSubs(),()=>{a.tryUnsubscribe(),a.onStateChange=void 0}},[i,j]),d.createElement((c||k).Provider,{value:i},b)},m=c(48369),n=c(53968),o=c(29072),p=c(46279),q=c(91282),r=c(11814),s=c(67852);function t(a,b){return b instanceof HTMLElement?"HTMLElement <".concat(b.tagName,' class="').concat(b.className,'">'):b===window?"global.window":b}var u=c(55111),v=c(61185),w=c(72001),x=(0,n.Z0)({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(a,b)=>{a.dots.push(b.payload)},removeDot:(a,b)=>{var c=(0,w.ss)(a).dots.findIndex(a=>a===b.payload);-1!==c&&a.dots.splice(c,1)},addArea:(a,b)=>{a.areas.push(b.payload)},removeArea:(a,b)=>{var c=(0,w.ss)(a).areas.findIndex(a=>a===b.payload);-1!==c&&a.areas.splice(c,1)},addLine:(a,b)=>{a.lines.push(b.payload)},removeLine:(a,b)=>{var c=(0,w.ss)(a).lines.findIndex(a=>a===b.payload);-1!==c&&a.lines.splice(c,1)}}}),{addDot:y,removeDot:z,addArea:A,removeArea:B,addLine:C,removeLine:D}=x.actions,E=x.reducer,F={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},G=(0,n.Z0)({name:"brush",initialState:F,reducers:{setBrushSettings:(a,b)=>null==b.payload?F:b.payload}}),{setBrushSettings:H}=G.actions,I=G.reducer,J=c(75959),K=c(21252),L=(0,n.Z0)({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(a,b){a.radiusAxis[b.payload.id]=(0,w.h4)(b.payload)},removeRadiusAxis(a,b){delete a.radiusAxis[b.payload.id]},addAngleAxis(a,b){a.angleAxis[b.payload.id]=(0,w.h4)(b.payload)},removeAngleAxis(a,b){delete a.angleAxis[b.payload.id]}}}),{addRadiusAxis:M,removeRadiusAxis:N,addAngleAxis:O,removeAngleAxis:P}=L.actions,Q=L.reducer,R=c(74030),S=c(7440),T=c(13778),U=c(25996),V=(0,n.Z0)({name:"errorBars",initialState:{},reducers:{addErrorBar:(a,b)=>{var{itemId:c,errorBar:d}=b.payload;a[c]||(a[c]=[]),a[c].push(d)},removeErrorBar:(a,b)=>{var{itemId:c,errorBar:d}=b.payload;a[c]&&(a[c]=a[c].filter(a=>a.dataKey!==d.dataKey||a.direction!==d.direction))}}}),{addErrorBar:W,removeErrorBar:X}=V.actions,Y=V.reducer,Z=(0,m.HY)({brush:I,cartesianAxis:u.CA,chartData:q.LV,errorBars:Y,graphicalItems:v.iZ,layout:r.Vp,legend:J.CU,options:o.lJ,polarAxis:Q,polarOptions:R.J,referenceElements:E,rootProps:K.vE,tooltip:p.En}),$=function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return(0,n.U1)({reducer:Z,preloadedState:a,middleware:a=>a({serializableCheck:!1}).concat([s.YF.middleware,s.fP.middleware,S.$7.middleware,T.x.middleware,U.k.middleware]),devTools:{serialize:{replacer:t},name:"recharts-".concat(b)}})},_=c(53530),aa=c(50625);function ab(a){var{preloadedState:b,children:c,reduxStoreName:e}=a,f=(0,_.r)(),g=(0,d.useRef)(null);if(f)return c;null==g.current&&(g.current=$(b,e));var h=aa.E;return d.createElement(l,{context:h,store:g.current},c)}},37177:(a,b,c)=>{a.exports=c(92264).get},38352:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.argumentsTag="[object Arguments]",b.arrayBufferTag="[object ArrayBuffer]",b.arrayTag="[object Array]",b.bigInt64ArrayTag="[object BigInt64Array]",b.bigUint64ArrayTag="[object BigUint64Array]",b.booleanTag="[object Boolean]",b.dataViewTag="[object DataView]",b.dateTag="[object Date]",b.errorTag="[object Error]",b.float32ArrayTag="[object Float32Array]",b.float64ArrayTag="[object Float64Array]",b.functionTag="[object Function]",b.int16ArrayTag="[object Int16Array]",b.int32ArrayTag="[object Int32Array]",b.int8ArrayTag="[object Int8Array]",b.mapTag="[object Map]",b.numberTag="[object Number]",b.objectTag="[object Object]",b.regexpTag="[object RegExp]",b.setTag="[object Set]",b.stringTag="[object String]",b.symbolTag="[object Symbol]",b.uint16ArrayTag="[object Uint16Array]",b.uint32ArrayTag="[object Uint32Array]",b.uint8ArrayTag="[object Uint8Array]",b.uint8ClampedArrayTag="[object Uint8ClampedArray]"},38649:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.getTag=function(a){return null==a?void 0===a?"[object Undefined]":"[object Null]":Object.prototype.toString.call(a)}},39115:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.flatten=function(a,b=1){let c=[],d=Math.floor(b),e=(a,b)=>{for(let f=0;f<a.length;f++){let g=a[f];Array.isArray(g)&&b<d?e(g,b+1):c.push(g)}};return e(a,0),c}},39683:(a,b,c)=>{"use strict";c.d(b,{N:()=>d});var d=(a,b)=>b},40106:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"Image",{enumerable:!0,get:function(){return u}});let d=c(35288),e=c(55823),f=c(21124),g=e._(c(38301)),h=d._(c(23312)),i=d._(c(63725)),j=c(63974),k=c(3001),l=c(456);c(21507);let m=c(18355),n=d._(c(49656)),o=c(49427),p={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function q(a,b,c,d,e,f,g){let h=null==a?void 0:a.src;a&&a["data-loaded-src"]!==h&&(a["data-loaded-src"]=h,("decode"in a?a.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(a.parentElement&&a.isConnected){if("empty"!==b&&e(!0),null==c?void 0:c.current){let b=new Event("load");Object.defineProperty(b,"target",{writable:!1,value:a});let d=!1,e=!1;c.current({...b,nativeEvent:b,currentTarget:a,target:a,isDefaultPrevented:()=>d,isPropagationStopped:()=>e,persist:()=>{},preventDefault:()=>{d=!0,b.preventDefault()},stopPropagation:()=>{e=!0,b.stopPropagation()}})}(null==d?void 0:d.current)&&d.current(a)}}))}function r(a){return g.use?{fetchPriority:a}:{fetchpriority:a}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let s=(0,g.forwardRef)((a,b)=>{let{src:c,srcSet:d,sizes:e,height:h,width:i,decoding:j,className:k,style:l,fetchPriority:m,placeholder:n,loading:p,unoptimized:s,fill:t,onLoadRef:u,onLoadingCompleteRef:v,setBlurComplete:w,setShowAltText:x,sizesInput:y,onLoad:z,onError:A,...B}=a,C=(0,g.useCallback)(a=>{a&&(A&&(a.src=a.src),a.complete&&q(a,n,u,v,w,s,y))},[c,n,u,v,w,A,s,y]),D=(0,o.useMergedRef)(b,C);return(0,f.jsx)("img",{...B,...r(m),loading:p,width:i,height:h,decoding:j,"data-nimg":t?"fill":"1",className:k,style:l,sizes:e,srcSet:d,src:c,ref:D,onLoad:a=>{q(a.currentTarget,n,u,v,w,s,y)},onError:a=>{x(!0),"empty"!==n&&w(!0),A&&A(a)}})});function t(a){let{isAppRouter:b,imgAttributes:c}=a,d={as:"image",imageSrcSet:c.srcSet,imageSizes:c.sizes,crossOrigin:c.crossOrigin,referrerPolicy:c.referrerPolicy,...r(c.fetchPriority)};return b&&h.default.preload?(h.default.preload(c.src,d),null):(0,f.jsx)(i.default,{children:(0,f.jsx)("link",{rel:"preload",href:c.srcSet?void 0:c.src,...d},"__nimg-"+c.src+c.srcSet+c.sizes)})}let u=(0,g.forwardRef)((a,b)=>{let c=(0,g.useContext)(m.RouterContext),d=(0,g.useContext)(l.ImageConfigContext),e=(0,g.useMemo)(()=>{var a;let b=p||d||k.imageConfigDefault,c=[...b.deviceSizes,...b.imageSizes].sort((a,b)=>a-b),e=b.deviceSizes.sort((a,b)=>a-b),f=null==(a=b.qualities)?void 0:a.sort((a,b)=>a-b);return{...b,allSizes:c,deviceSizes:e,qualities:f}},[d]),{onLoad:h,onLoadingComplete:i}=a,o=(0,g.useRef)(h);(0,g.useEffect)(()=>{o.current=h},[h]);let q=(0,g.useRef)(i);(0,g.useEffect)(()=>{q.current=i},[i]);let[r,u]=(0,g.useState)(!1),[v,w]=(0,g.useState)(!1),{props:x,meta:y}=(0,j.getImgProps)(a,{defaultLoader:n.default,imgConf:e,blurComplete:r,showAltText:v});return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(s,{...x,unoptimized:y.unoptimized,placeholder:y.placeholder,fill:y.fill,onLoadRef:o,onLoadingCompleteRef:q,setBlurComplete:u,setShowAltText:w,sizesInput:a.sizes,ref:b}),y.priority?(0,f.jsx)(t,{isAppRouter:!c,imgAttributes:x}):null]})});("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},40749:(a,b,c)=>{"use strict";c.d(b,{aX:()=>E,pg:()=>C,r1:()=>y});var d=c(54985),e=c(45608),f=c.n(e),g=c(79241),h=c(8693),i=c(76265),j=c(15379),k=c(92173),l=c(35268),m=c(42911),n=c(62761),o=c(33648),p=c(90481),q=c(52063),r=c(32187),s=c(75639),t=c(61508),u=c(25401),v=(a,b)=>b,w=(a,b,c)=>c,x=(a,b,c,d)=>d,y=(0,d.Mz)(i.R4,a=>f()(a,a=>a.coordinate)),z=(0,d.Mz)([s.J,v,w,x],n.i),A=(0,d.Mz)([z,i.n4],o.P),B=(0,d.Mz)([s.J,v,w,x],q.q),C=(0,d.Mz)([l.Lp,l.A$,j.fz,k.HZ,i.R4,x,B,r.x],p.o);(0,d.Mz)([z,C],(a,b)=>{var c;return null!=(c=a.coordinate)?c:b});var D=(0,d.Mz)(i.R4,A,m.E);(0,d.Mz)([B,A,h.LF,u.D,D,r.x,v],t.N),(0,d.Mz)([z],a=>({isActive:a.active,activeIndex:a.index}));var E=(a,b,c,d,e,f,h,i)=>{if(a&&b&&d&&e&&f){var j=(0,g.r4)(a.chartX,a.chartY,b,c,i);if(j){var k=(0,g.SW)(j,b),l=(0,g.gH)(k,h,f,d,e),m=(0,g.bk)(b,f,l,j);return{activeIndex:String(l),activeCoordinate:m}}}}},40853:(a,b,c)=>{"use strict";c.d(b,{F:()=>au,L:()=>ao});var d=c(38301),e=c(37177),f=c.n(e),g=c(43249),h=c(54985),i=c(8693),j=c(92173),k=c(79241),l=c(60343),m=c(15379),n=c(39683),o=c(25610),p=c(14851),q=a=>a.graphicalItems.polarItems,r=(0,h.Mz)([n.N,o.E],l.eo),s=(0,h.Mz)([q,l.DP,r],l.ec),t=(0,h.Mz)([s],l.rj),u=(0,h.Mz)([t,i.z3],l.Nk),v=(0,h.Mz)([u,l.DP,s],l.fb),w=(0,h.Mz)([u,l.DP,s],(a,b,c)=>c.length>0?a.flatMap(a=>c.flatMap(c=>{var d;return{value:(0,k.kr)(a,null!=(d=b.dataKey)?d:c.dataKey),errorDomain:[]}})).filter(Boolean):(null==b?void 0:b.dataKey)!=null?a.map(a=>({value:(0,k.kr)(a,b.dataKey),errorDomain:[]})):a.map(a=>({value:a,errorDomain:[]}))),x=()=>void 0,y=(0,h.Mz)([l.DP,l.AV,x,w,x,m.fz,n.N],l.wL),z=(0,h.Mz)([l.DP,m.fz,u,v,p.eC,n.N,y],l.tP),A=(0,h.Mz)([z,l.DP,l.xM],l.xp);function B(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function C(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?B(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):B(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}(0,h.Mz)([l.DP,z,A,n.N],l.g1);var D=(0,h.Mz)([q,(a,b)=>b],(a,b)=>a.filter(a=>"pie"===a.type).find(a=>a.id===b)),E=[],F=(a,b,c)=>(null==c?void 0:c.length)===0?E:c,G=(0,h.Mz)([i.z3,D,F],(a,b,c)=>{var d,{chartData:e}=a;if(null!=b&&((d=(null==b?void 0:b.data)!=null&&b.data.length>0?b.data:e)&&d.length||null==c||(d=c.map(a=>C(C({},b.presentationProps),a.props))),null!=d))return d}),H=(0,h.Mz)([G,D,F],(a,b,c)=>{if(null!=a&&null!=b)return a.map((a,d)=>{var e,f,g=(0,k.kr)(a,b.nameKey,b.name);return f=null!=c&&null!=(e=c[d])&&null!=(e=e.props)&&e.fill?c[d].props.fill:"object"==typeof a&&null!=a&&"fill"in a?a.fill:b.fill,{value:(0,k.uM)(g,b.dataKey),color:f,payload:a,type:b.legendType}})}),I=(0,h.Mz)([G,D,F,j.HZ],(a,b,c,d)=>{if(null!=b&&null!=a)return ao({offset:d,pieSettings:b,displayedData:a,cells:c})}),J=c(56998),K=c(55413),L=c(6487),M=c(81249),N=c(83790),O=c(72677),P=c(4702),Q=c(29160),R=c(22688),S=c(61188),T=c(86852),U=c(96136),V=c(51846),W=c(76265),X=c(76341),Y=c(71156),Z=c(27007),$=c(65764),_=c(33344),aa=c(72650),ab=c(41800),ac=c(25283),ad=["onMouseEnter","onClick","onMouseLeave"],ae=["id"],af=["id"];function ag(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function ah(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function ai(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?ah(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):ah(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function aj(){return(aj=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function ak(a){var b=(0,d.useMemo)(()=>(0,O.aS)(a.children,N.f),[a.children]),c=(0,J.G)(c=>H(c,a.id,b));return null==c?null:d.createElement(X._,{legendPayload:c})}function al(a){var{dataKey:b,nameKey:c,sectors:d,stroke:e,strokeWidth:f,fill:g,name:h,hide:i,tooltipType:j}=a;return{dataDefinedOnItem:null==d?void 0:d.map(a=>a.tooltipPayload),positions:null==d?void 0:d.map(a=>a.tooltipPosition),settings:{stroke:e,strokeWidth:f,fill:g,dataKey:b,nameKey:c,name:(0,k.uM)(h,b),hide:i,type:j,color:g,unit:""}}}function am(a){var{sectors:b,props:c,showLabels:e}=a,{label:f,labelLine:h,dataKey:i}=c;if(!e||!f||!b)return null;var j=(0,ab.u)(c),l=(0,O.J9)(f,!1),m=(0,O.J9)(h,!1),n="object"==typeof f&&"offsetRadius"in f&&f.offsetRadius||20,o=b.map((a,b)=>{var c,e,o=(a.startAngle+a.endAngle)/2,p=(0,Q.IZ)(a.cx,a.cy,a.outerRadius+n,o),q=ai(ai(ai(ai({},j),a),{},{stroke:"none"},l),{},{index:b,textAnchor:(c=p.x)>(e=a.cx)?"start":c<e?"end":"middle"},p),r=ai(ai(ai(ai({},j),a),{},{fill:"none",stroke:a.fill},m),{},{index:b,points:[(0,Q.IZ)(a.cx,a.cy,a.outerRadius,o),p],key:"line"});return d.createElement(K.W,{key:"label-".concat(a.startAngle,"-").concat(a.endAngle,"-").concat(a.midAngle,"-").concat(b)},h&&((a,b)=>{if(d.isValidElement(a))return d.cloneElement(a,b);if("function"==typeof a)return a(b);var c=(0,g.$)("recharts-pie-label-line","boolean"!=typeof a?a.className:"");return d.createElement(L.I,aj({},b,{type:"linear",className:c}))})(h,r),((a,b,c)=>{if(d.isValidElement(a))return d.cloneElement(a,b);var e=c;if("function"==typeof a&&(e=a(b),d.isValidElement(e)))return e;var f=(0,g.$)("recharts-pie-label-text","boolean"!=typeof a&&"function"!=typeof a?a.className:"");return d.createElement(M.E,aj({},b,{alignmentBaseline:"middle",className:f}),e)})(f,q,(0,k.kr)(a,i)))});return d.createElement(K.W,{className:"recharts-pie-labels"},o)}function an(a){var{sectors:b,activeShape:c,inactiveShape:e,allOtherPieProps:f,showLabels:g}=a,h=(0,J.G)(W.A2),{onMouseEnter:i,onClick:j,onMouseLeave:k}=f,l=ag(f,ad),m=(0,U.Cj)(i,f.dataKey),n=(0,U.Pg)(k),o=(0,U.Ub)(j,f.dataKey);return null==b?null:d.createElement(d.Fragment,null,b.map((a,g)=>{if((null==a?void 0:a.startAngle)===0&&(null==a?void 0:a.endAngle)===0&&1!==b.length)return null;var i=c&&String(g)===h,j=i?c:h?e:null,k=ai(ai({},a),{},{stroke:a.stroke,tabIndex:-1,[Y.F0]:g,[Y.um]:f.dataKey});return d.createElement(K.W,aj({tabIndex:-1,className:"recharts-pie-sector"},(0,S.XC)(l,a,g),{onMouseEnter:m(a,g),onMouseLeave:n(a,g),onClick:o(a,g),key:"sector-".concat(null==a?void 0:a.startAngle,"-").concat(null==a?void 0:a.endAngle,"-").concat(a.midAngle,"-").concat(g)}),d.createElement(T.y,aj({option:j,isActive:i,shapeType:"sector"},k)))}),d.createElement(am,{sectors:b,props:f,showLabels:g}))}function ao(a){var b,c,d,{pieSettings:e,displayedData:f,cells:g,offset:h}=a,{cornerRadius:i,startAngle:j,endAngle:l,dataKey:m,nameKey:n,tooltipType:o}=e,p=Math.abs(e.minAngle),q=(0,R.sA)(l-j)*Math.min(Math.abs(l-j),360),r=Math.abs(q),s=f.length<=1?0:null!=(b=e.paddingAngle)?b:0,t=f.filter(a=>0!==(0,k.kr)(a,m,0)).length,u=r-t*p-(r>=360?t:t-1)*s,v=f.reduce((a,b)=>{var c=(0,k.kr)(b,m,0);return a+((0,R.Et)(c)?c:0)},0);return v>0&&(c=f.map((a,b)=>{var c,f=(0,k.kr)(a,m,0),l=(0,k.kr)(a,n,b),r=((a,b,c)=>{let d,e,f;var{top:g,left:h,width:i,height:j}=b,k=(0,Q.lY)(i,j),l=h+(0,R.F4)(a.cx,i,i/2),m=g+(0,R.F4)(a.cy,j,j/2),n=(0,R.F4)(a.innerRadius,k,0);return{cx:l,cy:m,innerRadius:n,outerRadius:(d=c,e=a.outerRadius,f=k,"function"==typeof e?e(d):(0,R.F4)(e,f,.8*f)),maxRadius:a.maxRadius||Math.sqrt(i*i+j*j)/2}})(e,h,a),t=((0,R.Et)(f)?f:0)/v,w=ai(ai({},a),g&&g[b]&&g[b].props),x=(c=b?d.endAngle+(0,R.sA)(q)*s*(0!==f):j)+(0,R.sA)(q)*((0!==f?p:0)+t*u),y=(c+x)/2,z=(r.innerRadius+r.outerRadius)/2,A=[{name:l,value:f,payload:w,dataKey:m,type:o}],B=(0,Q.IZ)(r.cx,r.cy,z,y);return d=ai(ai(ai(ai({},e.presentationProps),{},{percent:t,cornerRadius:i,name:l,tooltipPayload:A,midAngle:y,middleRadius:z,tooltipPosition:B},w),r),{},{value:(0,k.kr)(a,m),startAngle:c,endAngle:x,payload:w,paddingAngle:(0,R.sA)(q)*s})})),c}function ap(a){var{props:b,previousSectorsRef:c}=a,{sectors:e,isAnimationActive:g,animationBegin:h,animationDuration:i,animationEasing:j,activeShape:k,inactiveShape:l,onAnimationStart:m,onAnimationEnd:n}=b,o=(0,Z.n)(b,"recharts-pie-"),p=c.current,[q,r]=(0,d.useState)(!0),s=(0,d.useCallback)(()=>{"function"==typeof n&&n(),r(!1)},[n]),t=(0,d.useCallback)(()=>{"function"==typeof m&&m(),r(!0)},[m]);return d.createElement(ac.J,{begin:h,duration:i,isActive:g,easing:j,onAnimationStart:t,onAnimationEnd:s,key:o},a=>{var g=[],h=(e&&e[0]).startAngle;return e.forEach((b,c)=>{var d=p&&p[c],e=c>0?f()(b,"paddingAngle",0):0;if(d){var i=(0,R.Dj)(d.endAngle-d.startAngle,b.endAngle-b.startAngle),j=ai(ai({},b),{},{startAngle:h+e,endAngle:h+i(a)+e});g.push(j),h=j.endAngle}else{var{endAngle:k,startAngle:l}=b,m=(0,R.Dj)(0,k-l)(a),n=ai(ai({},b),{},{startAngle:h+e,endAngle:h+m+e});g.push(n),h=n.endAngle}}),c.current=g,d.createElement(K.W,null,d.createElement(an,{sectors:g,activeShape:k,inactiveShape:l,allOtherPieProps:b,showLabels:!q}))})}function aq(a){var{sectors:b,isAnimationActive:c,activeShape:e,inactiveShape:f}=a,g=(0,d.useRef)(null),h=g.current;return c&&b&&b.length&&(!h||h!==b)?d.createElement(ap,{props:a,previousSectorsRef:g}):d.createElement(an,{sectors:b,activeShape:e,inactiveShape:f,allOtherPieProps:a,showLabels:!0})}function ar(a){var{hide:b,className:c,rootTabIndex:e}=a,f=(0,g.$)("recharts-pie",c);return b?null:d.createElement(K.W,{tabIndex:e,className:f},d.createElement(aq,a))}var as={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!P.m.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function at(a){var{id:b}=a,c=ag(a,ae),e=(0,d.useMemo)(()=>(0,O.aS)(a.children,N.f),[a.children]),f=(0,J.G)(a=>I(a,b,e));return d.createElement(d.Fragment,null,d.createElement(V.r,{fn:al,args:ai(ai({},a),{},{sectors:f})}),d.createElement(ar,aj({},c,{sectors:f})))}function au(a){var b=(0,$.e)(a,as),{id:c}=b,e=ag(b,af),f=(0,ab.u)(e);return d.createElement(_.x,{id:c,type:"pie"},a=>d.createElement(d.Fragment,null,d.createElement(aa.v,{type:"pie",id:a,data:e.data,dataKey:e.dataKey,hide:e.hide,angleAxisId:0,radiusAxisId:0,name:e.name,nameKey:e.nameKey,tooltipType:e.tooltipType,legendType:e.legendType,fill:e.fill,cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,paddingAngle:e.paddingAngle,minAngle:e.minAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius,cornerRadius:e.cornerRadius,presentationProps:f}),d.createElement(ak,aj({},e,{id:a})),d.createElement(at,aj({},e,{id:a})),e.children))}au.displayName="Pie"},41523:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(1162);b.toNumber=function(a){return d.isSymbol(a)?NaN:Number(a)}},41650:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(1162),e=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,f=/^\w*$/;b.isKey=function(a,b){return!Array.isArray(a)&&(!!("number"==typeof a||"boolean"==typeof a||null==a||d.isSymbol(a))||"string"==typeof a&&(f.test(a)||!e.test(a))||null!=b&&Object.hasOwn(b,a))}},41800:(a,b,c)=>{"use strict";c.d(b,{R:()=>e,u:()=>f});var d=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"];function e(a){return"string"==typeof a&&d.includes(a)}function f(a){return Object.fromEntries(Object.entries(a).filter(a=>{var[b]=a;return e(b)}))}},42797:(a,b,c)=>{"use strict";var d=c(38301),e="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},f=d.useState,g=d.useEffect,h=d.useLayoutEffect,i=d.useDebugValue;function j(a){var b=a.getSnapshot;a=a.value;try{var c=b();return!e(a,c)}catch(a){return!0}}var k="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(a,b){return b()}:function(a,b){var c=b(),d=f({inst:{value:c,getSnapshot:b}}),e=d[0].inst,k=d[1];return h(function(){e.value=c,e.getSnapshot=b,j(e)&&k({inst:e})},[a,c,b]),g(function(){return j(e)&&k({inst:e}),a(function(){j(e)&&k({inst:e})})},[a]),i(c),c};b.useSyncExternalStore=void 0!==d.useSyncExternalStore?d.useSyncExternalStore:k},42911:(a,b,c)=>{"use strict";c.d(b,{E:()=>e});var d=c(22688),e=(a,b)=>{var c,e=Number(b);if(!(0,d.M8)(e)&&null!=b)return e>=0?null==a||null==(c=a[e])?void 0:c.value:void 0}},43249:(a,b,c)=>{"use strict";function d(){for(var a,b,c=0,d="",e=arguments.length;c<e;c++)(a=arguments[c])&&(b=function a(b){var c,d,e="";if("string"==typeof b||"number"==typeof b)e+=b;else if("object"==typeof b)if(Array.isArray(b)){var f=b.length;for(c=0;c<f;c++)b[c]&&(d=a(b[c]))&&(e&&(e+=" "),e+=d)}else for(d in b)b[d]&&(e&&(e+=" "),e+=d);return e}(a))&&(d&&(d+=" "),d+=b);return d}c.d(b,{$:()=>d})},44029:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(47369);b.isMatch=function(a,b){return d.isMatchWith(a,b,()=>void 0)}},44611:(a,b,c)=>{"use strict";c.d(b,{I:()=>d});var d=(a,b)=>{if(a&&b)return null!=a&&a.reversed?[b[1],b[0]]:b}},45215:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(92264);b.property=function(a){return function(b){return d.get(b,a)}}},45608:(a,b,c)=>{a.exports=c(22263).sortBy},45807:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("arrow-down-left",[["path",{d:"M17 7 7 17",key:"15tmo1"}],["path",{d:"M17 17H7V7",key:"1org7z"}]])},46153:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isUnsafeProperty=function(a){return"__proto__"===a}},46193:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.toKey=function(a){return"string"==typeof a||"symbol"==typeof a?a:Object.is(a?.valueOf?.(),-0)?"-0":String(a)}},46279:(a,b,c)=>{"use strict";c.d(b,{E1:()=>q,En:()=>s,Ix:()=>h,ML:()=>n,Nt:()=>o,RD:()=>k,XB:()=>i,jF:()=>p,k_:()=>f,o4:()=>r,oP:()=>l,xS:()=>m});var d=c(53968),e=c(72001),f={active:!1,index:null,dataKey:void 0,coordinate:void 0},g=(0,d.Z0)({name:"tooltip",initialState:{itemInteraction:{click:f,hover:f},axisInteraction:{click:f,hover:f},keyboardInteraction:f,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(a,b){a.tooltipItemPayloads.push((0,e.h4)(b.payload))},removeTooltipEntrySettings(a,b){var c=(0,e.ss)(a).tooltipItemPayloads.indexOf((0,e.h4)(b.payload));c>-1&&a.tooltipItemPayloads.splice(c,1)},setTooltipSettingsState(a,b){a.settings=b.payload},setActiveMouseOverItemIndex(a,b){a.syncInteraction.active=!1,a.keyboardInteraction.active=!1,a.itemInteraction.hover.active=!0,a.itemInteraction.hover.index=b.payload.activeIndex,a.itemInteraction.hover.dataKey=b.payload.activeDataKey,a.itemInteraction.hover.coordinate=b.payload.activeCoordinate},mouseLeaveChart(a){a.itemInteraction.hover.active=!1,a.axisInteraction.hover.active=!1},mouseLeaveItem(a){a.itemInteraction.hover.active=!1},setActiveClickItemIndex(a,b){a.syncInteraction.active=!1,a.itemInteraction.click.active=!0,a.keyboardInteraction.active=!1,a.itemInteraction.click.index=b.payload.activeIndex,a.itemInteraction.click.dataKey=b.payload.activeDataKey,a.itemInteraction.click.coordinate=b.payload.activeCoordinate},setMouseOverAxisIndex(a,b){a.syncInteraction.active=!1,a.axisInteraction.hover.active=!0,a.keyboardInteraction.active=!1,a.axisInteraction.hover.index=b.payload.activeIndex,a.axisInteraction.hover.dataKey=b.payload.activeDataKey,a.axisInteraction.hover.coordinate=b.payload.activeCoordinate},setMouseClickAxisIndex(a,b){a.syncInteraction.active=!1,a.keyboardInteraction.active=!1,a.axisInteraction.click.active=!0,a.axisInteraction.click.index=b.payload.activeIndex,a.axisInteraction.click.dataKey=b.payload.activeDataKey,a.axisInteraction.click.coordinate=b.payload.activeCoordinate},setSyncInteraction(a,b){a.syncInteraction=b.payload},setKeyboardInteraction(a,b){a.keyboardInteraction.active=b.payload.active,a.keyboardInteraction.index=b.payload.activeIndex,a.keyboardInteraction.coordinate=b.payload.activeCoordinate,a.keyboardInteraction.dataKey=b.payload.activeDataKey}}}),{addTooltipEntrySettings:h,removeTooltipEntrySettings:i,setTooltipSettingsState:j,setActiveMouseOverItemIndex:k,mouseLeaveItem:l,mouseLeaveChart:m,setActiveClickItemIndex:n,setMouseOverAxisIndex:o,setMouseClickAxisIndex:p,setSyncInteraction:q,setKeyboardInteraction:r}=g.actions,s=g.reducer},46600:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.identity=function(a){return a}},47268:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},47369:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(44029),e=c(50497),f=c(99032),g=c(73665);function h(a,b,c,d){if(b===a)return!0;switch(typeof b){case"object":return function(a,b,c,d){if(null==b)return!0;if(Array.isArray(b))return i(a,b,c,d);if(b instanceof Map){var e=a,g=b,h=c,k=d;if(0===g.size)return!0;if(!(e instanceof Map))return!1;for(let[a,b]of g.entries())if(!1===h(e.get(a),b,a,e,g,k))return!1;return!0}if(b instanceof Set)return j(a,b,c,d);let l=Object.keys(b);if(null==a)return 0===l.length;if(0===l.length)return!0;if(d&&d.has(b))return d.get(b)===a;d&&d.set(b,a);try{for(let e=0;e<l.length;e++){let g=l[e];if(!f.isPrimitive(a)&&!(g in a)||void 0===b[g]&&void 0!==a[g]||null===b[g]&&null!==a[g]||!c(a[g],b[g],g,a,b,d))return!1}return!0}finally{d&&d.delete(b)}}(a,b,c,d);case"function":if(Object.keys(b).length>0)return h(a,{...b},c,d);return g.eq(a,b);default:if(!e.isObject(a))return g.eq(a,b);if("string"==typeof b)return""===b;return!0}}function i(a,b,c,d){if(0===b.length)return!0;if(!Array.isArray(a))return!1;let e=new Set;for(let f=0;f<b.length;f++){let g=b[f],h=!1;for(let i=0;i<a.length;i++){if(e.has(i))continue;let j=a[i],k=!1;if(c(j,g,f,a,b,d)&&(k=!0),k){e.add(i),h=!0;break}}if(!h)return!1}return!0}function j(a,b,c,d){return 0===b.size||a instanceof Set&&i([...a],[...b],c,d)}b.isMatchWith=function(a,b,c){return"function"!=typeof c?d.isMatch(a,b):h(a,b,function a(b,d,e,f,g,i){let j=c(b,d,e,f,g,i);return void 0!==j?!!j:h(b,d,a,i)},new Map)},b.isSetMatch=j},48369:(a,b,c)=>{"use strict";function d(a){return`Minified Redux error #${a}; visit https://redux.js.org/Errors?code=${a} for the full message or use the non-minified dev environment for full errors. `}c.d(b,{HY:()=>j,Qd:()=>h,Tw:()=>l,Zz:()=>k,ve:()=>m,y$:()=>i});var e="function"==typeof Symbol&&Symbol.observable||"@@observable",f=()=>Math.random().toString(36).substring(7).split("").join("."),g={INIT:`@@redux/INIT${f()}`,REPLACE:`@@redux/REPLACE${f()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${f()}`};function h(a){if("object"!=typeof a||null===a)return!1;let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b||null===Object.getPrototypeOf(a)}function i(a,b,c){if("function"!=typeof a)throw Error(d(2));if("function"==typeof b&&"function"==typeof c||"function"==typeof c&&"function"==typeof arguments[3])throw Error(d(0));if("function"==typeof b&&void 0===c&&(c=b,b=void 0),void 0!==c){if("function"!=typeof c)throw Error(d(1));return c(i)(a,b)}let f=a,j=b,k=new Map,l=k,m=0,n=!1;function o(){l===k&&(l=new Map,k.forEach((a,b)=>{l.set(b,a)}))}function p(){if(n)throw Error(d(3));return j}function q(a){if("function"!=typeof a)throw Error(d(4));if(n)throw Error(d(5));let b=!0;o();let c=m++;return l.set(c,a),function(){if(b){if(n)throw Error(d(6));b=!1,o(),l.delete(c),k=null}}}function r(a){if(!h(a))throw Error(d(7));if(void 0===a.type)throw Error(d(8));if("string"!=typeof a.type)throw Error(d(17));if(n)throw Error(d(9));try{n=!0,j=f(j,a)}finally{n=!1}return(k=l).forEach(a=>{a()}),a}return r({type:g.INIT}),{dispatch:r,subscribe:q,getState:p,replaceReducer:function(a){if("function"!=typeof a)throw Error(d(10));f=a,r({type:g.REPLACE})},[e]:function(){return{subscribe(a){if("object"!=typeof a||null===a)throw Error(d(11));function b(){a.next&&a.next(p())}return b(),{unsubscribe:q(b)}},[e](){return this}}}}}function j(a){let b,c=Object.keys(a),e={};for(let b=0;b<c.length;b++){let d=c[b];"function"==typeof a[d]&&(e[d]=a[d])}let f=Object.keys(e);try{Object.keys(e).forEach(a=>{let b=e[a];if(void 0===b(void 0,{type:g.INIT}))throw Error(d(12));if(void 0===b(void 0,{type:g.PROBE_UNKNOWN_ACTION()}))throw Error(d(13))})}catch(a){b=a}return function(a={},c){if(b)throw b;let g=!1,h={};for(let b=0;b<f.length;b++){let i=f[b],j=e[i],k=a[i],l=j(k,c);if(void 0===l)throw c&&c.type,Error(d(14));h[i]=l,g=g||l!==k}return(g=g||f.length!==Object.keys(a).length)?h:a}}function k(...a){return 0===a.length?a=>a:1===a.length?a[0]:a.reduce((a,b)=>(...c)=>a(b(...c)))}function l(...a){return b=>(c,e)=>{let f=b(c,e),g=()=>{throw Error(d(15))},h={getState:f.getState,dispatch:(a,...b)=>g(a,...b)};return g=k(...a.map(a=>a(h)))(f.dispatch),{...f,dispatch:g}}}function m(a){return h(a)&&"type"in a&&"string"==typeof a.type}},49427:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"useMergedRef",{enumerable:!0,get:function(){return e}});let d=c(38301);function e(a,b){let c=(0,d.useRef)(null),e=(0,d.useRef)(null);return(0,d.useCallback)(d=>{if(null===d){let a=c.current;a&&(c.current=null,a());let b=e.current;b&&(e.current=null,b())}else a&&(c.current=f(a,d)),b&&(e.current=f(b,d))},[a,b])}function f(a,b){if("function"!=typeof a)return a.current=b,()=>{a.current=null};{let c=a(b);return"function"==typeof c?c:()=>a(null)}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},49449:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(94398),e=c(4516),f=c(38649),g=c(38352),h=c(73665);b.isEqualWith=function(a,b,c){return function a(b,c,i,j,k,l,m){let n=m(b,c,i,j,k,l);if(void 0!==n)return n;if(typeof b==typeof c)switch(typeof b){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return b===c;case"number":return b===c||Object.is(b,c)}return function b(c,i,j,k){if(Object.is(c,i))return!0;let l=f.getTag(c),m=f.getTag(i);if(l===g.argumentsTag&&(l=g.objectTag),m===g.argumentsTag&&(m=g.objectTag),l!==m)return!1;switch(l){case g.stringTag:return c.toString()===i.toString();case g.numberTag:{let a=c.valueOf(),b=i.valueOf();return h.eq(a,b)}case g.booleanTag:case g.dateTag:case g.symbolTag:return Object.is(c.valueOf(),i.valueOf());case g.regexpTag:return c.source===i.source&&c.flags===i.flags;case g.functionTag:return c===i}let n=(j=j??new Map).get(c),o=j.get(i);if(null!=n&&null!=o)return n===i;j.set(c,i),j.set(i,c);try{switch(l){case g.mapTag:if(c.size!==i.size)return!1;for(let[b,d]of c.entries())if(!i.has(b)||!a(d,i.get(b),b,c,i,j,k))return!1;return!0;case g.setTag:{if(c.size!==i.size)return!1;let b=Array.from(c.values()),d=Array.from(i.values());for(let e=0;e<b.length;e++){let f=b[e],g=d.findIndex(b=>a(f,b,void 0,c,i,j,k));if(-1===g)return!1;d.splice(g,1)}return!0}case g.arrayTag:case g.uint8ArrayTag:case g.uint8ClampedArrayTag:case g.uint16ArrayTag:case g.uint32ArrayTag:case g.bigUint64ArrayTag:case g.int8ArrayTag:case g.int16ArrayTag:case g.int32ArrayTag:case g.bigInt64ArrayTag:case g.float32ArrayTag:case g.float64ArrayTag:if("undefined"!=typeof Buffer&&Buffer.isBuffer(c)!==Buffer.isBuffer(i)||c.length!==i.length)return!1;for(let b=0;b<c.length;b++)if(!a(c[b],i[b],b,c,i,j,k))return!1;return!0;case g.arrayBufferTag:if(c.byteLength!==i.byteLength)return!1;return b(new Uint8Array(c),new Uint8Array(i),j,k);case g.dataViewTag:if(c.byteLength!==i.byteLength||c.byteOffset!==i.byteOffset)return!1;return b(new Uint8Array(c),new Uint8Array(i),j,k);case g.errorTag:return c.name===i.name&&c.message===i.message;case g.objectTag:{if(!(b(c.constructor,i.constructor,j,k)||d.isPlainObject(c)&&d.isPlainObject(i)))return!1;let f=[...Object.keys(c),...e.getSymbols(c)],g=[...Object.keys(i),...e.getSymbols(i)];if(f.length!==g.length)return!1;for(let b=0;b<f.length;b++){let d=f[b],e=c[d];if(!Object.hasOwn(i,d))return!1;let g=i[d];if(!a(e,g,d,c,i,j,k))return!1}return!0}default:return!1}}finally{j.delete(c),j.delete(i)}}(b,c,l,m)}(a,b,void 0,void 0,void 0,void 0,c)}},49560:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(38649);b.isArguments=function(a){return null!==a&&"object"==typeof a&&"[object Arguments]"===d.getTag(a)}},49656:(a,b)=>{"use strict";function c(a){var b;let{config:c,src:d,width:e,quality:f}=a,g=f||(null==(b=c.qualities)?void 0:b.reduce((a,b)=>Math.abs(b-75)<Math.abs(a-75)?b:a))||75;return c.path+"?url="+encodeURIComponent(d)+"&w="+e+"&q="+g+(d.startsWith("/_next/static/media/"),"")}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return d}}),c.__next_img_default=!0;let d=c},50088:(a,b,c)=>{"use strict";c.d(b,{M:()=>d});var d=a=>a.tooltip.settings.axisId},50193:(a,b,c)=>{a.exports=c(60735).throttle},50497:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isObject=function(a){return null!==a&&("object"==typeof a||"function"==typeof a)}},50625:(a,b,c)=>{"use strict";c.d(b,{E:()=>d});var d=(0,c(38301).createContext)(null)},51846:(a,b,c)=>{"use strict";c.d(b,{r:()=>f}),c(38301);var d=c(56998);c(46279);var e=c(53530);function f(a){var{fn:b,args:c}=a;return(0,d.j)(),(0,e.r)(),null}},51911:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(2095);b.cloneDeep=function(a){return d.cloneDeepWith(a)}},52063:(a,b,c)=>{"use strict";c.d(b,{q:()=>d});var d=(a,b,c,d)=>{var e;return"axis"===b?a.tooltipItemPayloads:0===a.tooltipItemPayloads.length?[]:null==(e="hover"===c?a.itemInteraction.hover.dataKey:a.itemInteraction.click.dataKey)&&null!=d?[a.tooltipItemPayloads[0]]:a.tooltipItemPayloads.filter(a=>{var b;return(null==(b=a.settings)?void 0:b.dataKey)===e})}},53053:(a,b,c)=>{"use strict";function d(a){return Number.isFinite(a)}function e(a){return"number"==typeof a&&a>0&&Number.isFinite(a)}c.d(b,{F:()=>e,H:()=>d})},53530:(a,b,c)=>{"use strict";c.d(b,{r:()=>f});var d=c(38301),e=(0,d.createContext)(null),f=()=>null!=(0,d.useContext)(e)},53968:(a,b,c)=>{"use strict";c.d(b,{U1:()=>n,VP:()=>i,Nc:()=>X,Z0:()=>r});var d=c(48369);function e(a){return({dispatch:b,getState:c})=>d=>e=>"function"==typeof e?e(b,c,a):d(e)}var f=e(),g=c(72001),h="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?d.Zz:d.Zz.apply(null,arguments)};function i(a,b){function c(...d){if(b){let c=b(...d);if(!c)throw Error(Y(0));return{type:a,payload:c.payload,..."meta"in c&&{meta:c.meta},..."error"in c&&{error:c.error}}}return{type:a,payload:d[0]}}return c.toString=()=>`${a}`,c.type=a,c.match=b=>(0,d.ve)(b)&&b.type===a,c}"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var j=class a extends Array{constructor(...b){super(...b),Object.setPrototypeOf(this,a.prototype)}static get[Symbol.species](){return a}concat(...a){return super.concat.apply(this,a)}prepend(...b){return 1===b.length&&Array.isArray(b[0])?new a(...b[0].concat(this)):new a(...b.concat(this))}};function k(a){return(0,g.a6)(a)?(0,g.jM)(a,()=>{}):a}function l(a,b,c){return a.has(b)?a.get(b):a.set(b,c(b)).get(b)}var m=a=>b=>{setTimeout(b,a)};function n(a){let b,c,g,i=function(a){let{thunk:b=!0,immutableCheck:c=!0,serializableCheck:d=!0,actionCreatorCheck:g=!0}=a??{},h=new j;return b&&("boolean"==typeof b?h.push(f):h.push(e(b.extraArgument))),h},{reducer:k,middleware:l,devTools:n=!0,duplicateMiddlewareCheck:o=!0,preloadedState:p,enhancers:q}=a||{};if("function"==typeof k)b=k;else if((0,d.Qd)(k))b=(0,d.HY)(k);else throw Error(Y(1));c="function"==typeof l?l(i):i();let r=d.Zz;n&&(r=h({trace:!1,..."object"==typeof n&&n}));let s=(g=(0,d.Tw)(...c),function(a){let{autoBatch:b=!0}=a??{},c=new j(g);return b&&c.push(((a={type:"raf"})=>b=>(...c)=>{let d=b(...c),e=!0,f=!1,g=!1,h=new Set,i="tick"===a.type?queueMicrotask:"raf"===a.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:m(10):"callback"===a.type?a.queueNotification:m(a.timeout),j=()=>{g=!1,f&&(f=!1,h.forEach(a=>a()))};return Object.assign({},d,{subscribe(a){let b=d.subscribe(()=>e&&a());return h.add(a),()=>{b(),h.delete(a)}},dispatch(a){try{return(f=!(e=!a?.meta?.RTK_autoBatch))&&!g&&(g=!0,i(j)),d.dispatch(a)}finally{e=!0}}})})("object"==typeof b?b:void 0)),c}),t=r(..."function"==typeof q?q(s):s());return(0,d.y$)(b,p,t)}function o(a){let b,c={},d=[],e={addCase(a,b){let d="string"==typeof a?a:a.type;if(!d)throw Error(Y(28));if(d in c)throw Error(Y(29));return c[d]=b,e},addMatcher:(a,b)=>(d.push({matcher:a,reducer:b}),e),addDefaultCase:a=>(b=a,e)};return a(e),[c,d,b]}var p=Symbol.for("rtk-slice-createasyncthunk"),q=(a=>(a.reducer="reducer",a.reducerWithPrepare="reducerWithPrepare",a.asyncThunk="asyncThunk",a))(q||{}),r=function({creators:a}={}){let b=a?.asyncThunk?.[p];return function(a){let c,{name:d,reducerPath:e=d}=a;if(!d)throw Error(Y(11));let f=("function"==typeof a.reducers?a.reducers(function(){function a(a,b){return{_reducerDefinitionType:"asyncThunk",payloadCreator:a,...b}}return a.withTypes=()=>a,{reducer:a=>Object.assign({[a.name]:(...b)=>a(...b)}[a.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(a,b)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:a,reducer:b}),asyncThunk:a}}()):a.reducers)||{},h=Object.keys(f),j={},m={},n={},p=[],q={addCase(a,b){let c="string"==typeof a?a:a.type;if(!c)throw Error(Y(12));if(c in m)throw Error(Y(13));return m[c]=b,q},addMatcher:(a,b)=>(p.push({matcher:a,reducer:b}),q),exposeAction:(a,b)=>(n[a]=b,q),exposeCaseReducer:(a,b)=>(j[a]=b,q)};function r(){let[b={},c=[],d]="function"==typeof a.extraReducers?o(a.extraReducers):[a.extraReducers],e={...b,...m};return function(a,b){let c,[d,e,f]=o(b);if("function"==typeof a)c=()=>k(a());else{let b=k(a);c=()=>b}function h(a=c(),b){let i=[d[b.type],...e.filter(({matcher:a})=>a(b)).map(({reducer:a})=>a)];return 0===i.filter(a=>!!a).length&&(i=[f]),i.reduce((a,c)=>{if(c)if((0,g.Qx)(a)){let d=c(a,b);return void 0===d?a:d}else{if((0,g.a6)(a))return(0,g.jM)(a,a=>c(a,b));let d=c(a,b);if(void 0===d){if(null===a)return a;throw Error("A case reducer on a non-draftable value must not return undefined")}return d}return a},a)}return h.getInitialState=c,h}(a.initialState,a=>{for(let b in e)a.addCase(b,e[b]);for(let b of p)a.addMatcher(b.matcher,b.reducer);for(let b of c)a.addMatcher(b.matcher,b.reducer);d&&a.addDefaultCase(d)})}h.forEach(c=>{let e=f[c],g={reducerName:c,type:`${d}/${c}`,createNotation:"function"==typeof a.reducers};"asyncThunk"===e._reducerDefinitionType?function({type:a,reducerName:b},c,d,e){if(!e)throw Error(Y(18));let{payloadCreator:f,fulfilled:g,pending:h,rejected:i,settled:j,options:k}=c,l=e(a,f,k);d.exposeAction(b,l),g&&d.addCase(l.fulfilled,g),h&&d.addCase(l.pending,h),i&&d.addCase(l.rejected,i),j&&d.addMatcher(l.settled,j),d.exposeCaseReducer(b,{fulfilled:g||s,pending:h||s,rejected:i||s,settled:j||s})}(g,e,q,b):function({type:a,reducerName:b,createNotation:c},d,e){let f,g;if("reducer"in d){if(c&&"reducerWithPrepare"!==d._reducerDefinitionType)throw Error(Y(17));f=d.reducer,g=d.prepare}else f=d;e.addCase(a,f).exposeCaseReducer(b,f).exposeAction(b,g?i(a,g):i(a))}(g,e,q)});let t=a=>a,u=new Map,v=new WeakMap;function w(a,b){return c||(c=r()),c(a,b)}function x(){return c||(c=r()),c.getInitialState()}function y(b,c=!1){function d(a){let e=a[b];return void 0===e&&c&&(e=l(v,d,x)),e}function e(b=t){let d=l(u,c,()=>new WeakMap);return l(d,b,()=>{let d={};for(let[e,f]of Object.entries(a.selectors??{}))d[e]=function(a,b,c,d){function e(f,...g){let h=b(f);return void 0===h&&d&&(h=c()),a(h,...g)}return e.unwrapped=a,e}(f,b,()=>l(v,b,x),c);return d})}return{reducerPath:b,getSelectors:e,get selectors(){return e(d)},selectSlice:d}}let z={name:d,reducer:w,actions:n,caseReducers:j,getInitialState:x,...y(e),injectInto(a,{reducerPath:b,...c}={}){let d=b??e;return a.inject({reducerPath:d,reducer:w},c),{...z,...y(d,!0)}}};return z}}();function s(){}var t="listener",u="completed",v="cancelled",w=`task-${v}`,x=`task-${u}`,y=`${t}-${v}`,z=`${t}-${u}`,A=class{constructor(a){this.code=a,this.message=`task ${v} (reason: ${a})`}name="TaskAbortError";message},B=(a,b)=>{if("function"!=typeof a)throw TypeError(Y(32))},C=()=>{},D=(a,b=C)=>(a.catch(b),a),E=(a,b)=>(a.addEventListener("abort",b,{once:!0}),()=>a.removeEventListener("abort",b)),F=(a,b)=>{let c=a.signal;c.aborted||("reason"in c||Object.defineProperty(c,"reason",{enumerable:!0,value:b,configurable:!0,writable:!0}),a.abort(b))},G=a=>{if(a.aborted){let{reason:b}=a;throw new A(b)}};function H(a,b){let c=C;return new Promise((d,e)=>{let f=()=>e(new A(a.reason));if(a.aborted)return void f();c=E(a,f),b.finally(()=>c()).then(d,e)}).finally(()=>{c=C})}var I=async(a,b)=>{try{await Promise.resolve();let b=await a();return{status:"ok",value:b}}catch(a){return{status:a instanceof A?"cancelled":"rejected",error:a}}finally{b?.()}},J=a=>b=>D(H(a,b).then(b=>(G(a),b))),K=a=>{let b=J(a);return a=>b(new Promise(b=>setTimeout(b,a)))},{assign:L}=Object,M={},N="listenerMiddleware",O=a=>{let{type:b,actionCreator:c,matcher:d,predicate:e,effect:f}=a;if(b)e=i(b).match;else if(c)b=c.type,e=c.match;else if(d)e=d;else if(e);else throw Error(Y(21));return B(f,"options.listener"),{predicate:e,type:b,effect:f}},P=L(a=>{let{type:b,predicate:c,effect:d}=O(a);return{id:((a=21)=>{let b="",c=a;for(;c--;)b+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return b})(),effect:d,type:b,predicate:c,pending:new Set,unsubscribe:()=>{throw Error(Y(22))}}},{withTypes:()=>P}),Q=(a,b)=>{let{type:c,effect:d,predicate:e}=O(b);return Array.from(a.values()).find(a=>("string"==typeof c?a.type===c:a.predicate===e)&&a.effect===d)},R=a=>{a.pending.forEach(a=>{F(a,y)})},S=(a,b,c)=>{try{a(b,c)}catch(a){setTimeout(()=>{throw a},0)}},T=L(i(`${N}/add`),{withTypes:()=>T}),U=i(`${N}/removeAll`),V=L(i(`${N}/remove`),{withTypes:()=>V}),W=(...a)=>{console.error(`${N}/error`,...a)},X=(a={})=>{let b=new Map,{extra:c,onError:e=W}=a;B(e,"onError");let f=a=>(a=>(a.unsubscribe=()=>b.delete(a.id),b.set(a.id,a),b=>{a.unsubscribe(),b?.cancelActive&&R(a)}))(Q(b,a)??P(a));L(f,{withTypes:()=>f});let g=a=>{let c=Q(b,a);return c&&(c.unsubscribe(),a.cancelActive&&R(c)),!!c};L(g,{withTypes:()=>g});let h=async(a,d,g,h)=>{let i=new AbortController,j=((a,b)=>{let c=async(c,d)=>{G(b);let e=()=>{},f=[new Promise((b,d)=>{let f=a({predicate:c,effect:(a,c)=>{c.unsubscribe(),b([a,c.getState(),c.getOriginalState()])}});e=()=>{f(),d()}})];null!=d&&f.push(new Promise(a=>setTimeout(a,d,null)));try{let a=await H(b,Promise.race(f));return G(b),a}finally{e()}};return(a,b)=>D(c(a,b))})(f,i.signal),k=[];try{a.pending.add(i),await Promise.resolve(a.effect(d,L({},g,{getOriginalState:h,condition:(a,b)=>j(a,b).then(Boolean),take:j,delay:K(i.signal),pause:J(i.signal),extra:c,signal:i.signal,fork:((a,b)=>(c,d)=>{B(c,"taskExecutor");let e=new AbortController;E(a,()=>F(e,a.reason));let f=I(async()=>{G(a),G(e.signal);let b=await c({pause:J(e.signal),delay:K(e.signal),signal:e.signal});return G(e.signal),b},()=>F(e,x));return d?.autoJoin&&b.push(f.catch(C)),{result:J(a)(f),cancel(){F(e,w)}}})(i.signal,k),unsubscribe:a.unsubscribe,subscribe:()=>{b.set(a.id,a)},cancelActiveListeners:()=>{a.pending.forEach((a,b,c)=>{a!==i&&(F(a,y),c.delete(a))})},cancel:()=>{F(i,y),a.pending.delete(i)},throwIfCancelled:()=>{G(i.signal)}})))}catch(a){a instanceof A||S(e,a,{raisedBy:"effect"})}finally{await Promise.all(k),F(i,z),a.pending.delete(i)}},i=(a=>()=>{a.forEach(R),a.clear()})(b);return{middleware:a=>c=>j=>{let k;if(!(0,d.ve)(j))return c(j);if(T.match(j))return f(j.payload);if(U.match(j))return void i();if(V.match(j))return g(j.payload);let l=a.getState(),m=()=>{if(l===M)throw Error(Y(23));return l};try{if(k=c(j),b.size>0){let c=a.getState();for(let d of Array.from(b.values())){let b=!1;try{b=d.predicate(j,c,l)}catch(a){b=!1,S(e,a,{raisedBy:"predicate"})}b&&h(d,j,a,m)}}}finally{l=M}return k},startListening:f,stopListening:g,clearListeners:i}};function Y(a){return`Minified Redux Toolkit error #${a}; visit https://redux-toolkit.js.org/Errors?code=${a} for the full message or use the non-minified dev environment for full errors. `}Symbol.for("rtk-state-proxy-original")},54758:(a,b,c)=>{"use strict";c.d(b,{dc:()=>h,ff:()=>g,g0:()=>i});var d=c(54985),e=c(45608),f=c.n(e),g=a=>a.legend.settings,h=a=>a.legend.size,i=(0,d.Mz)([a=>a.legend.payload,g],(a,b)=>{var{itemSorter:c}=b,d=a.flat(1);return c?f()(d,c):d})},54985:(a,b,c)=>{"use strict";c.d(b,{Mz:()=>u});var d=a=>Array.isArray(a)?a:[a],e=0,f=class{revision=e;_value;_lastValue;_isEqual=g;constructor(a,b=g){this._value=this._lastValue=a,this._isEqual=b}get value(){return this._value}set value(a){this.value!==a&&(this._value=a,this.revision=++e)}};function g(a,b){return a===b}function h(a){return a instanceof f||console.warn("Not a valid cell! ",a),a.value}var i=(a,b)=>!1;function j(){return function(a,b=g){return new f(null,b)}(0,i)}var k=a=>{let b=a.collectionTag;null===b&&(b=a.collectionTag=j()),h(b)};Symbol();var l=0,m=Object.getPrototypeOf({}),n=class{constructor(a){this.value=a,this.value=a,this.tag.value=a}proxy=new Proxy(this,o);tag=j();tags={};children={};collectionTag=null;id=l++},o={get:(a,b)=>(function(){let{value:c}=a,d=Reflect.get(c,b);if("symbol"==typeof b||b in m)return d;if("object"==typeof d&&null!==d){var e;let c=a.children[b];return void 0===c&&(c=a.children[b]=Array.isArray(e=d)?new p(e):new n(e)),c.tag&&h(c.tag),c.proxy}{let c=a.tags[b];return void 0===c&&((c=a.tags[b]=j()).value=d),h(c),d}})(),ownKeys:a=>(k(a),Reflect.ownKeys(a.value)),getOwnPropertyDescriptor:(a,b)=>Reflect.getOwnPropertyDescriptor(a.value,b),has:(a,b)=>Reflect.has(a.value,b)},p=class{constructor(a){this.value=a,this.value=a,this.tag.value=a}proxy=new Proxy([this],q);tag=j();tags={};children={};collectionTag=null;id=l++},q={get:([a],b)=>("length"===b&&k(a),o.get(a,b)),ownKeys:([a])=>o.ownKeys(a),getOwnPropertyDescriptor:([a],b)=>o.getOwnPropertyDescriptor(a,b),has:([a],b)=>o.has(a,b)},r="undefined"!=typeof WeakRef?WeakRef:class{constructor(a){this.value=a}deref(){return this.value}};function s(){return{s:0,v:void 0,o:null,p:null}}function t(a,b={}){let c,d=s(),{resultEqualityCheck:e}=b,f=0;function g(){let b,g=d,{length:h}=arguments;for(let a=0;a<h;a++){let b=arguments[a];if("function"==typeof b||"object"==typeof b&&null!==b){let a=g.o;null===a&&(g.o=a=new WeakMap);let c=a.get(b);void 0===c?(g=s(),a.set(b,g)):g=c}else{let a=g.p;null===a&&(g.p=a=new Map);let c=a.get(b);void 0===c?(g=s(),a.set(b,g)):g=c}}let i=g;if(1===g.s)b=g.v;else if(b=a.apply(null,arguments),f++,e){let a=c?.deref?.()??c;null!=a&&e(a,b)&&(b=a,0!==f&&f--),c="object"==typeof b&&null!==b||"function"==typeof b?new r(b):b}return i.s=1,i.v=b,b}return g.clearCache=()=>{d=s(),g.resetResultsCount()},g.resultsCount=()=>f,g.resetResultsCount=()=>{f=0},g}var u=function(a,...b){let c="function"==typeof a?{memoize:a,memoizeOptions:b}:a,e=(...a)=>{let b,e=0,f=0,g={},h=a.pop();"object"==typeof h&&(g=h,h=a.pop()),function(a,b=`expected a function, instead received ${typeof a}`){if("function"!=typeof a)throw TypeError(b)}(h,`createSelector expects an output function after the inputs, but received: [${typeof h}]`);let{memoize:i,memoizeOptions:j=[],argsMemoize:k=t,argsMemoizeOptions:l=[],devModeChecks:m={}}={...c,...g},n=d(j),o=d(l),p=function(a){let b=Array.isArray(a[0])?a[0]:a;return!function(a,b="expected all items to be functions, instead received the following types: "){if(!a.every(a=>"function"==typeof a)){let c=a.map(a=>"function"==typeof a?`function ${a.name||"unnamed"}()`:typeof a).join(", ");throw TypeError(`${b}[${c}]`)}}(b,"createSelector expects all input-selectors to be functions, but received the following types: "),b}(a),q=i(function(){return e++,h.apply(null,arguments)},...n);return Object.assign(k(function(){f++;let a=function(a,b){let c=[],{length:d}=a;for(let e=0;e<d;e++)c.push(a[e].apply(null,b));return c}(p,arguments);return b=q.apply(null,a)},...o),{resultFunc:h,memoizedResultFunc:q,dependencies:p,dependencyRecomputations:()=>f,resetDependencyRecomputations:()=>{f=0},lastResult:()=>b,recomputations:()=>e,resetRecomputations:()=>{e=0},memoize:i,argsMemoize:k})};return Object.assign(e,{withTypes:()=>e}),e}(t),v=Object.assign((a,b=u)=>{!function(a,b=`expected an object, instead received ${typeof a}`){if("object"!=typeof a)throw TypeError(b)}(a,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof a}`);let c=Object.keys(a);return b(c.map(b=>a[b]),(...a)=>a.reduce((a,b,d)=>(a[c[d]]=b,a),{}))},{withTypes:()=>v})},55111:(a,b,c)=>{"use strict";c.d(b,{CA:()=>p,MC:()=>j,QG:()=>o,Vi:()=>i,cU:()=>k,fR:()=>l});var d=c(53968),e=c(72001);function f(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function g(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?f(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):f(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var h=(0,d.Z0)({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(a,b){a.xAxis[b.payload.id]=(0,e.h4)(b.payload)},removeXAxis(a,b){delete a.xAxis[b.payload.id]},addYAxis(a,b){a.yAxis[b.payload.id]=(0,e.h4)(b.payload)},removeYAxis(a,b){delete a.yAxis[b.payload.id]},addZAxis(a,b){a.zAxis[b.payload.id]=(0,e.h4)(b.payload)},removeZAxis(a,b){delete a.zAxis[b.payload.id]},updateYAxisWidth(a,b){var{id:c,width:d}=b.payload;a.yAxis[c]&&(a.yAxis[c]=g(g({},a.yAxis[c]),{},{width:d}))}}}),{addXAxis:i,removeXAxis:j,addYAxis:k,removeYAxis:l,addZAxis:m,removeZAxis:n,updateYAxisWidth:o}=h.actions,p=h.reducer},55413:(a,b,c)=>{"use strict";c.d(b,{W:()=>i});var d=c(38301),e=c(43249),f=c(72677),g=["children","className"];function h(){return(h=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var i=d.forwardRef((a,b)=>{var{children:c,className:i}=a,j=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,g),k=(0,e.$)("recharts-layer",i);return d.createElement("g",h({className:k},(0,f.J9)(j,!0),{ref:b}),c)})},56998:(a,b,c)=>{"use strict";c.d(b,{G:()=>l,j:()=>h});var d=c(93350),e=c(38301),f=c(50625),g=a=>a,h=()=>{var a=(0,e.useContext)(f.E);return a?a.store.dispatch:g},i=()=>{},j=()=>i,k=(a,b)=>a===b;function l(a){var b=(0,e.useContext)(f.E);return(0,d.useSyncExternalStoreWithSelector)(b?b.subscription.addNestedSub:j,b?b.store.getState:i,b?b.store.getState:i,b?a:i,k)}},57682:(a,b,c)=>{"use strict";c.d(b,{L:()=>g});var d=c(38301);class e{setTimeout(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,c=performance.now(),d=null,e=f=>{f-c>=b?a(f):"function"==typeof requestAnimationFrame&&(d=requestAnimationFrame(e))};return d=requestAnimationFrame(e),()=>{cancelAnimationFrame(d)}}}var f=(0,d.createContext)(function(){var a,b,c,d,f;return a=new e,b=()=>null,c=!1,d=null,f=e=>{if(!c){if(Array.isArray(e)){if(!e.length)return;var[g,...h]=e;if("number"==typeof g){d=a.setTimeout(f.bind(null,h),g);return}f(g),d=a.setTimeout(f.bind(null,h));return}"string"==typeof e&&b(e),"object"==typeof e&&b(e),"function"==typeof e&&e()}},{stop:()=>{c=!0},start:a=>{c=!1,d&&(d(),d=null),f(a)},subscribe:a=>(b=a,()=>{b=()=>null}),getTimeoutController:()=>a}});function g(a,b){var c=(0,d.useContext)(f);return(0,d.useMemo)(()=>null!=b?b:c(a),[a,b,c])}},57684:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return f}});let d=c(38301),e=()=>{};function f(a){var b;let{headManager:c,reduceComponentsToState:f}=a;function g(){if(c&&c.mountedInstances){let b=d.Children.toArray(Array.from(c.mountedInstances).filter(Boolean));c.updateHead(f(b,a))}}return null==c||null==(b=c.mountedInstances)||b.add(a.children),g(),e(()=>{var b;return null==c||null==(b=c.mountedInstances)||b.add(a.children),()=>{var b;null==c||null==(b=c.mountedInstances)||b.delete(a.children)}}),e(()=>(c&&(c._pendingUpdate=g),()=>{c&&(c._pendingUpdate=g)})),null}},58306:(a,b,c)=>{"use strict";c.d(b,{EI:()=>l,oM:()=>k});var d=c(56998),e=c(76265),f=c(54985),g=c(92173),h=(0,f.Mz)([g.HZ],a=>{if(a)return{top:a.top,bottom:a.bottom,left:a.left,right:a.right}}),i=c(35268),j=(0,f.Mz)([h,i.Lp,i.A$],(a,b,c)=>{if(a&&null!=b&&null!=c)return{x:a.left,y:a.top,width:Math.max(0,b-a.left-a.right),height:Math.max(0,c-a.top-a.bottom)}}),k=()=>(0,d.G)(j),l=()=>(0,d.G)(e.JG)},58829:(a,b,c)=>{"use strict";c.d(b,{QP:()=>aa});let d=(a,b)=>{if(0===a.length)return b.classGroupId;let c=a[0],e=b.nextPart.get(c),f=e?d(a.slice(1),e):void 0;if(f)return f;if(0===b.validators.length)return;let g=a.join("-");return b.validators.find(({validator:a})=>a(g))?.classGroupId},e=/^\[(.+)\]$/,f=(a,b,c,d)=>{a.forEach(a=>{if("string"==typeof a){(""===a?b:g(b,a)).classGroupId=c;return}if("function"==typeof a)return h(a)?void f(a(d),b,c,d):void b.validators.push({validator:a,classGroupId:c});Object.entries(a).forEach(([a,e])=>{f(e,g(b,a),c,d)})})},g=(a,b)=>{let c=a;return b.split("-").forEach(a=>{c.nextPart.has(a)||c.nextPart.set(a,{nextPart:new Map,validators:[]}),c=c.nextPart.get(a)}),c},h=a=>a.isThemeGetter,i=/\s+/;function j(){let a,b,c=0,d="";for(;c<arguments.length;)(a=arguments[c++])&&(b=k(a))&&(d&&(d+=" "),d+=b);return d}let k=a=>{let b;if("string"==typeof a)return a;let c="";for(let d=0;d<a.length;d++)a[d]&&(b=k(a[d]))&&(c&&(c+=" "),c+=b);return c},l=a=>{let b=b=>b[a]||[];return b.isThemeGetter=!0,b},m=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,n=/^\((?:(\w[\w-]*):)?(.+)\)$/i,o=/^\d+\/\d+$/,p=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,q=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,r=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,s=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,t=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,u=a=>o.test(a),v=a=>!!a&&!Number.isNaN(Number(a)),w=a=>!!a&&Number.isInteger(Number(a)),x=a=>a.endsWith("%")&&v(a.slice(0,-1)),y=a=>p.test(a),z=()=>!0,A=a=>q.test(a)&&!r.test(a),B=()=>!1,C=a=>s.test(a),D=a=>t.test(a),E=a=>!G(a)&&!M(a),F=a=>T(a,X,B),G=a=>m.test(a),H=a=>T(a,Y,A),I=a=>T(a,Z,v),J=a=>T(a,V,B),K=a=>T(a,W,D),L=a=>T(a,_,C),M=a=>n.test(a),N=a=>U(a,Y),O=a=>U(a,$),P=a=>U(a,V),Q=a=>U(a,X),R=a=>U(a,W),S=a=>U(a,_,!0),T=(a,b,c)=>{let d=m.exec(a);return!!d&&(d[1]?b(d[1]):c(d[2]))},U=(a,b,c=!1)=>{let d=n.exec(a);return!!d&&(d[1]?b(d[1]):c)},V=a=>"position"===a||"percentage"===a,W=a=>"image"===a||"url"===a,X=a=>"length"===a||"size"===a||"bg-size"===a,Y=a=>"length"===a,Z=a=>"number"===a,$=a=>"family-name"===a,_=a=>"shadow"===a;Symbol.toStringTag;let aa=function(a,...b){let c,g,h,k=function(i){let j;return g=(c={cache:(a=>{if(a<1)return{get:()=>void 0,set:()=>{}};let b=0,c=new Map,d=new Map,e=(e,f)=>{c.set(e,f),++b>a&&(b=0,d=c,c=new Map)};return{get(a){let b=c.get(a);return void 0!==b?b:void 0!==(b=d.get(a))?(e(a,b),b):void 0},set(a,b){c.has(a)?c.set(a,b):e(a,b)}}})((j=b.reduce((a,b)=>b(a),a())).cacheSize),parseClassName:(a=>{let{prefix:b,experimentalParseClassName:c}=a,d=a=>{let b,c,d=[],e=0,f=0,g=0;for(let c=0;c<a.length;c++){let h=a[c];if(0===e&&0===f){if(":"===h){d.push(a.slice(g,c)),g=c+1;continue}if("/"===h){b=c;continue}}"["===h?e++:"]"===h?e--:"("===h?f++:")"===h&&f--}let h=0===d.length?a:a.substring(g),i=(c=h).endsWith("!")?c.substring(0,c.length-1):c.startsWith("!")?c.substring(1):c;return{modifiers:d,hasImportantModifier:i!==h,baseClassName:i,maybePostfixModifierPosition:b&&b>g?b-g:void 0}};if(b){let a=b+":",c=d;d=b=>b.startsWith(a)?c(b.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:b,maybePostfixModifierPosition:void 0}}if(c){let a=d;d=b=>c({className:b,parseClassName:a})}return d})(j),sortModifiers:(a=>{let b=Object.fromEntries(a.orderSensitiveModifiers.map(a=>[a,!0]));return a=>{if(a.length<=1)return a;let c=[],d=[];return a.forEach(a=>{"["===a[0]||b[a]?(c.push(...d.sort(),a),d=[]):d.push(a)}),c.push(...d.sort()),c}})(j),...(a=>{let b=(a=>{let{theme:b,classGroups:c}=a,d={nextPart:new Map,validators:[]};for(let a in c)f(c[a],d,a,b);return d})(a),{conflictingClassGroups:c,conflictingClassGroupModifiers:g}=a;return{getClassGroupId:a=>{let c=a.split("-");return""===c[0]&&1!==c.length&&c.shift(),d(c,b)||(a=>{if(e.test(a)){let b=e.exec(a)[1],c=b?.substring(0,b.indexOf(":"));if(c)return"arbitrary.."+c}})(a)},getConflictingClassGroupIds:(a,b)=>{let d=c[a]||[];return b&&g[a]?[...d,...g[a]]:d}}})(j)}).cache.get,h=c.cache.set,k=l,l(i)};function l(a){let b=g(a);if(b)return b;let d=((a,b)=>{let{parseClassName:c,getClassGroupId:d,getConflictingClassGroupIds:e,sortModifiers:f}=b,g=[],h=a.trim().split(i),j="";for(let a=h.length-1;a>=0;a-=1){let b=h[a],{isExternal:i,modifiers:k,hasImportantModifier:l,baseClassName:m,maybePostfixModifierPosition:n}=c(b);if(i){j=b+(j.length>0?" "+j:j);continue}let o=!!n,p=d(o?m.substring(0,n):m);if(!p){if(!o||!(p=d(m))){j=b+(j.length>0?" "+j:j);continue}o=!1}let q=f(k).join(":"),r=l?q+"!":q,s=r+p;if(g.includes(s))continue;g.push(s);let t=e(p,o);for(let a=0;a<t.length;++a){let b=t[a];g.push(r+b)}j=b+(j.length>0?" "+j:j)}return j})(a,c);return h(a,d),d}return function(){return k(j.apply(null,arguments))}}(()=>{let a=l("color"),b=l("font"),c=l("text"),d=l("font-weight"),e=l("tracking"),f=l("leading"),g=l("breakpoint"),h=l("container"),i=l("spacing"),j=l("radius"),k=l("shadow"),m=l("inset-shadow"),n=l("text-shadow"),o=l("drop-shadow"),p=l("blur"),q=l("perspective"),r=l("aspect"),s=l("ease"),t=l("animate"),A=()=>["auto","avoid","all","avoid-page","page","left","right","column"],B=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],C=()=>[...B(),M,G],D=()=>["auto","hidden","clip","visible","scroll"],T=()=>["auto","contain","none"],U=()=>[M,G,i],V=()=>[u,"full","auto",...U()],W=()=>[w,"none","subgrid",M,G],X=()=>["auto",{span:["full",w,M,G]},w,M,G],Y=()=>[w,"auto",M,G],Z=()=>["auto","min","max","fr",M,G],$=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],_=()=>["start","end","center","stretch","center-safe","end-safe"],aa=()=>["auto",...U()],ab=()=>[u,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...U()],ac=()=>[a,M,G],ad=()=>[...B(),P,J,{position:[M,G]}],ae=()=>["no-repeat",{repeat:["","x","y","space","round"]}],af=()=>["auto","cover","contain",Q,F,{size:[M,G]}],ag=()=>[x,N,H],ah=()=>["","none","full",j,M,G],ai=()=>["",v,N,H],aj=()=>["solid","dashed","dotted","double"],ak=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],al=()=>[v,x,P,J],am=()=>["","none",p,M,G],an=()=>["none",v,M,G],ao=()=>["none",v,M,G],ap=()=>[v,M,G],aq=()=>[u,"full",...U()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[y],breakpoint:[y],color:[z],container:[y],"drop-shadow":[y],ease:["in","out","in-out"],font:[E],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[y],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[y],shadow:[y],spacing:["px",v],text:[y],"text-shadow":[y],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",u,G,M,r]}],container:["container"],columns:[{columns:[v,G,M,h]}],"break-after":[{"break-after":A()}],"break-before":[{"break-before":A()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:C()}],overflow:[{overflow:D()}],"overflow-x":[{"overflow-x":D()}],"overflow-y":[{"overflow-y":D()}],overscroll:[{overscroll:T()}],"overscroll-x":[{"overscroll-x":T()}],"overscroll-y":[{"overscroll-y":T()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:V()}],"inset-x":[{"inset-x":V()}],"inset-y":[{"inset-y":V()}],start:[{start:V()}],end:[{end:V()}],top:[{top:V()}],right:[{right:V()}],bottom:[{bottom:V()}],left:[{left:V()}],visibility:["visible","invisible","collapse"],z:[{z:[w,"auto",M,G]}],basis:[{basis:[u,"full","auto",h,...U()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[v,u,"auto","initial","none",G]}],grow:[{grow:["",v,M,G]}],shrink:[{shrink:["",v,M,G]}],order:[{order:[w,"first","last","none",M,G]}],"grid-cols":[{"grid-cols":W()}],"col-start-end":[{col:X()}],"col-start":[{"col-start":Y()}],"col-end":[{"col-end":Y()}],"grid-rows":[{"grid-rows":W()}],"row-start-end":[{row:X()}],"row-start":[{"row-start":Y()}],"row-end":[{"row-end":Y()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Z()}],"auto-rows":[{"auto-rows":Z()}],gap:[{gap:U()}],"gap-x":[{"gap-x":U()}],"gap-y":[{"gap-y":U()}],"justify-content":[{justify:[...$(),"normal"]}],"justify-items":[{"justify-items":[..._(),"normal"]}],"justify-self":[{"justify-self":["auto",..._()]}],"align-content":[{content:["normal",...$()]}],"align-items":[{items:[..._(),{baseline:["","last"]}]}],"align-self":[{self:["auto",..._(),{baseline:["","last"]}]}],"place-content":[{"place-content":$()}],"place-items":[{"place-items":[..._(),"baseline"]}],"place-self":[{"place-self":["auto",..._()]}],p:[{p:U()}],px:[{px:U()}],py:[{py:U()}],ps:[{ps:U()}],pe:[{pe:U()}],pt:[{pt:U()}],pr:[{pr:U()}],pb:[{pb:U()}],pl:[{pl:U()}],m:[{m:aa()}],mx:[{mx:aa()}],my:[{my:aa()}],ms:[{ms:aa()}],me:[{me:aa()}],mt:[{mt:aa()}],mr:[{mr:aa()}],mb:[{mb:aa()}],ml:[{ml:aa()}],"space-x":[{"space-x":U()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":U()}],"space-y-reverse":["space-y-reverse"],size:[{size:ab()}],w:[{w:[h,"screen",...ab()]}],"min-w":[{"min-w":[h,"screen","none",...ab()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[g]},...ab()]}],h:[{h:["screen","lh",...ab()]}],"min-h":[{"min-h":["screen","lh","none",...ab()]}],"max-h":[{"max-h":["screen","lh",...ab()]}],"font-size":[{text:["base",c,N,H]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[d,M,I]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",x,G]}],"font-family":[{font:[O,G,b]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[e,M,G]}],"line-clamp":[{"line-clamp":[v,"none",M,I]}],leading:[{leading:[f,...U()]}],"list-image":[{"list-image":["none",M,G]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",M,G]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:ac()}],"text-color":[{text:ac()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...aj(),"wavy"]}],"text-decoration-thickness":[{decoration:[v,"from-font","auto",M,H]}],"text-decoration-color":[{decoration:ac()}],"underline-offset":[{"underline-offset":[v,"auto",M,G]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:U()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",M,G]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",M,G]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ad()}],"bg-repeat":[{bg:ae()}],"bg-size":[{bg:af()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},w,M,G],radial:["",M,G],conic:[w,M,G]},R,K]}],"bg-color":[{bg:ac()}],"gradient-from-pos":[{from:ag()}],"gradient-via-pos":[{via:ag()}],"gradient-to-pos":[{to:ag()}],"gradient-from":[{from:ac()}],"gradient-via":[{via:ac()}],"gradient-to":[{to:ac()}],rounded:[{rounded:ah()}],"rounded-s":[{"rounded-s":ah()}],"rounded-e":[{"rounded-e":ah()}],"rounded-t":[{"rounded-t":ah()}],"rounded-r":[{"rounded-r":ah()}],"rounded-b":[{"rounded-b":ah()}],"rounded-l":[{"rounded-l":ah()}],"rounded-ss":[{"rounded-ss":ah()}],"rounded-se":[{"rounded-se":ah()}],"rounded-ee":[{"rounded-ee":ah()}],"rounded-es":[{"rounded-es":ah()}],"rounded-tl":[{"rounded-tl":ah()}],"rounded-tr":[{"rounded-tr":ah()}],"rounded-br":[{"rounded-br":ah()}],"rounded-bl":[{"rounded-bl":ah()}],"border-w":[{border:ai()}],"border-w-x":[{"border-x":ai()}],"border-w-y":[{"border-y":ai()}],"border-w-s":[{"border-s":ai()}],"border-w-e":[{"border-e":ai()}],"border-w-t":[{"border-t":ai()}],"border-w-r":[{"border-r":ai()}],"border-w-b":[{"border-b":ai()}],"border-w-l":[{"border-l":ai()}],"divide-x":[{"divide-x":ai()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ai()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...aj(),"hidden","none"]}],"divide-style":[{divide:[...aj(),"hidden","none"]}],"border-color":[{border:ac()}],"border-color-x":[{"border-x":ac()}],"border-color-y":[{"border-y":ac()}],"border-color-s":[{"border-s":ac()}],"border-color-e":[{"border-e":ac()}],"border-color-t":[{"border-t":ac()}],"border-color-r":[{"border-r":ac()}],"border-color-b":[{"border-b":ac()}],"border-color-l":[{"border-l":ac()}],"divide-color":[{divide:ac()}],"outline-style":[{outline:[...aj(),"none","hidden"]}],"outline-offset":[{"outline-offset":[v,M,G]}],"outline-w":[{outline:["",v,N,H]}],"outline-color":[{outline:ac()}],shadow:[{shadow:["","none",k,S,L]}],"shadow-color":[{shadow:ac()}],"inset-shadow":[{"inset-shadow":["none",m,S,L]}],"inset-shadow-color":[{"inset-shadow":ac()}],"ring-w":[{ring:ai()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:ac()}],"ring-offset-w":[{"ring-offset":[v,H]}],"ring-offset-color":[{"ring-offset":ac()}],"inset-ring-w":[{"inset-ring":ai()}],"inset-ring-color":[{"inset-ring":ac()}],"text-shadow":[{"text-shadow":["none",n,S,L]}],"text-shadow-color":[{"text-shadow":ac()}],opacity:[{opacity:[v,M,G]}],"mix-blend":[{"mix-blend":[...ak(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ak()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[v]}],"mask-image-linear-from-pos":[{"mask-linear-from":al()}],"mask-image-linear-to-pos":[{"mask-linear-to":al()}],"mask-image-linear-from-color":[{"mask-linear-from":ac()}],"mask-image-linear-to-color":[{"mask-linear-to":ac()}],"mask-image-t-from-pos":[{"mask-t-from":al()}],"mask-image-t-to-pos":[{"mask-t-to":al()}],"mask-image-t-from-color":[{"mask-t-from":ac()}],"mask-image-t-to-color":[{"mask-t-to":ac()}],"mask-image-r-from-pos":[{"mask-r-from":al()}],"mask-image-r-to-pos":[{"mask-r-to":al()}],"mask-image-r-from-color":[{"mask-r-from":ac()}],"mask-image-r-to-color":[{"mask-r-to":ac()}],"mask-image-b-from-pos":[{"mask-b-from":al()}],"mask-image-b-to-pos":[{"mask-b-to":al()}],"mask-image-b-from-color":[{"mask-b-from":ac()}],"mask-image-b-to-color":[{"mask-b-to":ac()}],"mask-image-l-from-pos":[{"mask-l-from":al()}],"mask-image-l-to-pos":[{"mask-l-to":al()}],"mask-image-l-from-color":[{"mask-l-from":ac()}],"mask-image-l-to-color":[{"mask-l-to":ac()}],"mask-image-x-from-pos":[{"mask-x-from":al()}],"mask-image-x-to-pos":[{"mask-x-to":al()}],"mask-image-x-from-color":[{"mask-x-from":ac()}],"mask-image-x-to-color":[{"mask-x-to":ac()}],"mask-image-y-from-pos":[{"mask-y-from":al()}],"mask-image-y-to-pos":[{"mask-y-to":al()}],"mask-image-y-from-color":[{"mask-y-from":ac()}],"mask-image-y-to-color":[{"mask-y-to":ac()}],"mask-image-radial":[{"mask-radial":[M,G]}],"mask-image-radial-from-pos":[{"mask-radial-from":al()}],"mask-image-radial-to-pos":[{"mask-radial-to":al()}],"mask-image-radial-from-color":[{"mask-radial-from":ac()}],"mask-image-radial-to-color":[{"mask-radial-to":ac()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":B()}],"mask-image-conic-pos":[{"mask-conic":[v]}],"mask-image-conic-from-pos":[{"mask-conic-from":al()}],"mask-image-conic-to-pos":[{"mask-conic-to":al()}],"mask-image-conic-from-color":[{"mask-conic-from":ac()}],"mask-image-conic-to-color":[{"mask-conic-to":ac()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ad()}],"mask-repeat":[{mask:ae()}],"mask-size":[{mask:af()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",M,G]}],filter:[{filter:["","none",M,G]}],blur:[{blur:am()}],brightness:[{brightness:[v,M,G]}],contrast:[{contrast:[v,M,G]}],"drop-shadow":[{"drop-shadow":["","none",o,S,L]}],"drop-shadow-color":[{"drop-shadow":ac()}],grayscale:[{grayscale:["",v,M,G]}],"hue-rotate":[{"hue-rotate":[v,M,G]}],invert:[{invert:["",v,M,G]}],saturate:[{saturate:[v,M,G]}],sepia:[{sepia:["",v,M,G]}],"backdrop-filter":[{"backdrop-filter":["","none",M,G]}],"backdrop-blur":[{"backdrop-blur":am()}],"backdrop-brightness":[{"backdrop-brightness":[v,M,G]}],"backdrop-contrast":[{"backdrop-contrast":[v,M,G]}],"backdrop-grayscale":[{"backdrop-grayscale":["",v,M,G]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[v,M,G]}],"backdrop-invert":[{"backdrop-invert":["",v,M,G]}],"backdrop-opacity":[{"backdrop-opacity":[v,M,G]}],"backdrop-saturate":[{"backdrop-saturate":[v,M,G]}],"backdrop-sepia":[{"backdrop-sepia":["",v,M,G]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":U()}],"border-spacing-x":[{"border-spacing-x":U()}],"border-spacing-y":[{"border-spacing-y":U()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",M,G]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[v,"initial",M,G]}],ease:[{ease:["linear","initial",s,M,G]}],delay:[{delay:[v,M,G]}],animate:[{animate:["none",t,M,G]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[q,M,G]}],"perspective-origin":[{"perspective-origin":C()}],rotate:[{rotate:an()}],"rotate-x":[{"rotate-x":an()}],"rotate-y":[{"rotate-y":an()}],"rotate-z":[{"rotate-z":an()}],scale:[{scale:ao()}],"scale-x":[{"scale-x":ao()}],"scale-y":[{"scale-y":ao()}],"scale-z":[{"scale-z":ao()}],"scale-3d":["scale-3d"],skew:[{skew:ap()}],"skew-x":[{"skew-x":ap()}],"skew-y":[{"skew-y":ap()}],transform:[{transform:[M,G,"","none","gpu","cpu"]}],"transform-origin":[{origin:C()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:aq()}],"translate-x":[{"translate-x":aq()}],"translate-y":[{"translate-y":aq()}],"translate-z":[{"translate-z":aq()}],"translate-none":["translate-none"],accent:[{accent:ac()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:ac()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",M,G]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":U()}],"scroll-mx":[{"scroll-mx":U()}],"scroll-my":[{"scroll-my":U()}],"scroll-ms":[{"scroll-ms":U()}],"scroll-me":[{"scroll-me":U()}],"scroll-mt":[{"scroll-mt":U()}],"scroll-mr":[{"scroll-mr":U()}],"scroll-mb":[{"scroll-mb":U()}],"scroll-ml":[{"scroll-ml":U()}],"scroll-p":[{"scroll-p":U()}],"scroll-px":[{"scroll-px":U()}],"scroll-py":[{"scroll-py":U()}],"scroll-ps":[{"scroll-ps":U()}],"scroll-pe":[{"scroll-pe":U()}],"scroll-pt":[{"scroll-pt":U()}],"scroll-pr":[{"scroll-pr":U()}],"scroll-pb":[{"scroll-pb":U()}],"scroll-pl":[{"scroll-pl":U()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",M,G]}],fill:[{fill:["none",...ac()]}],"stroke-w":[{stroke:[v,N,H,I]}],stroke:[{stroke:["none",...ac()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},60335:(a,b,c)=>{"use strict";function d(a,b,c){return Array.isArray(a)&&a&&b+c!==0?a.slice(b,c+1):a}c.d(b,{v:()=>d})},60343:(a,b,c)=>{"use strict";c.d(b,{kz:()=>eI,fb:()=>ez,q:()=>eZ,tP:()=>e6,g1:()=>fd,iv:()=>fD,Nk:()=>ex,pM:()=>eG,Oz:()=>eX,tF:()=>fB,rj:()=>ev,ec:()=>eq,bb:()=>e_,xp:()=>fb,wL:()=>e3,sr:()=>e8,Qn:()=>fa,MK:()=>eE,IO:()=>et,P9:()=>eR,S5:()=>eO,PU:()=>ee,cd:()=>eg,eo:()=>en,yi:()=>eP,CH:()=>eJ,ZB:()=>fF,D5:()=>fl,iV:()=>fn,Hd:()=>el,Gx:()=>fI,DP:()=>ek,BQ:()=>fA,_y:()=>fK,AV:()=>e2,um:()=>em,xM:()=>e9,gT:()=>eT,Kr:()=>eQ,$X:()=>eV,TC:()=>eF,Zi:()=>fG,CR:()=>fH,ld:()=>eo,L$:()=>fx,Rl:()=>ef,Lw:()=>fu,KR:()=>fy,sf:()=>eh,wP:()=>fz});var d={};c.r(d),c.d(d,{scaleBand:()=>o,scaleDiverging:()=>function a(){var b=aM(c_()(au));return b.copy=function(){return cY(b,a())},i.apply(b,arguments)},scaleDivergingLog:()=>function a(){var b=aU(c_()).domain([.1,1,10]);return b.copy=function(){return cY(b,a()).base(b.base())},i.apply(b,arguments)},scaleDivergingPow:()=>c0,scaleDivergingSqrt:()=>c1,scaleDivergingSymlog:()=>function a(){var b=aX(c_());return b.copy=function(){return cY(b,a()).constant(b.constant())},i.apply(b,arguments)},scaleIdentity:()=>function a(b){var c;function d(a){return null==a||isNaN(a*=1)?c:a}return d.invert=d,d.domain=d.range=function(a){return arguments.length?(b=Array.from(a,as),d):b.slice()},d.unknown=function(a){return arguments.length?(c=a,d):c},d.copy=function(){return a(b).unknown(c)},b=arguments.length?Array.from(b,as):[0,1],aM(d)},scaleImplicit:()=>m,scaleLinear:()=>function a(){var b=aA();return b.copy=function(){return ay(b,a())},h.apply(b,arguments),aM(b)},scaleLog:()=>function a(){let b=aU(az()).domain([1,10]);return b.copy=()=>ay(b,a()).base(b.base()),h.apply(b,arguments),b},scaleOrdinal:()=>n,scalePoint:()=>p,scalePow:()=>a0,scaleQuantile:()=>function a(){var b,c=[],d=[],e=[];function f(){var a=0,b=Math.max(1,d.length);for(e=Array(b-1);++a<b;)e[a-1]=function(a,b,c=B){if(!(!(d=a.length)||isNaN(b*=1))){if(b<=0||d<2)return+c(a[0],0,a);if(b>=1)return+c(a[d-1],d-1,a);var d,e=(d-1)*b,f=Math.floor(e),g=+c(a[f],f,a);return g+(c(a[f+1],f+1,a)-g)*(e-f)}}(c,a/b);return g}function g(a){return null==a||isNaN(a*=1)?b:d[D(e,a)]}return g.invertExtent=function(a){var b=d.indexOf(a);return b<0?[NaN,NaN]:[b>0?e[b-1]:c[0],b<e.length?e[b]:c[c.length-1]]},g.domain=function(a){if(!arguments.length)return c.slice();for(let b of(c=[],a))null==b||isNaN(b*=1)||c.push(b);return c.sort(x),f()},g.range=function(a){return arguments.length?(d=Array.from(a),f()):d.slice()},g.unknown=function(a){return arguments.length?(b=a,g):b},g.quantiles=function(){return e.slice()},g.copy=function(){return a().domain(c).range(d).unknown(b)},h.apply(g,arguments)},scaleQuantize:()=>function a(){var b,c=0,d=1,e=1,f=[.5],g=[0,1];function i(a){return null!=a&&a<=a?g[D(f,a,0,e)]:b}function j(){var a=-1;for(f=Array(e);++a<e;)f[a]=((a+1)*d-(a-e)*c)/(e+1);return i}return i.domain=function(a){return arguments.length?([c,d]=a,c*=1,d*=1,j()):[c,d]},i.range=function(a){return arguments.length?(e=(g=Array.from(a)).length-1,j()):g.slice()},i.invertExtent=function(a){var b=g.indexOf(a);return b<0?[NaN,NaN]:b<1?[c,f[0]]:b>=e?[f[e-1],d]:[f[b-1],f[b]]},i.unknown=function(a){return arguments.length&&(b=a),i},i.thresholds=function(){return f.slice()},i.copy=function(){return a().domain([c,d]).range(g).unknown(b)},h.apply(aM(i),arguments)},scaleRadial:()=>function a(){var b,c=aA(),d=[0,1],e=!1;function f(a){var d,f=Math.sign(d=c(a))*Math.sqrt(Math.abs(d));return isNaN(f)?b:e?Math.round(f):f}return f.invert=function(a){return c.invert(a2(a))},f.domain=function(a){return arguments.length?(c.domain(a),f):c.domain()},f.range=function(a){return arguments.length?(c.range((d=Array.from(a,as)).map(a2)),f):d.slice()},f.rangeRound=function(a){return f.range(a).round(!0)},f.round=function(a){return arguments.length?(e=!!a,f):e},f.clamp=function(a){return arguments.length?(c.clamp(a),f):c.clamp()},f.unknown=function(a){return arguments.length?(b=a,f):b},f.copy=function(){return a(c.domain(),d).round(e).clamp(c.clamp()).unknown(b)},h.apply(f,arguments),aM(f)},scaleSequential:()=>function a(){var b=aM(cX()(au));return b.copy=function(){return cY(b,a())},i.apply(b,arguments)},scaleSequentialLog:()=>function a(){var b=aU(cX()).domain([1,10]);return b.copy=function(){return cY(b,a()).base(b.base())},i.apply(b,arguments)},scaleSequentialPow:()=>cZ,scaleSequentialQuantile:()=>function a(){var b=[],c=au;function d(a){if(null!=a&&!isNaN(a*=1))return c((D(b,a,1)-1)/(b.length-1))}return d.domain=function(a){if(!arguments.length)return b.slice();for(let c of(b=[],a))null==c||isNaN(c*=1)||b.push(c);return b.sort(x),d},d.interpolator=function(a){return arguments.length?(c=a,d):c},d.range=function(){return b.map((a,d)=>c(d/(b.length-1)))},d.quantiles=function(a){return Array.from({length:a+1},(c,d)=>(function(a,b,c){if(!(!(d=(a=Float64Array.from(function*(a,b){if(void 0===b)for(let b of a)null!=b&&(b*=1)>=b&&(yield b);else{let c=-1;for(let d of a)null!=(d=b(d,++c,a))&&(d*=1)>=d&&(yield d)}}(a,void 0))).length)||isNaN(b*=1))){if(b<=0||d<2)return a4(a);if(b>=1)return a3(a);var d,e=(d-1)*b,f=Math.floor(e),g=a3((function a(b,c,d=0,e=1/0,f){if(c=Math.floor(c),d=Math.floor(Math.max(0,d)),e=Math.floor(Math.min(b.length-1,e)),!(d<=c&&c<=e))return b;for(f=void 0===f?a5:function(a=x){if(a===x)return a5;if("function"!=typeof a)throw TypeError("compare is not a function");return(b,c)=>{let d=a(b,c);return d||0===d?d:(0===a(c,c))-(0===a(b,b))}}(f);e>d;){if(e-d>600){let g=e-d+1,h=c-d+1,i=Math.log(g),j=.5*Math.exp(2*i/3),k=.5*Math.sqrt(i*j*(g-j)/g)*(h-g/2<0?-1:1),l=Math.max(d,Math.floor(c-h*j/g+k)),m=Math.min(e,Math.floor(c+(g-h)*j/g+k));a(b,c,l,m,f)}let g=b[c],h=d,i=e;for(a6(b,d,c),f(b[e],g)>0&&a6(b,d,e);h<i;){for(a6(b,h,i),++h,--i;0>f(b[h],g);)++h;for(;f(b[i],g)>0;)--i}0===f(b[d],g)?a6(b,d,i):a6(b,++i,e),i<=c&&(d=i+1),c<=i&&(e=i-1)}return b})(a,f).subarray(0,f+1));return g+(a4(a.subarray(f+1))-g)*(e-f)}})(b,d/a))},d.copy=function(){return a(c).domain(b)},i.apply(d,arguments)},scaleSequentialSqrt:()=>c$,scaleSequentialSymlog:()=>function a(){var b=aX(cX());return b.copy=function(){return cY(b,a()).constant(b.constant())},i.apply(b,arguments)},scaleSqrt:()=>a1,scaleSymlog:()=>function a(){var b=aX(az());return b.copy=function(){return ay(b,a()).constant(b.constant())},h.apply(b,arguments)},scaleThreshold:()=>function a(){var b,c=[.5],d=[0,1],e=1;function f(a){return null!=a&&a<=a?d[D(c,a,0,e)]:b}return f.domain=function(a){return arguments.length?(e=Math.min((c=Array.from(a)).length,d.length-1),f):c.slice()},f.range=function(a){return arguments.length?(d=Array.from(a),e=Math.min(c.length,d.length-1),f):d.slice()},f.invertExtent=function(a){var b=d.indexOf(a);return[c[b-1],c[b]]},f.unknown=function(a){return arguments.length?(b=a,f):b},f.copy=function(){return a().domain(c).range(d).unknown(b)},h.apply(f,arguments)},scaleTime:()=>cV,scaleUtc:()=>cW,tickFormat:()=>aL});var e=c(54985),f=c(9356),g=c.n(f);function h(a,b){switch(arguments.length){case 0:break;case 1:this.range(a);break;default:this.range(b).domain(a)}return this}function i(a,b){switch(arguments.length){case 0:break;case 1:"function"==typeof a?this.interpolator(a):this.range(a);break;default:this.domain(a),"function"==typeof b?this.interpolator(b):this.range(b)}return this}class j extends Map{constructor(a,b=l){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:b}}),null!=a)for(let[b,c]of a)this.set(b,c)}get(a){return super.get(k(this,a))}has(a){return super.has(k(this,a))}set(a,b){return super.set(function({_intern:a,_key:b},c){let d=b(c);return a.has(d)?a.get(d):(a.set(d,c),c)}(this,a),b)}delete(a){return super.delete(function({_intern:a,_key:b},c){let d=b(c);return a.has(d)&&(c=a.get(d),a.delete(d)),c}(this,a))}}function k({_intern:a,_key:b},c){let d=b(c);return a.has(d)?a.get(d):c}function l(a){return null!==a&&"object"==typeof a?a.valueOf():a}let m=Symbol("implicit");function n(){var a=new j,b=[],c=[],d=m;function e(e){let f=a.get(e);if(void 0===f){if(d!==m)return d;a.set(e,f=b.push(e)-1)}return c[f%c.length]}return e.domain=function(c){if(!arguments.length)return b.slice();for(let d of(b=[],a=new j,c))a.has(d)||a.set(d,b.push(d)-1);return e},e.range=function(a){return arguments.length?(c=Array.from(a),e):c.slice()},e.unknown=function(a){return arguments.length?(d=a,e):d},e.copy=function(){return n(b,c).unknown(d)},h.apply(e,arguments),e}function o(){var a,b,c=n().unknown(void 0),d=c.domain,e=c.range,f=0,g=1,i=!1,j=0,k=0,l=.5;function m(){var c=d().length,h=g<f,m=h?g:f,n=h?f:g;a=(n-m)/Math.max(1,c-j+2*k),i&&(a=Math.floor(a)),m+=(n-m-a*(c-j))*l,b=a*(1-j),i&&(m=Math.round(m),b=Math.round(b));var o=(function(a,b,c){a*=1,b*=1,c=(e=arguments.length)<2?(b=a,a=0,1):e<3?1:+c;for(var d=-1,e=0|Math.max(0,Math.ceil((b-a)/c)),f=Array(e);++d<e;)f[d]=a+d*c;return f})(c).map(function(b){return m+a*b});return e(h?o.reverse():o)}return delete c.unknown,c.domain=function(a){return arguments.length?(d(a),m()):d()},c.range=function(a){return arguments.length?([f,g]=a,f*=1,g*=1,m()):[f,g]},c.rangeRound=function(a){return[f,g]=a,f*=1,g*=1,i=!0,m()},c.bandwidth=function(){return b},c.step=function(){return a},c.round=function(a){return arguments.length?(i=!!a,m()):i},c.padding=function(a){return arguments.length?(j=Math.min(1,k=+a),m()):j},c.paddingInner=function(a){return arguments.length?(j=Math.min(1,a),m()):j},c.paddingOuter=function(a){return arguments.length?(k=+a,m()):k},c.align=function(a){return arguments.length?(l=Math.max(0,Math.min(1,a)),m()):l},c.copy=function(){return o(d(),[f,g]).round(i).paddingInner(j).paddingOuter(k).align(l)},h.apply(m(),arguments)}function p(){return function a(b){var c=b.copy;return b.padding=b.paddingOuter,delete b.paddingInner,delete b.paddingOuter,b.copy=function(){return a(c())},b}(o.apply(null,arguments).paddingInner(1))}let q=Math.sqrt(50),r=Math.sqrt(10),s=Math.sqrt(2);function t(a,b,c){let d,e,f,g=(b-a)/Math.max(0,c),h=Math.floor(Math.log10(g)),i=g/Math.pow(10,h),j=i>=q?10:i>=r?5:i>=s?2:1;return(h<0?(d=Math.round(a*(f=Math.pow(10,-h)/j)),e=Math.round(b*f),d/f<a&&++d,e/f>b&&--e,f=-f):(d=Math.round(a/(f=Math.pow(10,h)*j)),e=Math.round(b/f),d*f<a&&++d,e*f>b&&--e),e<d&&.5<=c&&c<2)?t(a,b,2*c):[d,e,f]}function u(a,b,c){if(b*=1,a*=1,!((c*=1)>0))return[];if(a===b)return[a];let d=b<a,[e,f,g]=d?t(b,a,c):t(a,b,c);if(!(f>=e))return[];let h=f-e+1,i=Array(h);if(d)if(g<0)for(let a=0;a<h;++a)i[a]=-((f-a)/g);else for(let a=0;a<h;++a)i[a]=(f-a)*g;else if(g<0)for(let a=0;a<h;++a)i[a]=-((e+a)/g);else for(let a=0;a<h;++a)i[a]=(e+a)*g;return i}function v(a,b,c){return t(a*=1,b*=1,c*=1)[2]}function w(a,b,c){b*=1,a*=1,c*=1;let d=b<a,e=d?v(b,a,c):v(a,b,c);return(d?-1:1)*(e<0?-(1/e):e)}function x(a,b){return null==a||null==b?NaN:a<b?-1:a>b?1:a>=b?0:NaN}function y(a,b){return null==a||null==b?NaN:b<a?-1:b>a?1:b>=a?0:NaN}function z(a){let b,c,d;function e(a,d,f=0,g=a.length){if(f<g){if(0!==b(d,d))return g;do{let b=f+g>>>1;0>c(a[b],d)?f=b+1:g=b}while(f<g)}return f}return 2!==a.length?(b=x,c=(b,c)=>x(a(b),c),d=(b,c)=>a(b)-c):(b=a===x||a===y?a:A,c=a,d=a),{left:e,center:function(a,b,c=0,f=a.length){let g=e(a,b,c,f-1);return g>c&&d(a[g-1],b)>-d(a[g],b)?g-1:g},right:function(a,d,e=0,f=a.length){if(e<f){if(0!==b(d,d))return f;do{let b=e+f>>>1;0>=c(a[b],d)?e=b+1:f=b}while(e<f)}return e}}}function A(){return 0}function B(a){return null===a?NaN:+a}let C=z(x),D=C.right;function E(a,b,c){a.prototype=b.prototype=c,c.constructor=a}function F(a,b){var c=Object.create(a.prototype);for(var d in b)c[d]=b[d];return c}function G(){}C.left,z(B).center;var H="\\s*([+-]?\\d+)\\s*",I="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",J="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",K=/^#([0-9a-f]{3,8})$/,L=RegExp(`^rgb\\(${H},${H},${H}\\)$`),M=RegExp(`^rgb\\(${J},${J},${J}\\)$`),N=RegExp(`^rgba\\(${H},${H},${H},${I}\\)$`),O=RegExp(`^rgba\\(${J},${J},${J},${I}\\)$`),P=RegExp(`^hsl\\(${I},${J},${J}\\)$`),Q=RegExp(`^hsla\\(${I},${J},${J},${I}\\)$`),R={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function S(){return this.rgb().formatHex()}function T(){return this.rgb().formatRgb()}function U(a){var b,c;return a=(a+"").trim().toLowerCase(),(b=K.exec(a))?(c=b[1].length,b=parseInt(b[1],16),6===c?V(b):3===c?new Y(b>>8&15|b>>4&240,b>>4&15|240&b,(15&b)<<4|15&b,1):8===c?W(b>>24&255,b>>16&255,b>>8&255,(255&b)/255):4===c?W(b>>12&15|b>>8&240,b>>8&15|b>>4&240,b>>4&15|240&b,((15&b)<<4|15&b)/255):null):(b=L.exec(a))?new Y(b[1],b[2],b[3],1):(b=M.exec(a))?new Y(255*b[1]/100,255*b[2]/100,255*b[3]/100,1):(b=N.exec(a))?W(b[1],b[2],b[3],b[4]):(b=O.exec(a))?W(255*b[1]/100,255*b[2]/100,255*b[3]/100,b[4]):(b=P.exec(a))?ac(b[1],b[2]/100,b[3]/100,1):(b=Q.exec(a))?ac(b[1],b[2]/100,b[3]/100,b[4]):R.hasOwnProperty(a)?V(R[a]):"transparent"===a?new Y(NaN,NaN,NaN,0):null}function V(a){return new Y(a>>16&255,a>>8&255,255&a,1)}function W(a,b,c,d){return d<=0&&(a=b=c=NaN),new Y(a,b,c,d)}function X(a,b,c,d){var e;return 1==arguments.length?((e=a)instanceof G||(e=U(e)),e)?new Y((e=e.rgb()).r,e.g,e.b,e.opacity):new Y:new Y(a,b,c,null==d?1:d)}function Y(a,b,c,d){this.r=+a,this.g=+b,this.b=+c,this.opacity=+d}function Z(){return`#${ab(this.r)}${ab(this.g)}${ab(this.b)}`}function $(){let a=_(this.opacity);return`${1===a?"rgb(":"rgba("}${aa(this.r)}, ${aa(this.g)}, ${aa(this.b)}${1===a?")":`, ${a})`}`}function _(a){return isNaN(a)?1:Math.max(0,Math.min(1,a))}function aa(a){return Math.max(0,Math.min(255,Math.round(a)||0))}function ab(a){return((a=aa(a))<16?"0":"")+a.toString(16)}function ac(a,b,c,d){return d<=0?a=b=c=NaN:c<=0||c>=1?a=b=NaN:b<=0&&(a=NaN),new ae(a,b,c,d)}function ad(a){if(a instanceof ae)return new ae(a.h,a.s,a.l,a.opacity);if(a instanceof G||(a=U(a)),!a)return new ae;if(a instanceof ae)return a;var b=(a=a.rgb()).r/255,c=a.g/255,d=a.b/255,e=Math.min(b,c,d),f=Math.max(b,c,d),g=NaN,h=f-e,i=(f+e)/2;return h?(g=b===f?(c-d)/h+(c<d)*6:c===f?(d-b)/h+2:(b-c)/h+4,h/=i<.5?f+e:2-f-e,g*=60):h=i>0&&i<1?0:g,new ae(g,h,i,a.opacity)}function ae(a,b,c,d){this.h=+a,this.s=+b,this.l=+c,this.opacity=+d}function af(a){return(a=(a||0)%360)<0?a+360:a}function ag(a){return Math.max(0,Math.min(1,a||0))}function ah(a,b,c){return(a<60?b+(c-b)*a/60:a<180?c:a<240?b+(c-b)*(240-a)/60:b)*255}function ai(a,b,c,d,e){var f=a*a,g=f*a;return((1-3*a+3*f-g)*b+(4-6*f+3*g)*c+(1+3*a+3*f-3*g)*d+g*e)/6}E(G,U,{copy(a){return Object.assign(new this.constructor,this,a)},displayable(){return this.rgb().displayable()},hex:S,formatHex:S,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return ad(this).formatHsl()},formatRgb:T,toString:T}),E(Y,X,F(G,{brighter(a){return a=null==a?1.4285714285714286:Math.pow(1.4285714285714286,a),new Y(this.r*a,this.g*a,this.b*a,this.opacity)},darker(a){return a=null==a?.7:Math.pow(.7,a),new Y(this.r*a,this.g*a,this.b*a,this.opacity)},rgb(){return this},clamp(){return new Y(aa(this.r),aa(this.g),aa(this.b),_(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Z,formatHex:Z,formatHex8:function(){return`#${ab(this.r)}${ab(this.g)}${ab(this.b)}${ab((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:$,toString:$})),E(ae,function(a,b,c,d){return 1==arguments.length?ad(a):new ae(a,b,c,null==d?1:d)},F(G,{brighter(a){return a=null==a?1.4285714285714286:Math.pow(1.4285714285714286,a),new ae(this.h,this.s,this.l*a,this.opacity)},darker(a){return a=null==a?.7:Math.pow(.7,a),new ae(this.h,this.s,this.l*a,this.opacity)},rgb(){var a=this.h%360+(this.h<0)*360,b=isNaN(a)||isNaN(this.s)?0:this.s,c=this.l,d=c+(c<.5?c:1-c)*b,e=2*c-d;return new Y(ah(a>=240?a-240:a+120,e,d),ah(a,e,d),ah(a<120?a+240:a-120,e,d),this.opacity)},clamp(){return new ae(af(this.h),ag(this.s),ag(this.l),_(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let a=_(this.opacity);return`${1===a?"hsl(":"hsla("}${af(this.h)}, ${100*ag(this.s)}%, ${100*ag(this.l)}%${1===a?")":`, ${a})`}`}}));let aj=a=>()=>a;function ak(a,b){var c=b-a;return c?function(b){return a+b*c}:aj(isNaN(a)?b:a)}let al=function a(b){var c,d=1==(c=+b)?ak:function(a,b){var d,e,f;return b-a?(d=a,e=b,d=Math.pow(d,f=c),e=Math.pow(e,f)-d,f=1/f,function(a){return Math.pow(d+a*e,f)}):aj(isNaN(a)?b:a)};function e(a,b){var c=d((a=X(a)).r,(b=X(b)).r),e=d(a.g,b.g),f=d(a.b,b.b),g=ak(a.opacity,b.opacity);return function(b){return a.r=c(b),a.g=e(b),a.b=f(b),a.opacity=g(b),a+""}}return e.gamma=a,e}(1);function am(a){return function(b){var c,d,e=b.length,f=Array(e),g=Array(e),h=Array(e);for(c=0;c<e;++c)d=X(b[c]),f[c]=d.r||0,g[c]=d.g||0,h[c]=d.b||0;return f=a(f),g=a(g),h=a(h),d.opacity=1,function(a){return d.r=f(a),d.g=g(a),d.b=h(a),d+""}}}function an(a,b){return a*=1,b*=1,function(c){return a*(1-c)+b*c}}am(function(a){var b=a.length-1;return function(c){var d=c<=0?c=0:c>=1?(c=1,b-1):Math.floor(c*b),e=a[d],f=a[d+1],g=d>0?a[d-1]:2*e-f,h=d<b-1?a[d+2]:2*f-e;return ai((c-d/b)*b,g,e,f,h)}}),am(function(a){var b=a.length;return function(c){var d=Math.floor(((c%=1)<0?++c:c)*b),e=a[(d+b-1)%b],f=a[d%b],g=a[(d+1)%b],h=a[(d+2)%b];return ai((c-d/b)*b,e,f,g,h)}});var ao=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ap=RegExp(ao.source,"g");function aq(a,b){var c,d,e=typeof b;return null==b||"boolean"===e?aj(b):("number"===e?an:"string"===e?(d=U(b))?(b=d,al):function(a,b){var c,d,e,f,g,h=ao.lastIndex=ap.lastIndex=0,i=-1,j=[],k=[];for(a+="",b+="";(e=ao.exec(a))&&(f=ap.exec(b));)(g=f.index)>h&&(g=b.slice(h,g),j[i]?j[i]+=g:j[++i]=g),(e=e[0])===(f=f[0])?j[i]?j[i]+=f:j[++i]=f:(j[++i]=null,k.push({i:i,x:an(e,f)})),h=ap.lastIndex;return h<b.length&&(g=b.slice(h),j[i]?j[i]+=g:j[++i]=g),j.length<2?k[0]?(c=k[0].x,function(a){return c(a)+""}):(d=b,function(){return d}):(b=k.length,function(a){for(var c,d=0;d<b;++d)j[(c=k[d]).i]=c.x(a);return j.join("")})}:b instanceof U?al:b instanceof Date?function(a,b){var c=new Date;return a*=1,b*=1,function(d){return c.setTime(a*(1-d)+b*d),c}}:!ArrayBuffer.isView(c=b)||c instanceof DataView?Array.isArray(b)?function(a,b){var c,d=b?b.length:0,e=a?Math.min(d,a.length):0,f=Array(e),g=Array(d);for(c=0;c<e;++c)f[c]=aq(a[c],b[c]);for(;c<d;++c)g[c]=b[c];return function(a){for(c=0;c<e;++c)g[c]=f[c](a);return g}}:"function"!=typeof b.valueOf&&"function"!=typeof b.toString||isNaN(b)?function(a,b){var c,d={},e={};for(c in(null===a||"object"!=typeof a)&&(a={}),(null===b||"object"!=typeof b)&&(b={}),b)c in a?d[c]=aq(a[c],b[c]):e[c]=b[c];return function(a){for(c in d)e[c]=d[c](a);return e}}:an:function(a,b){b||(b=[]);var c,d=a?Math.min(b.length,a.length):0,e=b.slice();return function(f){for(c=0;c<d;++c)e[c]=a[c]*(1-f)+b[c]*f;return e}})(a,b)}function ar(a,b){return a*=1,b*=1,function(c){return Math.round(a*(1-c)+b*c)}}function as(a){return+a}var at=[0,1];function au(a){return a}function av(a,b){var c;return(b-=a*=1)?function(c){return(c-a)/b}:(c=isNaN(b)?NaN:.5,function(){return c})}function aw(a,b,c){var d=a[0],e=a[1],f=b[0],g=b[1];return e<d?(d=av(e,d),f=c(g,f)):(d=av(d,e),f=c(f,g)),function(a){return f(d(a))}}function ax(a,b,c){var d=Math.min(a.length,b.length)-1,e=Array(d),f=Array(d),g=-1;for(a[d]<a[0]&&(a=a.slice().reverse(),b=b.slice().reverse());++g<d;)e[g]=av(a[g],a[g+1]),f[g]=c(b[g],b[g+1]);return function(b){var c=D(a,b,1,d)-1;return f[c](e[c](b))}}function ay(a,b){return b.domain(a.domain()).range(a.range()).interpolate(a.interpolate()).clamp(a.clamp()).unknown(a.unknown())}function az(){var a,b,c,d,e,f,g=at,h=at,i=aq,j=au;function k(){var a,b,c,i=Math.min(g.length,h.length);return j!==au&&(a=g[0],b=g[i-1],a>b&&(c=a,a=b,b=c),j=function(c){return Math.max(a,Math.min(b,c))}),d=i>2?ax:aw,e=f=null,l}function l(b){return null==b||isNaN(b*=1)?c:(e||(e=d(g.map(a),h,i)))(a(j(b)))}return l.invert=function(c){return j(b((f||(f=d(h,g.map(a),an)))(c)))},l.domain=function(a){return arguments.length?(g=Array.from(a,as),k()):g.slice()},l.range=function(a){return arguments.length?(h=Array.from(a),k()):h.slice()},l.rangeRound=function(a){return h=Array.from(a),i=ar,k()},l.clamp=function(a){return arguments.length?(j=!!a||au,k()):j!==au},l.interpolate=function(a){return arguments.length?(i=a,k()):i},l.unknown=function(a){return arguments.length?(c=a,l):c},function(c,d){return a=c,b=d,k()}}function aA(){return az()(au,au)}var aB=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function aC(a){var b;if(!(b=aB.exec(a)))throw Error("invalid format: "+a);return new aD({fill:b[1],align:b[2],sign:b[3],symbol:b[4],zero:b[5],width:b[6],comma:b[7],precision:b[8]&&b[8].slice(1),trim:b[9],type:b[10]})}function aD(a){this.fill=void 0===a.fill?" ":a.fill+"",this.align=void 0===a.align?">":a.align+"",this.sign=void 0===a.sign?"-":a.sign+"",this.symbol=void 0===a.symbol?"":a.symbol+"",this.zero=!!a.zero,this.width=void 0===a.width?void 0:+a.width,this.comma=!!a.comma,this.precision=void 0===a.precision?void 0:+a.precision,this.trim=!!a.trim,this.type=void 0===a.type?"":a.type+""}function aE(a,b){if((c=(a=b?a.toExponential(b-1):a.toExponential()).indexOf("e"))<0)return null;var c,d=a.slice(0,c);return[d.length>1?d[0]+d.slice(2):d,+a.slice(c+1)]}function aF(a){return(a=aE(Math.abs(a)))?a[1]:NaN}function aG(a,b){var c=aE(a,b);if(!c)return a+"";var d=c[0],e=c[1];return e<0?"0."+Array(-e).join("0")+d:d.length>e+1?d.slice(0,e+1)+"."+d.slice(e+1):d+Array(e-d.length+2).join("0")}aC.prototype=aD.prototype,aD.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let aH={"%":(a,b)=>(100*a).toFixed(b),b:a=>Math.round(a).toString(2),c:a=>a+"",d:function(a){return Math.abs(a=Math.round(a))>=1e21?a.toLocaleString("en").replace(/,/g,""):a.toString(10)},e:(a,b)=>a.toExponential(b),f:(a,b)=>a.toFixed(b),g:(a,b)=>a.toPrecision(b),o:a=>Math.round(a).toString(8),p:(a,b)=>aG(100*a,b),r:aG,s:function(a,b){var c=aE(a,b);if(!c)return a+"";var d=c[0],e=c[1],f=e-(c9=3*Math.max(-8,Math.min(8,Math.floor(e/3))))+1,g=d.length;return f===g?d:f>g?d+Array(f-g+1).join("0"):f>0?d.slice(0,f)+"."+d.slice(f):"0."+Array(1-f).join("0")+aE(a,Math.max(0,b+f-1))[0]},X:a=>Math.round(a).toString(16).toUpperCase(),x:a=>Math.round(a).toString(16)};function aI(a){return a}var aJ=Array.prototype.map,aK=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function aL(a,b,c,d){var e,f,g=w(a,b,c);switch((d=aC(null==d?",f":d)).type){case"s":var h=Math.max(Math.abs(a),Math.abs(b));return null!=d.precision||isNaN(f=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(aF(h)/3)))-aF(Math.abs(g))))||(d.precision=f),dc(d,h);case"":case"e":case"g":case"p":case"r":null!=d.precision||isNaN(f=Math.max(0,aF(Math.abs(Math.max(Math.abs(a),Math.abs(b)))-(e=Math.abs(e=g)))-aF(e))+1)||(d.precision=f-("e"===d.type));break;case"f":case"%":null!=d.precision||isNaN(f=Math.max(0,-aF(Math.abs(g))))||(d.precision=f-("%"===d.type)*2)}return db(d)}function aM(a){var b=a.domain;return a.ticks=function(a){var c=b();return u(c[0],c[c.length-1],null==a?10:a)},a.tickFormat=function(a,c){var d=b();return aL(d[0],d[d.length-1],null==a?10:a,c)},a.nice=function(c){null==c&&(c=10);var d,e,f=b(),g=0,h=f.length-1,i=f[g],j=f[h],k=10;for(j<i&&(e=i,i=j,j=e,e=g,g=h,h=e);k-- >0;){if((e=v(i,j,c))===d)return f[g]=i,f[h]=j,b(f);if(e>0)i=Math.floor(i/e)*e,j=Math.ceil(j/e)*e;else if(e<0)i=Math.ceil(i*e)/e,j=Math.floor(j*e)/e;else break;d=e}return a},a}function aN(a,b){a=a.slice();var c,d=0,e=a.length-1,f=a[d],g=a[e];return g<f&&(c=d,d=e,e=c,c=f,f=g,g=c),a[d]=b.floor(f),a[e]=b.ceil(g),a}function aO(a){return Math.log(a)}function aP(a){return Math.exp(a)}function aQ(a){return-Math.log(-a)}function aR(a){return-Math.exp(-a)}function aS(a){return isFinite(a)?+("1e"+a):a<0?0:a}function aT(a){return(b,c)=>-a(-b,c)}function aU(a){let b,c,d=a(aO,aP),e=d.domain,f=10;function g(){var g,h;return b=(g=f)===Math.E?Math.log:10===g&&Math.log10||2===g&&Math.log2||(g=Math.log(g),a=>Math.log(a)/g),c=10===(h=f)?aS:h===Math.E?Math.exp:a=>Math.pow(h,a),e()[0]<0?(b=aT(b),c=aT(c),a(aQ,aR)):a(aO,aP),d}return d.base=function(a){return arguments.length?(f=+a,g()):f},d.domain=function(a){return arguments.length?(e(a),g()):e()},d.ticks=a=>{let d,g,h=e(),i=h[0],j=h[h.length-1],k=j<i;k&&([i,j]=[j,i]);let l=b(i),m=b(j),n=null==a?10:+a,o=[];if(!(f%1)&&m-l<n){if(l=Math.floor(l),m=Math.ceil(m),i>0){for(;l<=m;++l)for(d=1;d<f;++d)if(!((g=l<0?d/c(-l):d*c(l))<i)){if(g>j)break;o.push(g)}}else for(;l<=m;++l)for(d=f-1;d>=1;--d)if(!((g=l>0?d/c(-l):d*c(l))<i)){if(g>j)break;o.push(g)}2*o.length<n&&(o=u(i,j,n))}else o=u(l,m,Math.min(m-l,n)).map(c);return k?o.reverse():o},d.tickFormat=(a,e)=>{if(null==a&&(a=10),null==e&&(e=10===f?"s":","),"function"!=typeof e&&(f%1||null!=(e=aC(e)).precision||(e.trim=!0),e=db(e)),a===1/0)return e;let g=Math.max(1,f*a/d.ticks().length);return a=>{let d=a/c(Math.round(b(a)));return d*f<f-.5&&(d*=f),d<=g?e(a):""}},d.nice=()=>e(aN(e(),{floor:a=>c(Math.floor(b(a))),ceil:a=>c(Math.ceil(b(a)))})),d}function aV(a){return function(b){return Math.sign(b)*Math.log1p(Math.abs(b/a))}}function aW(a){return function(b){return Math.sign(b)*Math.expm1(Math.abs(b))*a}}function aX(a){var b=1,c=a(aV(1),aW(b));return c.constant=function(c){return arguments.length?a(aV(b=+c),aW(b)):b},aM(c)}function aY(a){return function(b){return b<0?-Math.pow(-b,a):Math.pow(b,a)}}function aZ(a){return a<0?-Math.sqrt(-a):Math.sqrt(a)}function a$(a){return a<0?-a*a:a*a}function a_(a){var b=a(au,au),c=1;return b.exponent=function(b){return arguments.length?1==(c=+b)?a(au,au):.5===c?a(aZ,a$):a(aY(c),aY(1/c)):c},aM(b)}function a0(){var a=a_(az());return a.copy=function(){return ay(a,a0()).exponent(a.exponent())},h.apply(a,arguments),a}function a1(){return a0.apply(null,arguments).exponent(.5)}function a2(a){return Math.sign(a)*a*a}function a3(a,b){let c;if(void 0===b)for(let b of a)null!=b&&(c<b||void 0===c&&b>=b)&&(c=b);else{let d=-1;for(let e of a)null!=(e=b(e,++d,a))&&(c<e||void 0===c&&e>=e)&&(c=e)}return c}function a4(a,b){let c;if(void 0===b)for(let b of a)null!=b&&(c>b||void 0===c&&b>=b)&&(c=b);else{let d=-1;for(let e of a)null!=(e=b(e,++d,a))&&(c>e||void 0===c&&e>=e)&&(c=e)}return c}function a5(a,b){return(null==a||!(a>=a))-(null==b||!(b>=b))||(a<b?-1:+(a>b))}function a6(a,b,c){let d=a[b];a[b]=a[c],a[c]=d}db=(da=function(a){var b,c,d,e=void 0===a.grouping||void 0===a.thousands?aI:(b=aJ.call(a.grouping,Number),c=a.thousands+"",function(a,d){for(var e=a.length,f=[],g=0,h=b[0],i=0;e>0&&h>0&&(i+h+1>d&&(h=Math.max(1,d-i)),f.push(a.substring(e-=h,e+h)),!((i+=h+1)>d));)h=b[g=(g+1)%b.length];return f.reverse().join(c)}),f=void 0===a.currency?"":a.currency[0]+"",g=void 0===a.currency?"":a.currency[1]+"",h=void 0===a.decimal?".":a.decimal+"",i=void 0===a.numerals?aI:(d=aJ.call(a.numerals,String),function(a){return a.replace(/[0-9]/g,function(a){return d[+a]})}),j=void 0===a.percent?"%":a.percent+"",k=void 0===a.minus?"−":a.minus+"",l=void 0===a.nan?"NaN":a.nan+"";function m(a){var b=(a=aC(a)).fill,c=a.align,d=a.sign,m=a.symbol,n=a.zero,o=a.width,p=a.comma,q=a.precision,r=a.trim,s=a.type;"n"===s?(p=!0,s="g"):aH[s]||(void 0===q&&(q=12),r=!0,s="g"),(n||"0"===b&&"="===c)&&(n=!0,b="0",c="=");var t="$"===m?f:"#"===m&&/[boxX]/.test(s)?"0"+s.toLowerCase():"",u="$"===m?g:/[%p]/.test(s)?j:"",v=aH[s],w=/[defgprs%]/.test(s);function x(a){var f,g,j,m=t,x=u;if("c"===s)x=v(a)+x,a="";else{var y=(a*=1)<0||1/a<0;if(a=isNaN(a)?l:v(Math.abs(a),q),r&&(a=function(a){a:for(var b,c=a.length,d=1,e=-1;d<c;++d)switch(a[d]){case".":e=b=d;break;case"0":0===e&&(e=d),b=d;break;default:if(!+a[d])break a;e>0&&(e=0)}return e>0?a.slice(0,e)+a.slice(b+1):a}(a)),y&&0==+a&&"+"!==d&&(y=!1),m=(y?"("===d?d:k:"-"===d||"("===d?"":d)+m,x=("s"===s?aK[8+c9/3]:"")+x+(y&&"("===d?")":""),w){for(f=-1,g=a.length;++f<g;)if(48>(j=a.charCodeAt(f))||j>57){x=(46===j?h+a.slice(f+1):a.slice(f))+x,a=a.slice(0,f);break}}}p&&!n&&(a=e(a,1/0));var z=m.length+a.length+x.length,A=z<o?Array(o-z+1).join(b):"";switch(p&&n&&(a=e(A+a,A.length?o-x.length:1/0),A=""),c){case"<":a=m+a+x+A;break;case"=":a=m+A+a+x;break;case"^":a=A.slice(0,z=A.length>>1)+m+a+x+A.slice(z);break;default:a=A+m+a+x}return i(a)}return q=void 0===q?6:/[gprs]/.test(s)?Math.max(1,Math.min(21,q)):Math.max(0,Math.min(20,q)),x.toString=function(){return a+""},x}return{format:m,formatPrefix:function(a,b){var c=m(((a=aC(a)).type="f",a)),d=3*Math.max(-8,Math.min(8,Math.floor(aF(b)/3))),e=Math.pow(10,-d),f=aK[8+d/3];return function(a){return c(e*a)+f}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,dc=da.formatPrefix;let a7=new Date,a8=new Date;function a9(a,b,c,d){function e(b){return a(b=0==arguments.length?new Date:new Date(+b)),b}return e.floor=b=>(a(b=new Date(+b)),b),e.ceil=c=>(a(c=new Date(c-1)),b(c,1),a(c),c),e.round=a=>{let b=e(a),c=e.ceil(a);return a-b<c-a?b:c},e.offset=(a,c)=>(b(a=new Date(+a),null==c?1:Math.floor(c)),a),e.range=(c,d,f)=>{let g,h=[];if(c=e.ceil(c),f=null==f?1:Math.floor(f),!(c<d)||!(f>0))return h;do h.push(g=new Date(+c)),b(c,f),a(c);while(g<c&&c<d);return h},e.filter=c=>a9(b=>{if(b>=b)for(;a(b),!c(b);)b.setTime(b-1)},(a,d)=>{if(a>=a)if(d<0)for(;++d<=0;)for(;b(a,-1),!c(a););else for(;--d>=0;)for(;b(a,1),!c(a););}),c&&(e.count=(b,d)=>(a7.setTime(+b),a8.setTime(+d),a(a7),a(a8),Math.floor(c(a7,a8))),e.every=a=>isFinite(a=Math.floor(a))&&a>0?a>1?e.filter(d?b=>d(b)%a==0:b=>e.count(0,b)%a==0):e:null),e}let ba=a9(()=>{},(a,b)=>{a.setTime(+a+b)},(a,b)=>b-a);ba.every=a=>isFinite(a=Math.floor(a))&&a>0?a>1?a9(b=>{b.setTime(Math.floor(b/a)*a)},(b,c)=>{b.setTime(+b+c*a)},(b,c)=>(c-b)/a):ba:null,ba.range;let bb=a9(a=>{a.setTime(a-a.getMilliseconds())},(a,b)=>{a.setTime(+a+1e3*b)},(a,b)=>(b-a)/1e3,a=>a.getUTCSeconds());bb.range;let bc=a9(a=>{a.setTime(a-a.getMilliseconds()-1e3*a.getSeconds())},(a,b)=>{a.setTime(+a+6e4*b)},(a,b)=>(b-a)/6e4,a=>a.getMinutes());bc.range;let bd=a9(a=>{a.setUTCSeconds(0,0)},(a,b)=>{a.setTime(+a+6e4*b)},(a,b)=>(b-a)/6e4,a=>a.getUTCMinutes());bd.range;let be=a9(a=>{a.setTime(a-a.getMilliseconds()-1e3*a.getSeconds()-6e4*a.getMinutes())},(a,b)=>{a.setTime(+a+36e5*b)},(a,b)=>(b-a)/36e5,a=>a.getHours());be.range;let bf=a9(a=>{a.setUTCMinutes(0,0,0)},(a,b)=>{a.setTime(+a+36e5*b)},(a,b)=>(b-a)/36e5,a=>a.getUTCHours());bf.range;let bg=a9(a=>a.setHours(0,0,0,0),(a,b)=>a.setDate(a.getDate()+b),(a,b)=>(b-a-(b.getTimezoneOffset()-a.getTimezoneOffset())*6e4)/864e5,a=>a.getDate()-1);bg.range;let bh=a9(a=>{a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+b)},(a,b)=>(b-a)/864e5,a=>a.getUTCDate()-1);bh.range;let bi=a9(a=>{a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+b)},(a,b)=>(b-a)/864e5,a=>Math.floor(a/864e5));function bj(a){return a9(b=>{b.setDate(b.getDate()-(b.getDay()+7-a)%7),b.setHours(0,0,0,0)},(a,b)=>{a.setDate(a.getDate()+7*b)},(a,b)=>(b-a-(b.getTimezoneOffset()-a.getTimezoneOffset())*6e4)/6048e5)}bi.range;let bk=bj(0),bl=bj(1),bm=bj(2),bn=bj(3),bo=bj(4),bp=bj(5),bq=bj(6);function br(a){return a9(b=>{b.setUTCDate(b.getUTCDate()-(b.getUTCDay()+7-a)%7),b.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+7*b)},(a,b)=>(b-a)/6048e5)}bk.range,bl.range,bm.range,bn.range,bo.range,bp.range,bq.range;let bs=br(0),bt=br(1),bu=br(2),bv=br(3),bw=br(4),bx=br(5),by=br(6);bs.range,bt.range,bu.range,bv.range,bw.range,bx.range,by.range;let bz=a9(a=>{a.setDate(1),a.setHours(0,0,0,0)},(a,b)=>{a.setMonth(a.getMonth()+b)},(a,b)=>b.getMonth()-a.getMonth()+(b.getFullYear()-a.getFullYear())*12,a=>a.getMonth());bz.range;let bA=a9(a=>{a.setUTCDate(1),a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCMonth(a.getUTCMonth()+b)},(a,b)=>b.getUTCMonth()-a.getUTCMonth()+(b.getUTCFullYear()-a.getUTCFullYear())*12,a=>a.getUTCMonth());bA.range;let bB=a9(a=>{a.setMonth(0,1),a.setHours(0,0,0,0)},(a,b)=>{a.setFullYear(a.getFullYear()+b)},(a,b)=>b.getFullYear()-a.getFullYear(),a=>a.getFullYear());bB.every=a=>isFinite(a=Math.floor(a))&&a>0?a9(b=>{b.setFullYear(Math.floor(b.getFullYear()/a)*a),b.setMonth(0,1),b.setHours(0,0,0,0)},(b,c)=>{b.setFullYear(b.getFullYear()+c*a)}):null,bB.range;let bC=a9(a=>{a.setUTCMonth(0,1),a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCFullYear(a.getUTCFullYear()+b)},(a,b)=>b.getUTCFullYear()-a.getUTCFullYear(),a=>a.getUTCFullYear());function bD(a,b,c,d,e,f){let g=[[bb,1,1e3],[bb,5,5e3],[bb,15,15e3],[bb,30,3e4],[f,1,6e4],[f,5,3e5],[f,15,9e5],[f,30,18e5],[e,1,36e5],[e,3,108e5],[e,6,216e5],[e,12,432e5],[d,1,864e5],[d,2,1728e5],[c,1,6048e5],[b,1,2592e6],[b,3,7776e6],[a,1,31536e6]];function h(b,c,d){let e=Math.abs(c-b)/d,f=z(([,,a])=>a).right(g,e);if(f===g.length)return a.every(w(b/31536e6,c/31536e6,d));if(0===f)return ba.every(Math.max(w(b,c,d),1));let[h,i]=g[e/g[f-1][2]<g[f][2]/e?f-1:f];return h.every(i)}return[function(a,b,c){let d=b<a;d&&([a,b]=[b,a]);let e=c&&"function"==typeof c.range?c:h(a,b,c),f=e?e.range(a,+b+1):[];return d?f.reverse():f},h]}bC.every=a=>isFinite(a=Math.floor(a))&&a>0?a9(b=>{b.setUTCFullYear(Math.floor(b.getUTCFullYear()/a)*a),b.setUTCMonth(0,1),b.setUTCHours(0,0,0,0)},(b,c)=>{b.setUTCFullYear(b.getUTCFullYear()+c*a)}):null,bC.range;let[bE,bF]=bD(bC,bA,bs,bi,bf,bd),[bG,bH]=bD(bB,bz,bk,bg,be,bc);function bI(a){if(0<=a.y&&a.y<100){var b=new Date(-1,a.m,a.d,a.H,a.M,a.S,a.L);return b.setFullYear(a.y),b}return new Date(a.y,a.m,a.d,a.H,a.M,a.S,a.L)}function bJ(a){if(0<=a.y&&a.y<100){var b=new Date(Date.UTC(-1,a.m,a.d,a.H,a.M,a.S,a.L));return b.setUTCFullYear(a.y),b}return new Date(Date.UTC(a.y,a.m,a.d,a.H,a.M,a.S,a.L))}function bK(a,b,c){return{y:a,m:b,d:c,H:0,M:0,S:0,L:0}}var bL={"-":"",_:" ",0:"0"},bM=/^\s*\d+/,bN=/^%/,bO=/[\\^$*+?|[\]().{}]/g;function bP(a,b,c){var d=a<0?"-":"",e=(d?-a:a)+"",f=e.length;return d+(f<c?Array(c-f+1).join(b)+e:e)}function bQ(a){return a.replace(bO,"\\$&")}function bR(a){return RegExp("^(?:"+a.map(bQ).join("|")+")","i")}function bS(a){return new Map(a.map((a,b)=>[a.toLowerCase(),b]))}function bT(a,b,c){var d=bM.exec(b.slice(c,c+1));return d?(a.w=+d[0],c+d[0].length):-1}function bU(a,b,c){var d=bM.exec(b.slice(c,c+1));return d?(a.u=+d[0],c+d[0].length):-1}function bV(a,b,c){var d=bM.exec(b.slice(c,c+2));return d?(a.U=+d[0],c+d[0].length):-1}function bW(a,b,c){var d=bM.exec(b.slice(c,c+2));return d?(a.V=+d[0],c+d[0].length):-1}function bX(a,b,c){var d=bM.exec(b.slice(c,c+2));return d?(a.W=+d[0],c+d[0].length):-1}function bY(a,b,c){var d=bM.exec(b.slice(c,c+4));return d?(a.y=+d[0],c+d[0].length):-1}function bZ(a,b,c){var d=bM.exec(b.slice(c,c+2));return d?(a.y=+d[0]+(+d[0]>68?1900:2e3),c+d[0].length):-1}function b$(a,b,c){var d=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(b.slice(c,c+6));return d?(a.Z=d[1]?0:-(d[2]+(d[3]||"00")),c+d[0].length):-1}function b_(a,b,c){var d=bM.exec(b.slice(c,c+1));return d?(a.q=3*d[0]-3,c+d[0].length):-1}function b0(a,b,c){var d=bM.exec(b.slice(c,c+2));return d?(a.m=d[0]-1,c+d[0].length):-1}function b1(a,b,c){var d=bM.exec(b.slice(c,c+2));return d?(a.d=+d[0],c+d[0].length):-1}function b2(a,b,c){var d=bM.exec(b.slice(c,c+3));return d?(a.m=0,a.d=+d[0],c+d[0].length):-1}function b3(a,b,c){var d=bM.exec(b.slice(c,c+2));return d?(a.H=+d[0],c+d[0].length):-1}function b4(a,b,c){var d=bM.exec(b.slice(c,c+2));return d?(a.M=+d[0],c+d[0].length):-1}function b5(a,b,c){var d=bM.exec(b.slice(c,c+2));return d?(a.S=+d[0],c+d[0].length):-1}function b6(a,b,c){var d=bM.exec(b.slice(c,c+3));return d?(a.L=+d[0],c+d[0].length):-1}function b7(a,b,c){var d=bM.exec(b.slice(c,c+6));return d?(a.L=Math.floor(d[0]/1e3),c+d[0].length):-1}function b8(a,b,c){var d=bN.exec(b.slice(c,c+1));return d?c+d[0].length:-1}function b9(a,b,c){var d=bM.exec(b.slice(c));return d?(a.Q=+d[0],c+d[0].length):-1}function ca(a,b,c){var d=bM.exec(b.slice(c));return d?(a.s=+d[0],c+d[0].length):-1}function cb(a,b){return bP(a.getDate(),b,2)}function cc(a,b){return bP(a.getHours(),b,2)}function cd(a,b){return bP(a.getHours()%12||12,b,2)}function ce(a,b){return bP(1+bg.count(bB(a),a),b,3)}function cf(a,b){return bP(a.getMilliseconds(),b,3)}function cg(a,b){return cf(a,b)+"000"}function ch(a,b){return bP(a.getMonth()+1,b,2)}function ci(a,b){return bP(a.getMinutes(),b,2)}function cj(a,b){return bP(a.getSeconds(),b,2)}function ck(a){var b=a.getDay();return 0===b?7:b}function cl(a,b){return bP(bk.count(bB(a)-1,a),b,2)}function cm(a){var b=a.getDay();return b>=4||0===b?bo(a):bo.ceil(a)}function cn(a,b){return a=cm(a),bP(bo.count(bB(a),a)+(4===bB(a).getDay()),b,2)}function co(a){return a.getDay()}function cp(a,b){return bP(bl.count(bB(a)-1,a),b,2)}function cq(a,b){return bP(a.getFullYear()%100,b,2)}function cr(a,b){return bP((a=cm(a)).getFullYear()%100,b,2)}function cs(a,b){return bP(a.getFullYear()%1e4,b,4)}function ct(a,b){var c=a.getDay();return bP((a=c>=4||0===c?bo(a):bo.ceil(a)).getFullYear()%1e4,b,4)}function cu(a){var b=a.getTimezoneOffset();return(b>0?"-":(b*=-1,"+"))+bP(b/60|0,"0",2)+bP(b%60,"0",2)}function cv(a,b){return bP(a.getUTCDate(),b,2)}function cw(a,b){return bP(a.getUTCHours(),b,2)}function cx(a,b){return bP(a.getUTCHours()%12||12,b,2)}function cy(a,b){return bP(1+bh.count(bC(a),a),b,3)}function cz(a,b){return bP(a.getUTCMilliseconds(),b,3)}function cA(a,b){return cz(a,b)+"000"}function cB(a,b){return bP(a.getUTCMonth()+1,b,2)}function cC(a,b){return bP(a.getUTCMinutes(),b,2)}function cD(a,b){return bP(a.getUTCSeconds(),b,2)}function cE(a){var b=a.getUTCDay();return 0===b?7:b}function cF(a,b){return bP(bs.count(bC(a)-1,a),b,2)}function cG(a){var b=a.getUTCDay();return b>=4||0===b?bw(a):bw.ceil(a)}function cH(a,b){return a=cG(a),bP(bw.count(bC(a),a)+(4===bC(a).getUTCDay()),b,2)}function cI(a){return a.getUTCDay()}function cJ(a,b){return bP(bt.count(bC(a)-1,a),b,2)}function cK(a,b){return bP(a.getUTCFullYear()%100,b,2)}function cL(a,b){return bP((a=cG(a)).getUTCFullYear()%100,b,2)}function cM(a,b){return bP(a.getUTCFullYear()%1e4,b,4)}function cN(a,b){var c=a.getUTCDay();return bP((a=c>=4||0===c?bw(a):bw.ceil(a)).getUTCFullYear()%1e4,b,4)}function cO(){return"+0000"}function cP(){return"%"}function cQ(a){return+a}function cR(a){return Math.floor(a/1e3)}function cS(a){return new Date(a)}function cT(a){return a instanceof Date?+a:+new Date(+a)}function cU(a,b,c,d,e,f,g,h,i,j){var k=aA(),l=k.invert,m=k.domain,n=j(".%L"),o=j(":%S"),p=j("%I:%M"),q=j("%I %p"),r=j("%a %d"),s=j("%b %d"),t=j("%B"),u=j("%Y");function v(a){return(i(a)<a?n:h(a)<a?o:g(a)<a?p:f(a)<a?q:d(a)<a?e(a)<a?r:s:c(a)<a?t:u)(a)}return k.invert=function(a){return new Date(l(a))},k.domain=function(a){return arguments.length?m(Array.from(a,cT)):m().map(cS)},k.ticks=function(b){var c=m();return a(c[0],c[c.length-1],null==b?10:b)},k.tickFormat=function(a,b){return null==b?v:j(b)},k.nice=function(a){var c=m();return a&&"function"==typeof a.range||(a=b(c[0],c[c.length-1],null==a?10:a)),a?m(aN(c,a)):k},k.copy=function(){return ay(k,cU(a,b,c,d,e,f,g,h,i,j))},k}function cV(){return h.apply(cU(bG,bH,bB,bz,bk,bg,be,bc,bb,de).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function cW(){return h.apply(cU(bE,bF,bC,bA,bs,bh,bf,bd,bb,df).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function cX(){var a,b,c,d,e,f=0,g=1,h=au,i=!1;function j(b){return null==b||isNaN(b*=1)?e:h(0===c?.5:(b=(d(b)-a)*c,i?Math.max(0,Math.min(1,b)):b))}function k(a){return function(b){var c,d;return arguments.length?([c,d]=b,h=a(c,d),j):[h(0),h(1)]}}return j.domain=function(e){return arguments.length?([f,g]=e,a=d(f*=1),b=d(g*=1),c=a===b?0:1/(b-a),j):[f,g]},j.clamp=function(a){return arguments.length?(i=!!a,j):i},j.interpolator=function(a){return arguments.length?(h=a,j):h},j.range=k(aq),j.rangeRound=k(ar),j.unknown=function(a){return arguments.length?(e=a,j):e},function(e){return d=e,a=e(f),b=e(g),c=a===b?0:1/(b-a),j}}function cY(a,b){return b.domain(a.domain()).interpolator(a.interpolator()).clamp(a.clamp()).unknown(a.unknown())}function cZ(){var a=a_(cX());return a.copy=function(){return cY(a,cZ()).exponent(a.exponent())},i.apply(a,arguments)}function c$(){return cZ.apply(null,arguments).exponent(.5)}function c_(){var a,b,c,d,e,f,g,h=0,i=.5,j=1,k=1,l=au,m=!1;function n(a){return isNaN(a*=1)?g:(a=.5+((a=+f(a))-b)*(k*a<k*b?d:e),l(m?Math.max(0,Math.min(1,a)):a))}function o(a){return function(b){var c,d,e;return arguments.length?([c,d,e]=b,l=function(a,b){void 0===b&&(b=a,a=aq);for(var c=0,d=b.length-1,e=b[0],f=Array(d<0?0:d);c<d;)f[c]=a(e,e=b[++c]);return function(a){var b=Math.max(0,Math.min(d-1,Math.floor(a*=d)));return f[b](a-b)}}(a,[c,d,e]),n):[l(0),l(.5),l(1)]}}return n.domain=function(g){return arguments.length?([h,i,j]=g,a=f(h*=1),b=f(i*=1),c=f(j*=1),d=a===b?0:.5/(b-a),e=b===c?0:.5/(c-b),k=b<a?-1:1,n):[h,i,j]},n.clamp=function(a){return arguments.length?(m=!!a,n):m},n.interpolator=function(a){return arguments.length?(l=a,n):l},n.range=o(aq),n.rangeRound=o(ar),n.unknown=function(a){return arguments.length?(g=a,n):g},function(g){return f=g,a=g(h),b=g(i),c=g(j),d=a===b?0:.5/(b-a),e=b===c?0:.5/(c-b),k=b<a?-1:1,n}}function c0(){var a=a_(c_());return a.copy=function(){return cY(a,c0()).exponent(a.exponent())},i.apply(a,arguments)}function c1(){return c0.apply(null,arguments).exponent(.5)}de=(dd=function(a){var b=a.dateTime,c=a.date,d=a.time,e=a.periods,f=a.days,g=a.shortDays,h=a.months,i=a.shortMonths,j=bR(e),k=bS(e),l=bR(f),m=bS(f),n=bR(g),o=bS(g),p=bR(h),q=bS(h),r=bR(i),s=bS(i),t={a:function(a){return g[a.getDay()]},A:function(a){return f[a.getDay()]},b:function(a){return i[a.getMonth()]},B:function(a){return h[a.getMonth()]},c:null,d:cb,e:cb,f:cg,g:cr,G:ct,H:cc,I:cd,j:ce,L:cf,m:ch,M:ci,p:function(a){return e[+(a.getHours()>=12)]},q:function(a){return 1+~~(a.getMonth()/3)},Q:cQ,s:cR,S:cj,u:ck,U:cl,V:cn,w:co,W:cp,x:null,X:null,y:cq,Y:cs,Z:cu,"%":cP},u={a:function(a){return g[a.getUTCDay()]},A:function(a){return f[a.getUTCDay()]},b:function(a){return i[a.getUTCMonth()]},B:function(a){return h[a.getUTCMonth()]},c:null,d:cv,e:cv,f:cA,g:cL,G:cN,H:cw,I:cx,j:cy,L:cz,m:cB,M:cC,p:function(a){return e[+(a.getUTCHours()>=12)]},q:function(a){return 1+~~(a.getUTCMonth()/3)},Q:cQ,s:cR,S:cD,u:cE,U:cF,V:cH,w:cI,W:cJ,x:null,X:null,y:cK,Y:cM,Z:cO,"%":cP},v={a:function(a,b,c){var d=n.exec(b.slice(c));return d?(a.w=o.get(d[0].toLowerCase()),c+d[0].length):-1},A:function(a,b,c){var d=l.exec(b.slice(c));return d?(a.w=m.get(d[0].toLowerCase()),c+d[0].length):-1},b:function(a,b,c){var d=r.exec(b.slice(c));return d?(a.m=s.get(d[0].toLowerCase()),c+d[0].length):-1},B:function(a,b,c){var d=p.exec(b.slice(c));return d?(a.m=q.get(d[0].toLowerCase()),c+d[0].length):-1},c:function(a,c,d){return y(a,b,c,d)},d:b1,e:b1,f:b7,g:bZ,G:bY,H:b3,I:b3,j:b2,L:b6,m:b0,M:b4,p:function(a,b,c){var d=j.exec(b.slice(c));return d?(a.p=k.get(d[0].toLowerCase()),c+d[0].length):-1},q:b_,Q:b9,s:ca,S:b5,u:bU,U:bV,V:bW,w:bT,W:bX,x:function(a,b,d){return y(a,c,b,d)},X:function(a,b,c){return y(a,d,b,c)},y:bZ,Y:bY,Z:b$,"%":b8};function w(a,b){return function(c){var d,e,f,g=[],h=-1,i=0,j=a.length;for(c instanceof Date||(c=new Date(+c));++h<j;)37===a.charCodeAt(h)&&(g.push(a.slice(i,h)),null!=(e=bL[d=a.charAt(++h)])?d=a.charAt(++h):e="e"===d?" ":"0",(f=b[d])&&(d=f(c,e)),g.push(d),i=h+1);return g.push(a.slice(i,h)),g.join("")}}function x(a,b){return function(c){var d,e,f=bK(1900,void 0,1);if(y(f,a,c+="",0)!=c.length)return null;if("Q"in f)return new Date(f.Q);if("s"in f)return new Date(1e3*f.s+("L"in f?f.L:0));if(!b||"Z"in f||(f.Z=0),"p"in f&&(f.H=f.H%12+12*f.p),void 0===f.m&&(f.m="q"in f?f.q:0),"V"in f){if(f.V<1||f.V>53)return null;"w"in f||(f.w=1),"Z"in f?(d=(e=(d=bJ(bK(f.y,0,1))).getUTCDay())>4||0===e?bt.ceil(d):bt(d),d=bh.offset(d,(f.V-1)*7),f.y=d.getUTCFullYear(),f.m=d.getUTCMonth(),f.d=d.getUTCDate()+(f.w+6)%7):(d=(e=(d=bI(bK(f.y,0,1))).getDay())>4||0===e?bl.ceil(d):bl(d),d=bg.offset(d,(f.V-1)*7),f.y=d.getFullYear(),f.m=d.getMonth(),f.d=d.getDate()+(f.w+6)%7)}else("W"in f||"U"in f)&&("w"in f||(f.w="u"in f?f.u%7:+("W"in f)),e="Z"in f?bJ(bK(f.y,0,1)).getUTCDay():bI(bK(f.y,0,1)).getDay(),f.m=0,f.d="W"in f?(f.w+6)%7+7*f.W-(e+5)%7:f.w+7*f.U-(e+6)%7);return"Z"in f?(f.H+=f.Z/100|0,f.M+=f.Z%100,bJ(f)):bI(f)}}function y(a,b,c,d){for(var e,f,g=0,h=b.length,i=c.length;g<h;){if(d>=i)return -1;if(37===(e=b.charCodeAt(g++))){if(!(f=v[(e=b.charAt(g++))in bL?b.charAt(g++):e])||(d=f(a,c,d))<0)return -1}else if(e!=c.charCodeAt(d++))return -1}return d}return t.x=w(c,t),t.X=w(d,t),t.c=w(b,t),u.x=w(c,u),u.X=w(d,u),u.c=w(b,u),{format:function(a){var b=w(a+="",t);return b.toString=function(){return a},b},parse:function(a){var b=x(a+="",!1);return b.toString=function(){return a},b},utcFormat:function(a){var b=w(a+="",u);return b.toString=function(){return a},b},utcParse:function(a){var b=x(a+="",!0);return b.toString=function(){return a},b}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,dd.parse,df=dd.utcFormat,dd.utcParse;var c2=c(15379),c3=c(79241),c4=c(8693),c5=c(22688),c6=c(53053);function c7(a){if(Array.isArray(a)&&2===a.length){var[b,c]=a;if((0,c6.H)(b)&&(0,c6.H)(c))return!0}return!1}function c8(a,b,c){return c?a:[Math.min(a[0],b[0]),Math.max(a[1],b[1])]}var c9,da,db,dc,dd,de,df,dg,dh,di=!0,dj="[DecimalError] ",dk=dj+"Invalid argument: ",dl=dj+"Exponent out of range: ",dm=Math.floor,dn=Math.pow,dp=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,dq=dm(1286742750677284.5),dr={};function ds(a,b){var c,d,e,f,g,h,i,j,k=a.constructor,l=k.precision;if(!a.s||!b.s)return b.s||(b=new k(a)),di?dC(b,l):b;if(i=a.d,j=b.d,g=a.e,e=b.e,i=i.slice(),f=g-e){for(f<0?(d=i,f=-f,h=j.length):(d=j,e=g,h=i.length),f>(h=(g=Math.ceil(l/7))>h?g+1:h+1)&&(f=h,d.length=1),d.reverse();f--;)d.push(0);d.reverse()}for((h=i.length)-(f=j.length)<0&&(f=h,d=j,j=i,i=d),c=0;f;)c=(i[--f]=i[f]+j[f]+c)/1e7|0,i[f]%=1e7;for(c&&(i.unshift(c),++e),h=i.length;0==i[--h];)i.pop();return b.d=i,b.e=e,di?dC(b,l):b}function dt(a,b,c){if(a!==~~a||a<b||a>c)throw Error(dk+a)}function du(a){var b,c,d,e=a.length-1,f="",g=a[0];if(e>0){for(f+=g,b=1;b<e;b++)(c=7-(d=a[b]+"").length)&&(f+=dz(c)),f+=d;(c=7-(d=(g=a[b])+"").length)&&(f+=dz(c))}else if(0===g)return"0";for(;g%10==0;)g/=10;return f+g}dr.absoluteValue=dr.abs=function(){var a=new this.constructor(this);return a.s&&(a.s=1),a},dr.comparedTo=dr.cmp=function(a){var b,c,d,e;if(a=new this.constructor(a),this.s!==a.s)return this.s||-a.s;if(this.e!==a.e)return this.e>a.e^this.s<0?1:-1;for(b=0,c=(d=this.d.length)<(e=a.d.length)?d:e;b<c;++b)if(this.d[b]!==a.d[b])return this.d[b]>a.d[b]^this.s<0?1:-1;return d===e?0:d>e^this.s<0?1:-1},dr.decimalPlaces=dr.dp=function(){var a=this.d.length-1,b=(a-this.e)*7;if(a=this.d[a])for(;a%10==0;a/=10)b--;return b<0?0:b},dr.dividedBy=dr.div=function(a){return dv(this,new this.constructor(a))},dr.dividedToIntegerBy=dr.idiv=function(a){var b=this.constructor;return dC(dv(this,new b(a),0,1),b.precision)},dr.equals=dr.eq=function(a){return!this.cmp(a)},dr.exponent=function(){return dx(this)},dr.greaterThan=dr.gt=function(a){return this.cmp(a)>0},dr.greaterThanOrEqualTo=dr.gte=function(a){return this.cmp(a)>=0},dr.isInteger=dr.isint=function(){return this.e>this.d.length-2},dr.isNegative=dr.isneg=function(){return this.s<0},dr.isPositive=dr.ispos=function(){return this.s>0},dr.isZero=function(){return 0===this.s},dr.lessThan=dr.lt=function(a){return 0>this.cmp(a)},dr.lessThanOrEqualTo=dr.lte=function(a){return 1>this.cmp(a)},dr.logarithm=dr.log=function(a){var b,c=this.constructor,d=c.precision,e=d+5;if(void 0===a)a=new c(10);else if((a=new c(a)).s<1||a.eq(dh))throw Error(dj+"NaN");if(this.s<1)throw Error(dj+(this.s?"NaN":"-Infinity"));return this.eq(dh)?new c(0):(di=!1,b=dv(dA(this,e),dA(a,e),e),di=!0,dC(b,d))},dr.minus=dr.sub=function(a){return a=new this.constructor(a),this.s==a.s?dD(this,a):ds(this,(a.s=-a.s,a))},dr.modulo=dr.mod=function(a){var b,c=this.constructor,d=c.precision;if(!(a=new c(a)).s)throw Error(dj+"NaN");return this.s?(di=!1,b=dv(this,a,0,1).times(a),di=!0,this.minus(b)):dC(new c(this),d)},dr.naturalExponential=dr.exp=function(){return dw(this)},dr.naturalLogarithm=dr.ln=function(){return dA(this)},dr.negated=dr.neg=function(){var a=new this.constructor(this);return a.s=-a.s||0,a},dr.plus=dr.add=function(a){return a=new this.constructor(a),this.s==a.s?ds(this,a):dD(this,(a.s=-a.s,a))},dr.precision=dr.sd=function(a){var b,c,d;if(void 0!==a&&!!a!==a&&1!==a&&0!==a)throw Error(dk+a);if(b=dx(this)+1,c=7*(d=this.d.length-1)+1,d=this.d[d]){for(;d%10==0;d/=10)c--;for(d=this.d[0];d>=10;d/=10)c++}return a&&b>c?b:c},dr.squareRoot=dr.sqrt=function(){var a,b,c,d,e,f,g,h=this.constructor;if(this.s<1){if(!this.s)return new h(0);throw Error(dj+"NaN")}for(a=dx(this),di=!1,0==(e=Math.sqrt(+this))||e==1/0?(((b=du(this.d)).length+a)%2==0&&(b+="0"),e=Math.sqrt(b),a=dm((a+1)/2)-(a<0||a%2),d=new h(b=e==1/0?"5e"+a:(b=e.toExponential()).slice(0,b.indexOf("e")+1)+a)):d=new h(e.toString()),e=g=(c=h.precision)+3;;)if(d=(f=d).plus(dv(this,f,g+2)).times(.5),du(f.d).slice(0,g)===(b=du(d.d)).slice(0,g)){if(b=b.slice(g-3,g+1),e==g&&"4999"==b){if(dC(f,c+1,0),f.times(f).eq(this)){d=f;break}}else if("9999"!=b)break;g+=4}return di=!0,dC(d,c)},dr.times=dr.mul=function(a){var b,c,d,e,f,g,h,i,j,k=this.constructor,l=this.d,m=(a=new k(a)).d;if(!this.s||!a.s)return new k(0);for(a.s*=this.s,c=this.e+a.e,(i=l.length)<(j=m.length)&&(f=l,l=m,m=f,g=i,i=j,j=g),f=[],d=g=i+j;d--;)f.push(0);for(d=j;--d>=0;){for(b=0,e=i+d;e>d;)h=f[e]+m[d]*l[e-d-1]+b,f[e--]=h%1e7|0,b=h/1e7|0;f[e]=(f[e]+b)%1e7|0}for(;!f[--g];)f.pop();return b?++c:f.shift(),a.d=f,a.e=c,di?dC(a,k.precision):a},dr.toDecimalPlaces=dr.todp=function(a,b){var c=this,d=c.constructor;return(c=new d(c),void 0===a)?c:(dt(a,0,1e9),void 0===b?b=d.rounding:dt(b,0,8),dC(c,a+dx(c)+1,b))},dr.toExponential=function(a,b){var c,d=this,e=d.constructor;return void 0===a?c=dE(d,!0):(dt(a,0,1e9),void 0===b?b=e.rounding:dt(b,0,8),c=dE(d=dC(new e(d),a+1,b),!0,a+1)),c},dr.toFixed=function(a,b){var c,d,e=this.constructor;return void 0===a?dE(this):(dt(a,0,1e9),void 0===b?b=e.rounding:dt(b,0,8),c=dE((d=dC(new e(this),a+dx(this)+1,b)).abs(),!1,a+dx(d)+1),this.isneg()&&!this.isZero()?"-"+c:c)},dr.toInteger=dr.toint=function(){var a=this.constructor;return dC(new a(this),dx(this)+1,a.rounding)},dr.toNumber=function(){return+this},dr.toPower=dr.pow=function(a){var b,c,d,e,f,g,h=this,i=h.constructor,j=+(a=new i(a));if(!a.s)return new i(dh);if(!(h=new i(h)).s){if(a.s<1)throw Error(dj+"Infinity");return h}if(h.eq(dh))return h;if(d=i.precision,a.eq(dh))return dC(h,d);if(g=(b=a.e)>=(c=a.d.length-1),f=h.s,g){if((c=j<0?-j:j)<=0x1fffffffffffff){for(e=new i(dh),b=Math.ceil(d/7+4),di=!1;c%2&&dF((e=e.times(h)).d,b),0!==(c=dm(c/2));)dF((h=h.times(h)).d,b);return di=!0,a.s<0?new i(dh).div(e):dC(e,d)}}else if(f<0)throw Error(dj+"NaN");return f=f<0&&1&a.d[Math.max(b,c)]?-1:1,h.s=1,di=!1,e=a.times(dA(h,d+12)),di=!0,(e=dw(e)).s=f,e},dr.toPrecision=function(a,b){var c,d,e=this,f=e.constructor;return void 0===a?(c=dx(e),d=dE(e,c<=f.toExpNeg||c>=f.toExpPos)):(dt(a,1,1e9),void 0===b?b=f.rounding:dt(b,0,8),c=dx(e=dC(new f(e),a,b)),d=dE(e,a<=c||c<=f.toExpNeg,a)),d},dr.toSignificantDigits=dr.tosd=function(a,b){var c=this.constructor;return void 0===a?(a=c.precision,b=c.rounding):(dt(a,1,1e9),void 0===b?b=c.rounding:dt(b,0,8)),dC(new c(this),a,b)},dr.toString=dr.valueOf=dr.val=dr.toJSON=dr[Symbol.for("nodejs.util.inspect.custom")]=function(){var a=dx(this),b=this.constructor;return dE(this,a<=b.toExpNeg||a>=b.toExpPos)};var dv=function(){function a(a,b){var c,d=0,e=a.length;for(a=a.slice();e--;)c=a[e]*b+d,a[e]=c%1e7|0,d=c/1e7|0;return d&&a.unshift(d),a}function b(a,b,c,d){var e,f;if(c!=d)f=c>d?1:-1;else for(e=f=0;e<c;e++)if(a[e]!=b[e]){f=a[e]>b[e]?1:-1;break}return f}function c(a,b,c){for(var d=0;c--;)a[c]-=d,d=+(a[c]<b[c]),a[c]=1e7*d+a[c]-b[c];for(;!a[0]&&a.length>1;)a.shift()}return function(d,e,f,g){var h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z=d.constructor,A=d.s==e.s?1:-1,B=d.d,C=e.d;if(!d.s)return new z(d);if(!e.s)throw Error(dj+"Division by zero");for(j=0,i=d.e-e.e,x=C.length,v=B.length,o=(n=new z(A)).d=[];C[j]==(B[j]||0);)++j;if(C[j]>(B[j]||0)&&--i,(s=null==f?f=z.precision:g?f+(dx(d)-dx(e))+1:f)<0)return new z(0);if(s=s/7+2|0,j=0,1==x)for(k=0,C=C[0],s++;(j<v||k)&&s--;j++)t=1e7*k+(B[j]||0),o[j]=t/C|0,k=t%C|0;else{for((k=1e7/(C[0]+1)|0)>1&&(C=a(C,k),B=a(B,k),x=C.length,v=B.length),u=x,q=(p=B.slice(0,x)).length;q<x;)p[q++]=0;(y=C.slice()).unshift(0),w=C[0],C[1]>=1e7/2&&++w;do k=0,(h=b(C,p,x,q))<0?(r=p[0],x!=q&&(r=1e7*r+(p[1]||0)),(k=r/w|0)>1?(k>=1e7&&(k=1e7-1),m=(l=a(C,k)).length,q=p.length,1==(h=b(l,p,m,q))&&(k--,c(l,x<m?y:C,m))):(0==k&&(h=k=1),l=C.slice()),(m=l.length)<q&&l.unshift(0),c(p,l,q),-1==h&&(q=p.length,(h=b(C,p,x,q))<1&&(k++,c(p,x<q?y:C,q))),q=p.length):0===h&&(k++,p=[0]),o[j++]=k,h&&p[0]?p[q++]=B[u]||0:(p=[B[u]],q=1);while((u++<v||void 0!==p[0])&&s--)}return o[0]||o.shift(),n.e=i,dC(n,g?f+dx(n)+1:f)}}();function dw(a,b){var c,d,e,f,g,h=0,i=0,j=a.constructor,k=j.precision;if(dx(a)>16)throw Error(dl+dx(a));if(!a.s)return new j(dh);for(null==b?(di=!1,g=k):g=b,f=new j(.03125);a.abs().gte(.1);)a=a.times(f),i+=5;for(g+=Math.log(dn(2,i))/Math.LN10*2+5|0,c=d=e=new j(dh),j.precision=g;;){if(d=dC(d.times(a),g),c=c.times(++h),du((f=e.plus(dv(d,c,g))).d).slice(0,g)===du(e.d).slice(0,g)){for(;i--;)e=dC(e.times(e),g);return j.precision=k,null==b?(di=!0,dC(e,k)):e}e=f}}function dx(a){for(var b=7*a.e,c=a.d[0];c>=10;c/=10)b++;return b}function dy(a,b,c){if(b>a.LN10.sd())throw di=!0,c&&(a.precision=c),Error(dj+"LN10 precision limit exceeded");return dC(new a(a.LN10),b)}function dz(a){for(var b="";a--;)b+="0";return b}function dA(a,b){var c,d,e,f,g,h,i,j,k,l=1,m=a,n=m.d,o=m.constructor,p=o.precision;if(m.s<1)throw Error(dj+(m.s?"NaN":"-Infinity"));if(m.eq(dh))return new o(0);if(null==b?(di=!1,j=p):j=b,m.eq(10))return null==b&&(di=!0),dy(o,j);if(o.precision=j+=10,d=(c=du(n)).charAt(0),!(15e14>Math.abs(f=dx(m))))return i=dy(o,j+2,p).times(f+""),m=dA(new o(d+"."+c.slice(1)),j-10).plus(i),o.precision=p,null==b?(di=!0,dC(m,p)):m;for(;d<7&&1!=d||1==d&&c.charAt(1)>3;)d=(c=du((m=m.times(a)).d)).charAt(0),l++;for(f=dx(m),d>1?(m=new o("0."+c),f++):m=new o(d+"."+c.slice(1)),h=g=m=dv(m.minus(dh),m.plus(dh),j),k=dC(m.times(m),j),e=3;;){if(g=dC(g.times(k),j),du((i=h.plus(dv(g,new o(e),j))).d).slice(0,j)===du(h.d).slice(0,j))return h=h.times(2),0!==f&&(h=h.plus(dy(o,j+2,p).times(f+""))),h=dv(h,new o(l),j),o.precision=p,null==b?(di=!0,dC(h,p)):h;h=i,e+=2}}function dB(a,b){var c,d,e;for((c=b.indexOf("."))>-1&&(b=b.replace(".","")),(d=b.search(/e/i))>0?(c<0&&(c=d),c+=+b.slice(d+1),b=b.substring(0,d)):c<0&&(c=b.length),d=0;48===b.charCodeAt(d);)++d;for(e=b.length;48===b.charCodeAt(e-1);)--e;if(b=b.slice(d,e)){if(e-=d,a.e=dm((c=c-d-1)/7),a.d=[],d=(c+1)%7,c<0&&(d+=7),d<e){for(d&&a.d.push(+b.slice(0,d)),e-=7;d<e;)a.d.push(+b.slice(d,d+=7));d=7-(b=b.slice(d)).length}else d-=e;for(;d--;)b+="0";if(a.d.push(+b),di&&(a.e>dq||a.e<-dq))throw Error(dl+c)}else a.s=0,a.e=0,a.d=[0];return a}function dC(a,b,c){var d,e,f,g,h,i,j,k,l=a.d;for(g=1,f=l[0];f>=10;f/=10)g++;if((d=b-g)<0)d+=7,e=b,j=l[k=0];else{if((k=Math.ceil((d+1)/7))>=(f=l.length))return a;for(g=1,j=f=l[k];f>=10;f/=10)g++;d%=7,e=d-7+g}if(void 0!==c&&(h=j/(f=dn(10,g-e-1))%10|0,i=b<0||void 0!==l[k+1]||j%f,i=c<4?(h||i)&&(0==c||c==(a.s<0?3:2)):h>5||5==h&&(4==c||i||6==c&&(d>0?e>0?j/dn(10,g-e):0:l[k-1])%10&1||c==(a.s<0?8:7))),b<1||!l[0])return i?(f=dx(a),l.length=1,b=b-f-1,l[0]=dn(10,(7-b%7)%7),a.e=dm(-b/7)||0):(l.length=1,l[0]=a.e=a.s=0),a;if(0==d?(l.length=k,f=1,k--):(l.length=k+1,f=dn(10,7-d),l[k]=e>0?(j/dn(10,g-e)%dn(10,e)|0)*f:0),i)for(;;)if(0==k){1e7==(l[0]+=f)&&(l[0]=1,++a.e);break}else{if(l[k]+=f,1e7!=l[k])break;l[k--]=0,f=1}for(d=l.length;0===l[--d];)l.pop();if(di&&(a.e>dq||a.e<-dq))throw Error(dl+dx(a));return a}function dD(a,b){var c,d,e,f,g,h,i,j,k,l,m=a.constructor,n=m.precision;if(!a.s||!b.s)return b.s?b.s=-b.s:b=new m(a),di?dC(b,n):b;if(i=a.d,l=b.d,d=b.e,j=a.e,i=i.slice(),g=j-d){for((k=g<0)?(c=i,g=-g,h=l.length):(c=l,d=j,h=i.length),g>(e=Math.max(Math.ceil(n/7),h)+2)&&(g=e,c.length=1),c.reverse(),e=g;e--;)c.push(0);c.reverse()}else{for((k=(e=i.length)<(h=l.length))&&(h=e),e=0;e<h;e++)if(i[e]!=l[e]){k=i[e]<l[e];break}g=0}for(k&&(c=i,i=l,l=c,b.s=-b.s),h=i.length,e=l.length-h;e>0;--e)i[h++]=0;for(e=l.length;e>g;){if(i[--e]<l[e]){for(f=e;f&&0===i[--f];)i[f]=1e7-1;--i[f],i[e]+=1e7}i[e]-=l[e]}for(;0===i[--h];)i.pop();for(;0===i[0];i.shift())--d;return i[0]?(b.d=i,b.e=d,di?dC(b,n):b):new m(0)}function dE(a,b,c){var d,e=dx(a),f=du(a.d),g=f.length;return b?(c&&(d=c-g)>0?f=f.charAt(0)+"."+f.slice(1)+dz(d):g>1&&(f=f.charAt(0)+"."+f.slice(1)),f=f+(e<0?"e":"e+")+e):e<0?(f="0."+dz(-e-1)+f,c&&(d=c-g)>0&&(f+=dz(d))):e>=g?(f+=dz(e+1-g),c&&(d=c-e-1)>0&&(f=f+"."+dz(d))):((d=e+1)<g&&(f=f.slice(0,d)+"."+f.slice(d)),c&&(d=c-g)>0&&(e+1===g&&(f+="."),f+=dz(d))),a.s<0?"-"+f:f}function dF(a,b){if(a.length>b)return a.length=b,!0}function dG(a){if(!a||"object"!=typeof a)throw Error(dj+"Object expected");var b,c,d,e=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(b=0;b<e.length;b+=3)if(void 0!==(d=a[c=e[b]]))if(dm(d)===d&&d>=e[b+1]&&d<=e[b+2])this[c]=d;else throw Error(dk+c+": "+d);if(void 0!==(d=a[c="LN10"]))if(d==Math.LN10)this[c]=new this(d);else throw Error(dk+c+": "+d);return this}var dg=function a(b){var c,d,e;function f(a){if(!(this instanceof f))return new f(a);if(this.constructor=f,a instanceof f){this.s=a.s,this.e=a.e,this.d=(a=a.d)?a.slice():a;return}if("number"==typeof a){if(0*a!=0)throw Error(dk+a);if(a>0)this.s=1;else if(a<0)a=-a,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(a===~~a&&a<1e7){this.e=0,this.d=[a];return}return dB(this,a.toString())}if("string"!=typeof a)throw Error(dk+a);if(45===a.charCodeAt(0)?(a=a.slice(1),this.s=-1):this.s=1,dp.test(a))dB(this,a);else throw Error(dk+a)}if(f.prototype=dr,f.ROUND_UP=0,f.ROUND_DOWN=1,f.ROUND_CEIL=2,f.ROUND_FLOOR=3,f.ROUND_HALF_UP=4,f.ROUND_HALF_DOWN=5,f.ROUND_HALF_EVEN=6,f.ROUND_HALF_CEIL=7,f.ROUND_HALF_FLOOR=8,f.clone=a,f.config=f.set=dG,void 0===b&&(b={}),b)for(c=0,e=["precision","rounding","toExpNeg","toExpPos","LN10"];c<e.length;)b.hasOwnProperty(d=e[c++])||(b[d]=this[d]);return f.config(b),f}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});dh=new dg(1);let dH=dg;var dI=a=>a,dJ={},dK=a=>function b(){let c;return 0==arguments.length||1==arguments.length&&(c=arguments.length<=0?void 0:arguments[0],c===dJ)?b:a(...arguments)},dL=(a,b)=>1===a?b:dK(function(){for(var c=arguments.length,d=Array(c),e=0;e<c;e++)d[e]=arguments[e];var f=d.filter(a=>a!==dJ).length;return f>=a?b(...d):dL(a-f,dK(function(){for(var a=arguments.length,c=Array(a),e=0;e<a;e++)c[e]=arguments[e];return b(...d.map(a=>a===dJ?c.shift():a),...c)}))}),dM=a=>dL(a.length,a),dN=(a,b)=>{for(var c=[],d=a;d<b;++d)c[d-a]=d;return c},dO=dM((a,b)=>Array.isArray(b)?b.map(a):Object.keys(b).map(a=>b[a]).map(a)),dP=function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];if(!b.length)return dI;var d=b.reverse(),e=d[0],f=d.slice(1);return function(){return f.reduce((a,b)=>b(a),e(...arguments))}},dQ=a=>Array.isArray(a)?a.reverse():a.split("").reverse().join(""),dR=a=>{var b=null,c=null;return function(){for(var d=arguments.length,e=Array(d),f=0;f<d;f++)e[f]=arguments[f];return b&&e.every((a,c)=>{var d;return a===(null==(d=b)?void 0:d[c])})?c:(b=e,c=a(...e))}};function dS(a){return 0===a?1:Math.floor(new dH(a).abs().log(10).toNumber())+1}function dT(a,b,c){for(var d=new dH(a),e=0,f=[];d.lt(b)&&e<1e5;)f.push(d.toNumber()),d=d.add(c),e++;return f}dM((a,b,c)=>{var d=+a;return d+c*(b-d)}),dM((a,b,c)=>{var d=b-a;return(c-a)/(d=d||1/0)}),dM((a,b,c)=>{var d=b-a;return Math.max(0,Math.min(1,(c-a)/(d=d||1/0)))});var dU=a=>{var[b,c]=a,[d,e]=[b,c];return b>c&&([d,e]=[c,b]),[d,e]},dV=(a,b,c)=>{if(a.lte(0))return new dH(0);var d=dS(a.toNumber()),e=new dH(10).pow(d),f=a.div(e),g=1!==d?.05:.1,h=new dH(Math.ceil(f.div(g).toNumber())).add(c).mul(g).mul(e);return new dH(b?h.toNumber():Math.ceil(h.toNumber()))},dW=function(a,b,c,d){var e,f=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((b-a)/(c-1)))return{step:new dH(0),tickMin:new dH(0),tickMax:new dH(0)};var g=dV(new dH(b).sub(a).div(c-1),d,f),h=Math.ceil((e=a<=0&&b>=0?new dH(0):(e=new dH(a).add(b).div(2)).sub(new dH(e).mod(g))).sub(a).div(g).toNumber()),i=Math.ceil(new dH(b).sub(e).div(g).toNumber()),j=h+i+1;return j>c?dW(a,b,c,d,f+1):(j<c&&(i=b>0?i+(c-j):i,h=b>0?h:h+(c-j)),{step:g,tickMin:e.sub(new dH(h).mul(g)),tickMax:e.add(new dH(i).mul(g))})},dX=dR(function(a){var[b,c]=a,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,e=!(arguments.length>2)||void 0===arguments[2]||arguments[2],f=Math.max(d,2),[g,h]=dU([b,c]);if(g===-1/0||h===1/0){var i=h===1/0?[g,...dN(0,d-1).map(()=>1/0)]:[...dN(0,d-1).map(()=>-1/0),h];return b>c?dQ(i):i}if(g===h){var j=new dH(1),k=new dH(g);if(!k.isint()&&e){var l=Math.abs(g);l<1?(j=new dH(10).pow(dS(g)-1),k=new dH(Math.floor(k.div(j).toNumber())).mul(j)):l>1&&(k=new dH(Math.floor(g)))}else 0===g?k=new dH(Math.floor((d-1)/2)):e||(k=new dH(Math.floor(g)));var m=Math.floor((d-1)/2);return dP(dO(a=>k.add(new dH(a-m).mul(j)).toNumber()),dN)(0,d)}var{step:n,tickMin:o,tickMax:p}=dW(g,h,f,e,0),q=dT(o,p.add(new dH(.1).mul(n)),n);return b>c?dQ(q):q}),dY=dR(function(a,b){var[c,d]=a,e=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[f,g]=dU([c,d]);if(f===-1/0||g===1/0)return[c,d];if(f===g)return[f];var h=Math.max(b,2),i=dV(new dH(g).sub(f).div(h-1),e,0),j=[...dT(new dH(f),new dH(g),i),g];return!1===e&&(j=j.map(a=>Math.round(a))),c>d?dQ(j):j}),dZ=c(35268),d$=c(21581),d_=c(92173),d0=c(86941),d1=c(14851),d2=c(65168),d3=c(39683),d4=c(25610),d5=c(44611),d6=c(71156),d7=c(75309),d8=c(25401),d9=c(76495),ea=c(61711);function eb(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function ec(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?eb(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):eb(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var ed=[0,"auto"],ee={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},ef=(a,b)=>{var c=a.cartesianAxis.xAxis[b];return null==c?ee:c},eg={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:ed,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:d6.tQ},eh=(a,b)=>{var c=a.cartesianAxis.yAxis[b];return null==c?eg:c},ei={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},ej=(a,b)=>{var c=a.cartesianAxis.zAxis[b];return null==c?ei:c},ek=(a,b,c)=>{switch(b){case"xAxis":return ef(a,c);case"yAxis":return eh(a,c);case"zAxis":return ej(a,c);case"angleAxis":return(0,d2.Be)(a,c);case"radiusAxis":return(0,d2.Gl)(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},el=(a,b,c)=>{switch(b){case"xAxis":return ef(a,c);case"yAxis":return eh(a,c);case"angleAxis":return(0,d2.Be)(a,c);case"radiusAxis":return(0,d2.Gl)(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},em=a=>a.graphicalItems.cartesianItems.some(a=>"bar"===a.type)||a.graphicalItems.polarItems.some(a=>"radialBar"===a.type);function en(a,b){return c=>{switch(a){case"xAxis":return"xAxisId"in c&&c.xAxisId===b;case"yAxis":return"yAxisId"in c&&c.yAxisId===b;case"zAxis":return"zAxisId"in c&&c.zAxisId===b;case"angleAxis":return"angleAxisId"in c&&c.angleAxisId===b;case"radiusAxis":return"radiusAxisId"in c&&c.radiusAxisId===b;default:return!1}}}var eo=a=>a.graphicalItems.cartesianItems,ep=(0,e.Mz)([d3.N,d4.E],en),eq=(a,b,c)=>a.filter(c).filter(a=>(null==b?void 0:b.includeHidden)===!0||!a.hide),er=(0,e.Mz)([eo,ek,ep],eq),es=(0,e.Mz)([er],a=>a.filter(a=>"area"===a.type||"bar"===a.type).filter(ea.g)),et=a=>a.filter(a=>!("stackId"in a)||void 0===a.stackId),eu=(0,e.Mz)([er],et),ev=a=>a.map(a=>a.data).filter(Boolean).flat(1),ew=(0,e.Mz)([er],ev),ex=(a,b)=>{var{chartData:c=[],dataStartIndex:d,dataEndIndex:e}=b;return a.length>0?a:c.slice(d,e+1)},ey=(0,e.Mz)([ew,c4.HS],ex),ez=(a,b,c)=>(null==b?void 0:b.dataKey)!=null?a.map(a=>({value:(0,c3.kr)(a,b.dataKey)})):c.length>0?c.map(a=>a.dataKey).flatMap(b=>a.map(a=>({value:(0,c3.kr)(a,b)}))):a.map(a=>({value:a})),eA=(0,e.Mz)([ey,ek,er],ez);function eB(a,b){switch(a){case"xAxis":return"x"===b.direction;case"yAxis":return"y"===b.direction;default:return!1}}function eC(a){return a.filter(a=>(0,c5.vh)(a)||a instanceof Date).map(Number).filter(a=>!1===(0,c5.M8)(a))}var eD=(0,e.Mz)([es,c4.HS,d8.D],d9.A),eE=(a,b,c)=>Object.fromEntries(Object.entries(b.reduce((a,b)=>(null==b.stackId||(null==a[b.stackId]&&(a[b.stackId]=[]),a[b.stackId].push(b)),a),{})).map(b=>{var[d,e]=b,f=e.map(d7.x);return[d,{stackedData:(0,c3.yy)(a,f,c),graphicalItems:e}]})),eF=(0,e.Mz)([eD,es,d1.eC],eE),eG=(a,b,c)=>{var{dataStartIndex:d,dataEndIndex:e}=b;if("zAxis"!==c){var f=(0,c3.Mk)(a,d,e);if(null==f||0!==f[0]||0!==f[1])return f}},eH=(0,e.Mz)([eF,c4.LF,d3.N],eG),eI=(a,b,c,d,e)=>c.length>0?a.flatMap(a=>c.flatMap(c=>{var f,g,h=null==(f=d[c.id])?void 0:f.filter(a=>eB(e,a)),i=(0,c3.kr)(a,null!=(g=b.dataKey)?g:c.dataKey);return{value:i,errorDomain:function(a,b,c){return!c||"number"!=typeof b||(0,c5.M8)(b)||!c.length?[]:eC(c.flatMap(c=>{var d,e,f=(0,c3.kr)(a,c.dataKey);if(Array.isArray(f)?[d,e]=f:d=e=f,(0,c6.H)(d)&&(0,c6.H)(e))return[b-d,b+e]}))}(a,i,h)}})).filter(Boolean):(null==b?void 0:b.dataKey)!=null?a.map(a=>({value:(0,c3.kr)(a,b.dataKey),errorDomain:[]})):a.map(a=>({value:a,errorDomain:[]})),eJ=a=>a.errorBars,eK=(a,b,c)=>a.flatMap(a=>b[a.id]).filter(Boolean).filter(a=>eB(c,a));(0,e.Mz)([eu,eJ,d3.N],eK);var eL=(0,e.Mz)([ey,ek,eu,eJ,d3.N],eI);function eM(a){var{value:b}=a;if((0,c5.vh)(b)||b instanceof Date)return b}var eN=a=>{var b=eC(a.flatMap(a=>[a.value,a.errorDomain]).flat(1));if(0!==b.length)return[Math.min(...b),Math.max(...b)]},eO=a=>{var b;if(null==a||!("domain"in a))return ed;if(null!=a.domain)return a.domain;if(null!=a.ticks){if("number"===a.type){var c=eC(a.ticks);return[Math.min(...c),Math.max(...c)]}if("category"===a.type)return a.ticks.map(String)}return null!=(b=null==a?void 0:a.domain)?b:ed},eP=function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];var d=b.filter(Boolean);if(0!==d.length){var e=d.flat();return[Math.min(...e),Math.max(...e)]}},eQ=a=>a.referenceElements.dots,eR=(a,b,c)=>a.filter(a=>"extendDomain"===a.ifOverflow).filter(a=>"xAxis"===b?a.xAxisId===c:a.yAxisId===c),eS=(0,e.Mz)([eQ,d3.N,d4.E],eR),eT=a=>a.referenceElements.areas,eU=(0,e.Mz)([eT,d3.N,d4.E],eR),eV=a=>a.referenceElements.lines,eW=(0,e.Mz)([eV,d3.N,d4.E],eR),eX=(a,b)=>{var c=eC(a.map(a=>"xAxis"===b?a.x:a.y));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},eY=(0,e.Mz)(eS,d3.N,eX),eZ=(a,b)=>{var c=eC(a.flatMap(a=>["xAxis"===b?a.x1:a.y1,"xAxis"===b?a.x2:a.y2]));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},e$=(0,e.Mz)([eU,d3.N],eZ),e_=(a,b)=>{var c=eC(a.map(a=>"xAxis"===b?a.x:a.y));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},e0=(0,e.Mz)(eW,d3.N,e_),e1=(0,e.Mz)(eY,e0,e$,(a,b,c)=>eP(a,c,b)),e2=(0,e.Mz)([ek],eO),e3=(a,b,c,d,e,f,g)=>{var h=function(a,b){if(b&&"function"!=typeof a&&Array.isArray(a)&&2===a.length){var c,d,[e,f]=a;if((0,c6.H)(e))c=e;else if("function"==typeof e)return;if((0,c6.H)(f))d=f;else if("function"==typeof f)return;var g=[c,d];if(c7(g))return g}}(b,a.allowDataOverflow);return null!=h?h:function(a,b,c){if(c||null!=b){if("function"==typeof a&&null!=b)try{var d=a(b,c);if(c7(d))return c8(d,b,c)}catch(a){}if(Array.isArray(a)&&2===a.length){var e,f,[g,h]=a;if("auto"===g)null!=b&&(e=Math.min(...b));else if((0,c5.Et)(g))e=g;else if("function"==typeof g)try{null!=b&&(e=g(null==b?void 0:b[0]))}catch(a){}else if("string"==typeof g&&c3.IH.test(g)){var i=c3.IH.exec(g);if(null==i||null==b)e=void 0;else{var j=+i[1];e=b[0]-j}}else e=null==b?void 0:b[0];if("auto"===h)null!=b&&(f=Math.max(...b));else if((0,c5.Et)(h))f=h;else if("function"==typeof h)try{null!=b&&(f=h(null==b?void 0:b[1]))}catch(a){}else if("string"==typeof h&&c3.qx.test(h)){var k=c3.qx.exec(h);if(null==k||null==b)f=void 0;else{var l=+k[1];f=b[1]+l}}else f=null==b?void 0:b[1];var m=[e,f];if(c7(m))return null==b?m:c8(m,b,c)}}}(b,"vertical"===f&&"xAxis"===g||"horizontal"===f&&"yAxis"===g?eP(c,e,eN(d)):eP(e,eN(d)),a.allowDataOverflow)},e4=(0,e.Mz)([ek,e2,eH,eL,e1,c2.fz,d3.N],e3),e5=[0,1],e6=(a,b,c,d,e,f,h)=>{if(null!=a&&null!=c&&0!==c.length||void 0!==h){var{dataKey:i,type:j}=a,k=(0,c3._L)(b,f);return k&&null==i?g()(0,c.length):"category"===j?((a,b,c)=>{var d=a.map(eM).filter(a=>null!=a);return c&&(null==b.dataKey||b.allowDuplicatedCategory&&(0,c5.CG)(d))?g()(0,a.length):b.allowDuplicatedCategory?d:Array.from(new Set(d))})(d,a,k):"expand"===e?e5:h}},e7=(0,e.Mz)([ek,c2.fz,ey,eA,d1.eC,d3.N,e4],e6),e8=(a,b,c,e,f)=>{if(null!=a){var{scale:g,type:h}=a;if("auto"===g)return"radial"===b&&"radiusAxis"===f?"band":"radial"===b&&"angleAxis"===f?"linear":"category"===h&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!c)?"point":"category"===h?"band":"linear";if("string"==typeof g){var i="scale".concat((0,c5.Zb)(g));return i in d?i:"point"}}},e9=(0,e.Mz)([ek,c2.fz,em,d1.iO,d3.N],e8);function fa(a,b,c,e){if(null!=c&&null!=e){if("function"==typeof a.scale)return a.scale.copy().domain(c).range(e);var f=function(a){if(null!=a){if(a in d)return d[a]();var b="scale".concat((0,c5.Zb)(a));if(b in d)return d[b]()}}(b);if(null!=f){var g=f.domain(c).range(e);return(0,c3.YB)(g),g}}}var fb=(a,b,c)=>{var d=eO(b);if("auto"===c||"linear"===c){if(null!=b&&b.tickCount&&Array.isArray(d)&&("auto"===d[0]||"auto"===d[1])&&c7(a))return dX(a,b.tickCount,b.allowDecimals);if(null!=b&&b.tickCount&&"number"===b.type&&c7(a))return dY(a,b.tickCount,b.allowDecimals)}},fc=(0,e.Mz)([e7,el,e9],fb),fd=(a,b,c,d)=>"angleAxis"!==d&&(null==a?void 0:a.type)==="number"&&c7(b)&&Array.isArray(c)&&c.length>0?[Math.min(b[0],c[0]),Math.max(b[1],c[c.length-1])]:b,fe=(0,e.Mz)([ek,e7,fc,d3.N],fd),ff=(0,e.Mz)(eA,ek,(a,b)=>{if(b&&"number"===b.type){var c=1/0,d=Array.from(eC(a.map(a=>a.value))).sort((a,b)=>a-b);if(d.length<2)return 1/0;var e=d[d.length-1]-d[0];if(0===e)return 1/0;for(var f=0;f<d.length-1;f++)c=Math.min(c,d[f+1]-d[f]);return c/e}}),fg=(0,e.Mz)(ff,c2.fz,d1.gY,d_.HZ,(a,b,c,d)=>d,(a,b,c,d,e)=>{if(!(0,c6.H)(a))return 0;var f="vertical"===b?d.height:d.width;if("gap"===e)return a*f/2;if("no-gap"===e){var g=(0,c5.F4)(c,a*f),h=a*f/2;return h-g-(h-g)/f*g}return 0}),fh=(0,e.Mz)(ef,(a,b)=>{var c=ef(a,b);return null==c||"string"!=typeof c.padding?0:fg(a,"xAxis",b,c.padding)},(a,b)=>{if(null==a)return{left:0,right:0};var c,d,{padding:e}=a;return"string"==typeof e?{left:b,right:b}:{left:(null!=(c=e.left)?c:0)+b,right:(null!=(d=e.right)?d:0)+b}}),fi=(0,e.Mz)(eh,(a,b)=>{var c=eh(a,b);return null==c||"string"!=typeof c.padding?0:fg(a,"yAxis",b,c.padding)},(a,b)=>{if(null==a)return{top:0,bottom:0};var c,d,{padding:e}=a;return"string"==typeof e?{top:b,bottom:b}:{top:(null!=(c=e.top)?c:0)+b,bottom:(null!=(d=e.bottom)?d:0)+b}}),fj=(0,e.Mz)([d_.HZ,fh,d0.U,d0.C,(a,b,c)=>c],(a,b,c,d,e)=>{var{padding:f}=d;return e?[f.left,c.width-f.right]:[a.left+b.left,a.left+a.width-b.right]}),fk=(0,e.Mz)([d_.HZ,c2.fz,fi,d0.U,d0.C,(a,b,c)=>c],(a,b,c,d,e,f)=>{var{padding:g}=e;return f?[d.height-g.bottom,g.top]:"horizontal"===b?[a.top+a.height-c.bottom,a.top+c.top]:[a.top+c.top,a.top+a.height-c.bottom]}),fl=(a,b,c,d)=>{var e;switch(b){case"xAxis":return fj(a,c,d);case"yAxis":return fk(a,c,d);case"zAxis":return null==(e=ej(a,c))?void 0:e.range;case"angleAxis":return(0,d2.Cv)(a);case"radiusAxis":return(0,d2.Dc)(a,c);default:return}},fm=(0,e.Mz)([ek,fl],d5.I),fn=(0,e.Mz)([ek,e9,fe,fm],fa);function fo(a,b){return a.id<b.id?-1:+(a.id>b.id)}(0,e.Mz)([er,eJ,d3.N],eK);var fp=(a,b)=>b,fq=(a,b,c)=>c,fr=(0,e.Mz)(d$.h,fp,fq,(a,b,c)=>a.filter(a=>a.orientation===b).filter(a=>a.mirror===c).sort(fo)),fs=(0,e.Mz)(d$.W,fp,fq,(a,b,c)=>a.filter(a=>a.orientation===b).filter(a=>a.mirror===c).sort(fo)),ft=(a,b)=>({width:a.width,height:b.height}),fu=(0,e.Mz)(d_.HZ,ef,ft),fv=(0,e.Mz)(dZ.A$,d_.HZ,fr,fp,fq,(a,b,c,d,e)=>{var f,g={};return c.forEach(c=>{var h=ft(b,c);null==f&&(f=((a,b,c)=>{switch(b){case"top":return a.top;case"bottom":return c-a.bottom;default:return 0}})(b,d,a));var i="top"===d&&!e||"bottom"===d&&e;g[c.id]=f-Number(i)*h.height,f+=(i?-1:1)*h.height}),g}),fw=(0,e.Mz)(dZ.Lp,d_.HZ,fs,fp,fq,(a,b,c,d,e)=>{var f,g={};return c.forEach(c=>{var h=((a,b)=>({width:"number"==typeof b.width?b.width:d6.tQ,height:a.height}))(b,c);null==f&&(f=((a,b,c)=>{switch(b){case"left":return a.left;case"right":return c-a.right;default:return 0}})(b,d,a));var i="left"===d&&!e||"right"===d&&e;g[c.id]=f-Number(i)*h.width,f+=(i?-1:1)*h.width}),g}),fx=(a,b)=>{var c=(0,d_.HZ)(a),d=ef(a,b);if(null!=d){var e=fv(a,d.orientation,d.mirror)[b];return null==e?{x:c.left,y:0}:{x:c.left,y:e}}},fy=(a,b)=>{var c=(0,d_.HZ)(a),d=eh(a,b);if(null!=d){var e=fw(a,d.orientation,d.mirror)[b];return null==e?{x:0,y:c.top}:{x:e,y:c.top}}},fz=(0,e.Mz)(d_.HZ,eh,(a,b)=>({width:"number"==typeof b.width?b.width:d6.tQ,height:a.height})),fA=(a,b,c)=>{switch(b){case"xAxis":return fu(a,c).width;case"yAxis":return fz(a,c).height;default:return}},fB=(a,b,c,d)=>{if(null!=c){var{allowDuplicatedCategory:e,type:f,dataKey:g}=c,h=(0,c3._L)(a,d),i=b.map(a=>a.value);if(g&&h&&"category"===f&&e&&(0,c5.CG)(i))return i}},fC=(0,e.Mz)([c2.fz,eA,ek,d3.N],fB),fD=(a,b,c,d)=>{if(null!=c&&null!=c.dataKey){var{type:e,scale:f}=c;if((0,c3._L)(a,d)&&("number"===e||"auto"!==f))return b.map(a=>a.value)}},fE=(0,e.Mz)([c2.fz,eA,el,d3.N],fD),fF=(0,e.Mz)([c2.fz,(a,b,c)=>{switch(b){case"xAxis":return ef(a,c);case"yAxis":return eh(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},e9,fn,fC,fE,fl,fc,d3.N],(a,b,c,d,e,f,g,h,i)=>{if(null==b)return null;var j=(0,c3._L)(a,i);return{angle:b.angle,interval:b.interval,minTickGap:b.minTickGap,orientation:b.orientation,tick:b.tick,tickCount:b.tickCount,tickFormatter:b.tickFormatter,ticks:b.ticks,type:b.type,unit:b.unit,axisType:i,categoricalDomain:f,duplicateDomain:e,isCategorical:j,niceTicks:h,range:g,realScaleType:c,scale:d}}),fG=(0,e.Mz)([c2.fz,el,e9,fn,fc,fl,fC,fE,d3.N],(a,b,c,d,e,f,g,h,i)=>{if(null!=b&&null!=d){var j=(0,c3._L)(a,i),{type:k,ticks:l,tickCount:m}=b,n="scaleBand"===c&&"function"==typeof d.bandwidth?d.bandwidth()/2:2,o="category"===k&&d.bandwidth?d.bandwidth()/n:0;o="angleAxis"===i&&null!=f&&f.length>=2?2*(0,c5.sA)(f[0]-f[1])*o:o;var p=l||e;return p?p.map((a,b)=>({index:b,coordinate:d(g?g.indexOf(a):a)+o,value:a,offset:o})).filter(a=>!(0,c5.M8)(a.coordinate)):j&&h?h.map((a,b)=>({coordinate:d(a)+o,value:a,index:b,offset:o})):d.ticks?d.ticks(m).map(a=>({coordinate:d(a)+o,value:a,offset:o})):d.domain().map((a,b)=>({coordinate:d(a)+o,value:g?g[a]:a,index:b,offset:o}))}}),fH=(0,e.Mz)([c2.fz,el,fn,fl,fC,fE,d3.N],(a,b,c,d,e,f,g)=>{if(null!=b&&null!=c&&null!=d&&d[0]!==d[1]){var h=(0,c3._L)(a,g),{tickCount:i}=b,j=0;return(j="angleAxis"===g&&(null==d?void 0:d.length)>=2?2*(0,c5.sA)(d[0]-d[1])*j:j,h&&f)?f.map((a,b)=>({coordinate:c(a)+j,value:a,index:b,offset:j})):c.ticks?c.ticks(i).map(a=>({coordinate:c(a)+j,value:a,offset:j})):c.domain().map((a,b)=>({coordinate:c(a)+j,value:e?e[a]:a,index:b,offset:j}))}}),fI=(0,e.Mz)(ek,fn,(a,b)=>{if(null!=a&&null!=b)return ec(ec({},a),{},{scale:b})}),fJ=(0,e.Mz)([ek,e9,e7,fm],fa);(0,e.Mz)((a,b,c)=>ej(a,c),fJ,(a,b)=>{if(null!=a&&null!=b)return ec(ec({},a),{},{scale:b})});var fK=(0,e.Mz)([c2.fz,d$.h,d$.W],(a,b,c)=>{switch(a){case"horizontal":return b.some(a=>a.reversed)?"right-to-left":"left-to-right";case"vertical":return c.some(a=>a.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}})},60735:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(71016);b.throttle=function(a,b=0,c={}){let{leading:e=!0,trailing:f=!0}=c;return d.debounce(a,b,{leading:e,maxWait:b,trailing:f})}},61185:(a,b,c)=>{"use strict";c.d(b,{As:()=>j,TK:()=>k,Vi:()=>i,ZF:()=>h,g5:()=>g,iZ:()=>l});var d=c(53968),e=c(72001),f=(0,d.Z0)({name:"graphicalItems",initialState:{cartesianItems:[],polarItems:[]},reducers:{addCartesianGraphicalItem(a,b){a.cartesianItems.push((0,e.h4)(b.payload))},replaceCartesianGraphicalItem(a,b){var{prev:c,next:d}=b.payload,f=(0,e.ss)(a).cartesianItems.indexOf((0,e.h4)(c));f>-1&&(a.cartesianItems[f]=(0,e.h4)(d))},removeCartesianGraphicalItem(a,b){var c=(0,e.ss)(a).cartesianItems.indexOf((0,e.h4)(b.payload));c>-1&&a.cartesianItems.splice(c,1)},addPolarGraphicalItem(a,b){a.polarItems.push((0,e.h4)(b.payload))},removePolarGraphicalItem(a,b){var c=(0,e.ss)(a).polarItems.indexOf((0,e.h4)(b.payload));c>-1&&a.polarItems.splice(c,1)}}}),{addCartesianGraphicalItem:g,replaceCartesianGraphicalItem:h,removeCartesianGraphicalItem:i,addPolarGraphicalItem:j,removePolarGraphicalItem:k}=f.actions,l=f.reducer},61188:(a,b,c)=>{"use strict";c.d(b,{VU:()=>g,XC:()=>i,_U:()=>h});var d=c(38301),e=c(31209),f=["points","pathLength"],g={svg:["viewBox","children"],polygon:f,polyline:f},h=(a,b)=>{if(!a||"function"==typeof a||"boolean"==typeof a)return null;var c=a;if((0,d.isValidElement)(a)&&(c=a.props),"object"!=typeof c&&"function"!=typeof c)return null;var f={};return Object.keys(c).forEach(a=>{(0,e.q)(a)&&(f[a]=b||(b=>c[a](c,b)))}),f},i=(a,b,c)=>{if(null===a||"object"!=typeof a&&"function"!=typeof a)return null;var d=null;return Object.keys(a).forEach(f=>{var g=a[f];(0,e.q)(f)&&"function"==typeof g&&(d||(d={}),d[f]=a=>(g(b,c,a),null))}),d}},61508:(a,b,c)=>{"use strict";c.d(b,{N:()=>i});var d=c(22688),e=c(79241),f=c(60335);function g(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function h(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?g(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):g(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var i=(a,b,c,g,i,j,k)=>{if(null!=b&&null!=j){var{chartData:l,computedData:m,dataStartIndex:n,dataEndIndex:o}=c;return a.reduce((a,c)=>{var p,q,r,s,t,{dataDefinedOnItem:u,settings:v}=c,w=(p=u,q=l,null!=p?p:q),x=Array.isArray(w)?(0,f.v)(w,n,o):w,y=null!=(r=null==v?void 0:v.dataKey)?r:null==g?void 0:g.dataKey,z=null==v?void 0:v.nameKey;return Array.isArray(s=null!=g&&g.dataKey&&Array.isArray(x)&&!Array.isArray(x[0])&&"axis"===k?(0,d.eP)(x,g.dataKey,i):j(x,b,m,z))?s.forEach(b=>{var c=h(h({},v),{},{name:b.name,unit:b.unit,color:void 0,fill:void 0});a.push((0,e.GF)({tooltipEntrySettings:c,dataKey:b.dataKey,payload:b.payload,value:(0,e.kr)(b.payload,b.dataKey),name:b.name}))}):a.push((0,e.GF)({tooltipEntrySettings:v,dataKey:y,payload:s,value:(0,e.kr)(s,y),name:null!=(t=(0,e.kr)(s,z))?t:null==v?void 0:v.name})),a},[])}}},61526:(a,b,c)=>{a.exports=c(10003).isPlainObject},61649:(a,b,c)=>{a.exports=c(69014).uniqBy},61711:(a,b,c)=>{"use strict";function d(a){return null!=a.stackId&&null!=a.dataKey}c.d(b,{g:()=>d})},62761:(a,b,c)=>{"use strict";c.d(b,{i:()=>g});var d=c(46279);function e(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function f(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?e(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):e(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var g=(a,b,c,e)=>{if(null==b)return d.k_;var g=function(a,b,c){return"axis"===b?"click"===c?a.axisInteraction.click:a.axisInteraction.hover:"click"===c?a.itemInteraction.click:a.itemInteraction.hover}(a,b,c);if(null==g)return d.k_;if(g.active)return g;if(a.keyboardInteraction.active)return a.keyboardInteraction;if(a.syncInteraction.active&&null!=a.syncInteraction.index)return a.syncInteraction;var h=!0===a.settings.active;if(null!=g.index){if(h)return f(f({},g),{},{active:!0})}else if(null!=e)return{active:!0,coordinate:void 0,dataKey:void 0,index:e};return f(f({},d.k_),{},{coordinate:g.coordinate})}},63004:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(34548);b.cloneDeep=function(a){return d.cloneDeepWithImpl(a,void 0,a,new Map,void 0)}},63258:(a,b,c)=>{"use strict";c.d(b,{M:()=>f,t:()=>e});var d=c(38301),e=(0,d.createContext)(null),f=()=>(0,d.useContext)(e)},63409:(a,b,c)=>{"use strict";function d(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function e(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?d(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):d(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}c.d(b,{dl:()=>f,mP:()=>g,s8:()=>h});var f=(a,b,c)=>a.map(a=>"".concat(a.replace(/([A-Z])/g,a=>"-".concat(a.toLowerCase()))," ").concat(b,"ms ").concat(c)).join(","),g=(a,b)=>[Object.keys(a),Object.keys(b)].reduce((a,b)=>a.filter(a=>b.includes(a))),h=(a,b)=>Object.keys(b).reduce((c,d)=>e(e({},c),{},{[d]:a(d,b[d])}),{})},63724:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(30276),e=c(26609);b.range=function(a,b,c){c&&"number"!=typeof c&&d.isIterateeCall(a,b,c)&&(b=c=void 0),a=e.toFinite(a),void 0===b?(b=a,a=0):b=e.toFinite(b),c=void 0===c?a<b?1:-1:e.toFinite(c);let f=Math.max(Math.ceil((b-a)/(c||1)),0),g=Array(f);for(let b=0;b<f;b++)g[b]=a,a+=c;return g}},63725:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{default:function(){return p},defaultHead:function(){return l}});let d=c(35288),e=c(55823),f=c(21124),g=e._(c(38301)),h=d._(c(57684)),i=c(81578),j=c(19746),k=c(15217);function l(a){void 0===a&&(a=!1);let b=[(0,f.jsx)("meta",{charSet:"utf-8"},"charset")];return a||b.push((0,f.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),b}function m(a,b){return"string"==typeof b||"number"==typeof b?a:b.type===g.default.Fragment?a.concat(g.default.Children.toArray(b.props.children).reduce((a,b)=>"string"==typeof b||"number"==typeof b?a:a.concat(b),[])):a.concat(b)}c(21507);let n=["name","httpEquiv","charSet","itemProp"];function o(a,b){let{inAmpMode:c}=b;return a.reduce(m,[]).reverse().concat(l(c).reverse()).filter(function(){let a=new Set,b=new Set,c=new Set,d={};return e=>{let f=!0,g=!1;if(e.key&&"number"!=typeof e.key&&e.key.indexOf("$")>0){g=!0;let b=e.key.slice(e.key.indexOf("$")+1);a.has(b)?f=!1:a.add(b)}switch(e.type){case"title":case"base":b.has(e.type)?f=!1:b.add(e.type);break;case"meta":for(let a=0,b=n.length;a<b;a++){let b=n[a];if(e.props.hasOwnProperty(b))if("charSet"===b)c.has(b)?f=!1:c.add(b);else{let a=e.props[b],c=d[b]||new Set;("name"!==b||!g)&&c.has(a)?f=!1:(c.add(a),d[b]=c)}}}return f}}()).reverse().map((a,b)=>{let c=a.key||b;return g.default.cloneElement(a,{key:c})})}let p=function(a){let{children:b}=a,c=(0,g.useContext)(i.AmpStateContext),d=(0,g.useContext)(j.HeadManagerContext);return(0,f.jsx)(h.default,{reduceComponentsToState:o,headManager:d,inAmpMode:(0,k.isInAmpMode)(c),children:b})};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},63974:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"getImgProps",{enumerable:!0,get:function(){return i}}),c(21507);let d=c(78757),e=c(3001),f=["-moz-initial","fill","none","scale-down",void 0];function g(a){return void 0!==a.default}function h(a){return void 0===a?a:"number"==typeof a?Number.isFinite(a)?a:NaN:"string"==typeof a&&/^[0-9]+$/.test(a)?parseInt(a,10):NaN}function i(a,b){var c,i;let j,k,l,{src:m,sizes:n,unoptimized:o=!1,priority:p=!1,loading:q,className:r,quality:s,width:t,height:u,fill:v=!1,style:w,overrideSrc:x,onLoad:y,onLoadingComplete:z,placeholder:A="empty",blurDataURL:B,fetchPriority:C,decoding:D="async",layout:E,objectFit:F,objectPosition:G,lazyBoundary:H,lazyRoot:I,...J}=a,{imgConf:K,showAltText:L,blurComplete:M,defaultLoader:N}=b,O=K||e.imageConfigDefault;if("allSizes"in O)j=O;else{let a=[...O.deviceSizes,...O.imageSizes].sort((a,b)=>a-b),b=O.deviceSizes.sort((a,b)=>a-b),d=null==(c=O.qualities)?void 0:c.sort((a,b)=>a-b);j={...O,allSizes:a,deviceSizes:b,qualities:d}}if(void 0===N)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let P=J.loader||N;delete J.loader,delete J.srcSet;let Q="__next_img_default"in P;if(Q){if("custom"===j.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let a=P;P=b=>{let{config:c,...d}=b;return a(d)}}if(E){"fill"===E&&(v=!0);let a={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[E];a&&(w={...w,...a});let b={responsive:"100vw",fill:"100vw"}[E];b&&!n&&(n=b)}let R="",S=h(t),T=h(u);if((i=m)&&"object"==typeof i&&(g(i)||void 0!==i.src)){let a=g(m)?m.default:m;if(!a.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!a.height||!a.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(k=a.blurWidth,l=a.blurHeight,B=B||a.blurDataURL,R=a.src,!v)if(S||T){if(S&&!T){let b=S/a.width;T=Math.round(a.height*b)}else if(!S&&T){let b=T/a.height;S=Math.round(a.width*b)}}else S=a.width,T=a.height}let U=!p&&("lazy"===q||void 0===q);(!(m="string"==typeof m?m:R)||m.startsWith("data:")||m.startsWith("blob:"))&&(o=!0,U=!1),j.unoptimized&&(o=!0),Q&&!j.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(o=!0);let V=h(s),W=Object.assign(v?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:F,objectPosition:G}:{},L?{}:{color:"transparent"},w),X=M||"empty"===A?null:"blur"===A?'url("data:image/svg+xml;charset=utf-8,'+(0,d.getImageBlurSvg)({widthInt:S,heightInt:T,blurWidth:k,blurHeight:l,blurDataURL:B||"",objectFit:W.objectFit})+'")':'url("'+A+'")',Y=f.includes(W.objectFit)?"fill"===W.objectFit?"100% 100%":"cover":W.objectFit,Z=X?{backgroundSize:Y,backgroundPosition:W.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},$=function(a){let{config:b,src:c,unoptimized:d,width:e,quality:f,sizes:g,loader:h}=a;if(d)return{src:c,srcSet:void 0,sizes:void 0};let{widths:i,kind:j}=function(a,b,c){let{deviceSizes:d,allSizes:e}=a;if(c){let a=/(^|\s)(1?\d?\d)vw/g,b=[];for(let d;d=a.exec(c);)b.push(parseInt(d[2]));if(b.length){let a=.01*Math.min(...b);return{widths:e.filter(b=>b>=d[0]*a),kind:"w"}}return{widths:e,kind:"w"}}return"number"!=typeof b?{widths:d,kind:"w"}:{widths:[...new Set([b,2*b].map(a=>e.find(b=>b>=a)||e[e.length-1]))],kind:"x"}}(b,e,g),k=i.length-1;return{sizes:g||"w"!==j?g:"100vw",srcSet:i.map((a,d)=>h({config:b,src:c,quality:f,width:a})+" "+("w"===j?a:d+1)+j).join(", "),src:h({config:b,src:c,quality:f,width:i[k]})}}({config:j,src:m,unoptimized:o,width:S,quality:V,sizes:n,loader:P});return{props:{...J,loading:U?"lazy":q,fetchPriority:C,width:S,height:T,decoding:D,className:r,style:{...W,...Z},sizes:$.sizes,srcSet:$.srcSet,src:x||$.src},meta:{unoptimized:o,priority:p,placeholder:A,fill:v}}}},64214:(a,b,c)=>{"use strict";c.d(b,{R:()=>d});var d=function(a,b){for(var c=arguments.length,d=Array(c>2?c-2:0),e=2;e<c;e++)d[e-2]=arguments[e]}},65168:(a,b,c)=>{"use strict";c.d(b,{Be:()=>q,Cv:()=>w,D0:()=>y,Gl:()=>r,Dc:()=>x});var d=c(54985),e=c(35268),f=c(92173),g=c(29160),h=c(22688),i={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},j={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},k=c(44611),l=c(15379),m={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:i.angleAxisId,includeHidden:!1,name:void 0,reversed:i.reversed,scale:i.scale,tick:i.tick,tickCount:void 0,ticks:void 0,type:i.type,unit:void 0},n={allowDataOverflow:j.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:j.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:j.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:j.scale,tick:j.tick,tickCount:j.tickCount,ticks:void 0,type:j.type,unit:void 0},o={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:i.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:i.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:i.scale,tick:i.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},p={allowDataOverflow:j.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:j.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:j.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:j.scale,tick:j.tick,tickCount:j.tickCount,ticks:void 0,type:"category",unit:void 0},q=(a,b)=>null!=a.polarAxis.angleAxis[b]?a.polarAxis.angleAxis[b]:"radial"===a.layout.layoutType?o:m,r=(a,b)=>null!=a.polarAxis.radiusAxis[b]?a.polarAxis.radiusAxis[b]:"radial"===a.layout.layoutType?p:n,s=a=>a.polarOptions,t=(0,d.Mz)([e.Lp,e.A$,f.HZ],g.lY),u=(0,d.Mz)([s,t],(a,b)=>{if(null!=a)return(0,h.F4)(a.innerRadius,b,0)}),v=(0,d.Mz)([s,t],(a,b)=>{if(null!=a)return(0,h.F4)(a.outerRadius,b,.8*b)}),w=(0,d.Mz)([s],a=>{if(null==a)return[0,0];var{startAngle:b,endAngle:c}=a;return[b,c]});(0,d.Mz)([q,w],k.I);var x=(0,d.Mz)([t,u,v],(a,b,c)=>{if(null!=a&&null!=b&&null!=c)return[b,c]});(0,d.Mz)([r,x],k.I);var y=(0,d.Mz)([l.fz,s,u,v,e.Lp,e.A$],(a,b,c,d,e,f)=>{if(("centric"===a||"radial"===a)&&null!=b&&null!=c&&null!=d){var{cx:g,cy:i,startAngle:j,endAngle:k}=b;return{cx:(0,h.F4)(g,e,e/2),cy:(0,h.F4)(i,f,f/2),innerRadius:c,outerRadius:d,startAngle:j,endAngle:k,clockWise:!1}}})},65302:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(46600),e=c(45215),f=c(94741),g=c(22580);b.iteratee=function(a){if(null==a)return d.identity;switch(typeof a){case"function":return a;case"object":if(Array.isArray(a)&&2===a.length)return g.matchesProperty(a[0],a[1]);return f.matches(a);case"string":case"symbol":case"number":return e.property(a)}}},65764:(a,b,c)=>{"use strict";function d(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function e(a,b){var c=function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?d(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):d(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({},a);return Object.keys(b).reduce((a,c)=>(void 0===a[c]&&void 0!==b[c]&&(a[c]=b[c]),a),c)}c.d(b,{e:()=>e})},65893:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},67852:(a,b,c)=>{"use strict";c.d(b,{YF:()=>j,dj:()=>k,fP:()=>l,ky:()=>i});var d=c(53968),e=c(46279),f=c(98246),g=c(5142),h=c(73604),i=(0,d.VP)("mouseClick"),j=(0,d.Nc)();j.startListening({actionCreator:i,effect:(a,b)=>{var c=a.payload,d=(0,f.g)(b.getState(),(0,h.w)(c));(null==d?void 0:d.activeIndex)!=null&&b.dispatch((0,e.jF)({activeIndex:d.activeIndex,activeDataKey:void 0,activeCoordinate:d.activeCoordinate}))}});var k=(0,d.VP)("mouseMove"),l=(0,d.Nc)();l.startListening({actionCreator:k,effect:(a,b)=>{var c=a.payload,d=b.getState(),i=(0,g.au)(d,d.tooltip.settings.shared),j=(0,f.g)(d,(0,h.w)(c));"axis"===i&&((null==j?void 0:j.activeIndex)!=null?b.dispatch((0,e.Nt)({activeIndex:j.activeIndex,activeDataKey:void 0,activeCoordinate:j.activeCoordinate})):b.dispatch((0,e.xS)()))}})},68543:(a,b,c)=>{"use strict";a.exports=c(81851)},69014:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(88485),e=c(46600),f=c(71929),g=c(65302);b.uniqBy=function(a,b=e.identity){return f.isArrayLikeObject(a)?d.uniqBy(Array.from(a),g.iteratee(b)):[]}},70522:(a,b,c)=>{"use strict";c.d(b,{s:()=>E});var d=c(38301),e=c(23312),f=c(63258),g=c(43249),h=c(99539),i=c(97652),j=c(61188);function k(){return(k=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function l(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function m(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class n extends d.PureComponent{renderIcon(a,b){var{inactiveColor:c}=this.props,e=32/6,f=32/3,g=a.inactive?c:a.color,h=null!=b?b:a.type;if("none"===h)return null;if("plainline"===h)return d.createElement("line",{strokeWidth:4,fill:"none",stroke:g,strokeDasharray:a.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===h)return d.createElement("path",{strokeWidth:4,fill:"none",stroke:g,d:"M0,".concat(16,"h").concat(f,"\n            A").concat(e,",").concat(e,",0,1,1,").concat(2*f,",").concat(16,"\n            H").concat(32,"M").concat(2*f,",").concat(16,"\n            A").concat(e,",").concat(e,",0,1,1,").concat(f,",").concat(16),className:"recharts-legend-icon"});if("rect"===h)return d.createElement("path",{stroke:"none",fill:g,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(d.isValidElement(a.legendIcon)){var j=function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?l(Object(c),!0).forEach(function(b){m(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):l(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({},a);return delete j.legendIcon,d.cloneElement(a.legendIcon,j)}return d.createElement(i.i,{fill:g,cx:16,cy:16,size:32,sizeType:"diameter",type:h})}renderItems(){var{payload:a,iconSize:b,layout:c,formatter:e,inactiveColor:f,iconType:i}=this.props,l={x:0,y:0,width:32,height:32},m={display:"horizontal"===c?"inline-block":"block",marginRight:10},n={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map((a,c)=>{var o=a.formatter||e,p=(0,g.$)({"recharts-legend-item":!0,["legend-item-".concat(c)]:!0,inactive:a.inactive});if("none"===a.type)return null;var q=a.inactive?f:a.color,r=o?o(a.value,a,c):a.value;return d.createElement("li",k({className:p,style:m,key:"legend-item-".concat(c)},(0,j.XC)(this.props,a,c)),d.createElement(h.u,{width:b,height:b,viewBox:l,style:n,"aria-label":"".concat(r," legend icon")},this.renderIcon(a,i)),d.createElement("span",{className:"recharts-legend-item-text",style:{color:q}},r))})}render(){var{payload:a,layout:b,align:c}=this.props;return a&&a.length?d.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===b?c:"left"}},this.renderItems()):null}}m(n,"displayName","Legend"),m(n,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",layout:"horizontal",verticalAlign:"middle"});var o=c(22688),p=c(61649),q=c.n(p),r=c(56998),s=c(54758),t=c(15379);c(75959);var u=["contextPayload"];function v(){return(v=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function w(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function x(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?w(Object(c),!0).forEach(function(b){y(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):w(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function y(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function z(a){return a.value}function A(a){var b,{contextPayload:c}=a,e=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,u),f=(b=a.payloadUniqBy,!0===b?q()(c,z):"function"==typeof b?q()(c,b):c),g=x(x({},e),{},{payload:f});return d.isValidElement(a.content)?d.cloneElement(a.content,g):"function"==typeof a.content?d.createElement(a.content,g):d.createElement(n,g)}function B(a){return(0,r.j)(),null}function C(a){return(0,r.j)(),null}function D(a){var b=(0,r.G)(s.g0),c=(0,f.M)(),g=(0,t.Kp)(),{width:h,height:i,wrapperStyle:j,portal:k}=a,[l,m]=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[b,c]=(0,d.useState)({height:0,left:0,top:0,width:0}),e=(0,d.useCallback)(a=>{if(null!=a){var d=a.getBoundingClientRect(),e={height:d.height,left:d.left,top:d.top,width:d.width};(Math.abs(e.height-b.height)>1||Math.abs(e.left-b.left)>1||Math.abs(e.top-b.top)>1||Math.abs(e.width-b.width)>1)&&c({height:e.height,left:e.left,top:e.top,width:e.width})}},[b.width,b.height,b.top,b.left,...a]);return[b,e]}([b]),n=(0,t.yi)(),o=(0,t.rY)(),p=n-(g.left||0)-(g.right||0),q=E.getWidthOrHeight(a.layout,i,h,p),u=k?j:x(x({position:"absolute",width:(null==q?void 0:q.width)||h||"auto",height:(null==q?void 0:q.height)||i||"auto"},function(a,b,c,d,e,f){var g,h,{layout:i,align:j,verticalAlign:k}=b;return a&&(void 0!==a.left&&null!==a.left||void 0!==a.right&&null!==a.right)||(g="center"===j&&"vertical"===i?{left:((d||0)-f.width)/2}:"right"===j?{right:c&&c.right||0}:{left:c&&c.left||0}),a&&(void 0!==a.top&&null!==a.top||void 0!==a.bottom&&null!==a.bottom)||(h="middle"===k?{top:((e||0)-f.height)/2}:"bottom"===k?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),x(x({},g),h)}(j,a,g,n,o,l)),j),w=null!=k?k:c;if(null==w)return null;var y=d.createElement("div",{className:"recharts-legend-wrapper",style:u,ref:m},d.createElement(B,{layout:a.layout,align:a.align,verticalAlign:a.verticalAlign,itemSorter:a.itemSorter}),d.createElement(C,{width:l.width,height:l.height}),d.createElement(A,v({},a,q,{margin:g,chartWidth:n,chartHeight:o,contextPayload:b})));return(0,e.createPortal)(y,w)}class E extends d.PureComponent{static getWidthOrHeight(a,b,c,d){return"vertical"===a&&(0,o.Et)(b)?{height:b}:"horizontal"===a?{width:c||d}:null}render(){return d.createElement(D,this.props)}}y(E,"displayName","Legend"),y(E,"defaultProps",{align:"center",iconSize:14,itemSorter:"value",layout:"horizontal",verticalAlign:"bottom"})},71016:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(90469);b.debounce=function(a,b=0,c={}){let e;"object"!=typeof c&&(c={});let{leading:f=!1,trailing:g=!0,maxWait:h}=c,i=[,,];f&&(i[0]="leading"),g&&(i[1]="trailing");let j=null,k=d.debounce(function(...b){e=a.apply(this,b),j=null},b,{edges:i}),l=function(...b){return null!=h&&(null===j&&(j=Date.now()),Date.now()-j>=h)?(e=a.apply(this,b),j=Date.now(),k.cancel(),k.schedule(),e):(k.apply(this,b),e)};return l.cancel=k.cancel,l.flush=()=>(k.flush(),e),l}},71156:(a,b,c)=>{"use strict";c.d(b,{F0:()=>d,tQ:()=>f,um:()=>e});var d="data-recharts-item-index",e="data-recharts-item-data-key",f=60},71929:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(77542),e=c(13258);b.isArrayLikeObject=function(a){return e.isObjectLike(a)&&d.isArrayLike(a)}},72001:(a,b,c)=>{"use strict";c.d(b,{Qx:()=>j,a6:()=>k,h4:()=>T,jM:()=>S,ss:()=>Q});var d,e=Symbol.for("immer-nothing"),f=Symbol.for("immer-draftable"),g=Symbol.for("immer-state");function h(a){throw Error(`[Immer] minified error nr: ${a}. Full error at: https://bit.ly/3cXEKWf`)}var i=Object.getPrototypeOf;function j(a){return!!a&&!!a[g]}function k(a){return!!a&&(m(a)||Array.isArray(a)||!!a[f]||!!a.constructor?.[f]||r(a)||s(a))}var l=Object.prototype.constructor.toString();function m(a){if(!a||"object"!=typeof a)return!1;let b=i(a);if(null===b)return!0;let c=Object.hasOwnProperty.call(b,"constructor")&&b.constructor;return c===Object||"function"==typeof c&&Function.toString.call(c)===l}function n(a,b){0===o(a)?Reflect.ownKeys(a).forEach(c=>{b(c,a[c],a)}):a.forEach((c,d)=>b(d,c,a))}function o(a){let b=a[g];return b?b.type_:Array.isArray(a)?1:r(a)?2:3*!!s(a)}function p(a,b){return 2===o(a)?a.has(b):Object.prototype.hasOwnProperty.call(a,b)}function q(a,b,c){let d=o(a);2===d?a.set(b,c):3===d?a.add(c):a[b]=c}function r(a){return a instanceof Map}function s(a){return a instanceof Set}function t(a){return a.copy_||a.base_}function u(a,b){if(r(a))return new Map(a);if(s(a))return new Set(a);if(Array.isArray(a))return Array.prototype.slice.call(a);let c=m(a);if(!0!==b&&("class_only"!==b||c)){let b=i(a);return null!==b&&c?{...a}:Object.assign(Object.create(b),a)}{let b=Object.getOwnPropertyDescriptors(a);delete b[g];let c=Reflect.ownKeys(b);for(let d=0;d<c.length;d++){let e=c[d],f=b[e];!1===f.writable&&(f.writable=!0,f.configurable=!0),(f.get||f.set)&&(b[e]={configurable:!0,writable:!0,enumerable:f.enumerable,value:a[e]})}return Object.create(i(a),b)}}function v(a,b=!1){return x(a)||j(a)||!k(a)||(o(a)>1&&(a.set=a.add=a.clear=a.delete=w),Object.freeze(a),b&&Object.entries(a).forEach(([a,b])=>v(b,!0))),a}function w(){h(2)}function x(a){return Object.isFrozen(a)}var y={};function z(a){let b=y[a];return b||h(0,a),b}function A(a,b){b&&(z("Patches"),a.patches_=[],a.inversePatches_=[],a.patchListener_=b)}function B(a){C(a),a.drafts_.forEach(E),a.drafts_=null}function C(a){a===d&&(d=a.parent_)}function D(a){return d={drafts_:[],parent_:d,immer_:a,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function E(a){let b=a[g];0===b.type_||1===b.type_?b.revoke_():b.revoked_=!0}function F(a,b){b.unfinalizedDrafts_=b.drafts_.length;let c=b.drafts_[0];return void 0!==a&&a!==c?(c[g].modified_&&(B(b),h(4)),k(a)&&(a=G(b,a),b.parent_||I(b,a)),b.patches_&&z("Patches").generateReplacementPatches_(c[g].base_,a,b.patches_,b.inversePatches_)):a=G(b,c,[]),B(b),b.patches_&&b.patchListener_(b.patches_,b.inversePatches_),a!==e?a:void 0}function G(a,b,c){if(x(b))return b;let d=b[g];if(!d)return n(b,(e,f)=>H(a,d,b,e,f,c)),b;if(d.scope_!==a)return b;if(!d.modified_)return I(a,d.base_,!0),d.base_;if(!d.finalized_){d.finalized_=!0,d.scope_.unfinalizedDrafts_--;let b=d.copy_,e=b,f=!1;3===d.type_&&(e=new Set(b),b.clear(),f=!0),n(e,(e,g)=>H(a,d,b,e,g,c,f)),I(a,b,!1),c&&a.patches_&&z("Patches").generatePatches_(d,c,a.patches_,a.inversePatches_)}return d.copy_}function H(a,b,c,d,e,f,g){if(j(e)){let g=G(a,e,f&&b&&3!==b.type_&&!p(b.assigned_,d)?f.concat(d):void 0);if(q(c,d,g),!j(g))return;a.canAutoFreeze_=!1}else g&&c.add(e);if(k(e)&&!x(e)){if(!a.immer_.autoFreeze_&&a.unfinalizedDrafts_<1)return;G(a,e),(!b||!b.scope_.parent_)&&"symbol"!=typeof d&&Object.prototype.propertyIsEnumerable.call(c,d)&&I(a,e)}}function I(a,b,c=!1){!a.parent_&&a.immer_.autoFreeze_&&a.canAutoFreeze_&&v(b,c)}var J={get(a,b){if(b===g)return a;let c=t(a);if(!p(c,b)){var d=a,e=c,f=b;let g=M(e,f);return g?"value"in g?g.value:g.get?.call(d.draft_):void 0}let h=c[b];return a.finalized_||!k(h)?h:h===L(a.base_,b)?(O(a),a.copy_[b]=P(h,a)):h},has:(a,b)=>b in t(a),ownKeys:a=>Reflect.ownKeys(t(a)),set(a,b,c){let d=M(t(a),b);if(d?.set)return d.set.call(a.draft_,c),!0;if(!a.modified_){let d=L(t(a),b),e=d?.[g];if(e&&e.base_===c)return a.copy_[b]=c,a.assigned_[b]=!1,!0;if((c===d?0!==c||1/c==1/d:c!=c&&d!=d)&&(void 0!==c||p(a.base_,b)))return!0;O(a),N(a)}return!!(a.copy_[b]===c&&(void 0!==c||b in a.copy_)||Number.isNaN(c)&&Number.isNaN(a.copy_[b]))||(a.copy_[b]=c,a.assigned_[b]=!0,!0)},deleteProperty:(a,b)=>(void 0!==L(a.base_,b)||b in a.base_?(a.assigned_[b]=!1,O(a),N(a)):delete a.assigned_[b],a.copy_&&delete a.copy_[b],!0),getOwnPropertyDescriptor(a,b){let c=t(a),d=Reflect.getOwnPropertyDescriptor(c,b);return d?{writable:!0,configurable:1!==a.type_||"length"!==b,enumerable:d.enumerable,value:c[b]}:d},defineProperty(){h(11)},getPrototypeOf:a=>i(a.base_),setPrototypeOf(){h(12)}},K={};function L(a,b){let c=a[g];return(c?t(c):a)[b]}function M(a,b){if(!(b in a))return;let c=i(a);for(;c;){let a=Object.getOwnPropertyDescriptor(c,b);if(a)return a;c=i(c)}}function N(a){!a.modified_&&(a.modified_=!0,a.parent_&&N(a.parent_))}function O(a){a.copy_||(a.copy_=u(a.base_,a.scope_.immer_.useStrictShallowCopy_))}function P(a,b){let c=r(a)?z("MapSet").proxyMap_(a,b):s(a)?z("MapSet").proxySet_(a,b):function(a,b){let c=Array.isArray(a),e={type_:+!!c,scope_:b?b.scope_:d,modified_:!1,finalized_:!1,assigned_:{},parent_:b,base_:a,draft_:null,copy_:null,revoke_:null,isManual_:!1},f=e,g=J;c&&(f=[e],g=K);let{revoke:h,proxy:i}=Proxy.revocable(f,g);return e.draft_=i,e.revoke_=h,i}(a,b);return(b?b.scope_:d).drafts_.push(c),c}function Q(a){return j(a)||h(10,a),function a(b){let c;if(!k(b)||x(b))return b;let d=b[g];if(d){if(!d.modified_)return d.base_;d.finalized_=!0,c=u(b,d.scope_.immer_.useStrictShallowCopy_)}else c=u(b,!0);return n(c,(b,d)=>{q(c,b,a(d))}),d&&(d.finalized_=!1),c}(a)}n(J,(a,b)=>{K[a]=function(){return arguments[0]=arguments[0][0],b.apply(this,arguments)}}),K.deleteProperty=function(a,b){return K.set.call(this,a,b,void 0)},K.set=function(a,b,c){return J.set.call(this,a[0],b,c,a[0])};var R=new class{constructor(a){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(a,b,c)=>{let d;if("function"==typeof a&&"function"!=typeof b){let c=b;b=a;let d=this;return function(a=c,...e){return d.produce(a,a=>b.call(this,a,...e))}}if("function"!=typeof b&&h(6),void 0!==c&&"function"!=typeof c&&h(7),k(a)){let e=D(this),f=P(a,void 0),g=!0;try{d=b(f),g=!1}finally{g?B(e):C(e)}return A(e,c),F(d,e)}if(a&&"object"==typeof a)h(1,a);else{if(void 0===(d=b(a))&&(d=a),d===e&&(d=void 0),this.autoFreeze_&&v(d,!0),c){let b=[],e=[];z("Patches").generateReplacementPatches_(a,d,b,e),c(b,e)}return d}},this.produceWithPatches=(a,b)=>{let c,d;return"function"==typeof a?(b,...c)=>this.produceWithPatches(b,b=>a(b,...c)):[this.produce(a,b,(a,b)=>{c=a,d=b}),c,d]},"boolean"==typeof a?.autoFreeze&&this.setAutoFreeze(a.autoFreeze),"boolean"==typeof a?.useStrictShallowCopy&&this.setUseStrictShallowCopy(a.useStrictShallowCopy)}createDraft(a){k(a)||h(8),j(a)&&(a=Q(a));let b=D(this),c=P(a,void 0);return c[g].isManual_=!0,C(b),c}finishDraft(a,b){let c=a&&a[g];c&&c.isManual_||h(9);let{scope_:d}=c;return A(d,b),F(void 0,d)}setAutoFreeze(a){this.autoFreeze_=a}setUseStrictShallowCopy(a){this.useStrictShallowCopy_=a}applyPatches(a,b){let c;for(c=b.length-1;c>=0;c--){let d=b[c];if(0===d.path.length&&"replace"===d.op){a=d.value;break}}c>-1&&(b=b.slice(c+1));let d=z("Patches").applyPatches_;return j(a)?d(a,b):this.produce(a,a=>d(a,b))}},S=R.produce;function T(a){return a}R.produceWithPatches.bind(R),R.setAutoFreeze.bind(R),R.setUseStrictShallowCopy.bind(R),R.applyPatches.bind(R),R.createDraft.bind(R),R.finishDraft.bind(R)},72577:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(33780),e=c(41650),f=c(77241);b.orderBy=function(a,b,c,g){if(null==a)return[];c=g?void 0:c,Array.isArray(a)||(a=Object.values(a)),Array.isArray(b)||(b=null==b?[null]:[b]),0===b.length&&(b=[null]),Array.isArray(c)||(c=null==c?[]:[c]),c=c.map(a=>String(a));let h=(a,b)=>{let c=a;for(let a=0;a<b.length&&null!=c;++a)c=c[b[a]];return c},i=b.map(a=>(Array.isArray(a)&&1===a.length&&(a=a[0]),null==a||"function"==typeof a||Array.isArray(a)||e.isKey(a))?a:{key:a,path:f.toPath(a)});return a.map(a=>({original:a,criteria:i.map(b=>{var c,d;return c=b,null==(d=a)||null==c?d:"object"==typeof c&&"key"in c?Object.hasOwn(d,c.key)?d[c.key]:h(d,c.path):"function"==typeof c?c(d):Array.isArray(c)?h(d,c):"object"==typeof d?d[c]:d})})).slice().sort((a,b)=>{for(let e=0;e<i.length;e++){let f=d.compareValues(a.criteria[e],b.criteria[e],c[e]);if(0!==f)return f}return 0}).map(a=>a.original)}},72650:(a,b,c)=>{"use strict";c.d(b,{p:()=>f,v:()=>g});var d=c(38301),e=c(56998);function f(a){return(0,e.j)(),(0,d.useRef)(null),null}function g(a){return(0,e.j)(),null}c(61185)},72677:(a,b,c)=>{"use strict";c.d(b,{J9:()=>r,aS:()=>p,y$:()=>q});var d=c(37177),e=c.n(d),f=c(38301),g=c(68543),h=c(22688),i=c(61188),j=c(31209),k=c(41800),l=a=>"string"==typeof a?a:a?a.displayName||a.name||"Component":"",m=null,n=null,o=a=>{if(a===m&&Array.isArray(n))return n;var b=[];return f.Children.forEach(a,a=>{(0,h.uy)(a)||((0,g.isFragment)(a)?b=b.concat(o(a.props.children)):b.push(a))}),n=b,m=a,b};function p(a,b){var c=[],d=[];return d=Array.isArray(b)?b.map(a=>l(a)):[l(b)],o(a).forEach(a=>{var b=e()(a,"type.displayName")||e()(a,"type.name");-1!==d.indexOf(b)&&c.push(a)}),c}var q=a=>!a||"object"!=typeof a||!("clipDot"in a)||!!a.clipDot,r=(a,b,c)=>{if(!a||"function"==typeof a||"boolean"==typeof a)return null;var d=a;if((0,f.isValidElement)(a)&&(d=a.props),"object"!=typeof d&&"function"!=typeof d)return null;var e={};return Object.keys(d).forEach(a=>{var f;((a,b,c,d)=>{if("symbol"==typeof b||"number"==typeof b)return!0;var e,f=null!=(e=d&&(null===i.VU||void 0===i.VU?void 0:i.VU[d]))?e:[],g=b.startsWith("data-"),h="function"!=typeof a&&(!!d&&f.includes(b)||(0,k.R)(b)),l=!!c&&(0,j.q)(b);return g||h||l})(null==(f=d)?void 0:f[a],a,b,c)&&(e[a]=d[a])}),e}},72706:(a,b,c)=>{"use strict";function d(a){return"object"==typeof a&&"length"in a?a:Array.from(a)}c.d(b,{A:()=>d}),Array.prototype.slice},73604:(a,b,c)=>{"use strict";c.d(b,{w:()=>d});var d=a=>{var b=a.currentTarget.getBoundingClientRect(),c=b.width/a.currentTarget.offsetWidth,d=b.height/a.currentTarget.offsetHeight;return{chartX:Math.round((a.clientX-b.left)/c),chartY:Math.round((a.clientY-b.top)/d)}}},73665:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.eq=function(a,b){return a===b||Number.isNaN(a)&&Number.isNaN(b)}},74030:(a,b,c)=>{"use strict";c.d(b,{J:()=>f,U:()=>e});var d=(0,c(53968).Z0)({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(a,b)=>b.payload}}),{updatePolarOptions:e}=d.actions,f=d.reducer},75309:(a,b,c)=>{"use strict";function d(a){return null==a?void 0:a.id}c.d(b,{x:()=>d})},75639:(a,b,c)=>{"use strict";c.d(b,{J:()=>d});var d=a=>a.tooltip},75959:(a,b,c)=>{"use strict";c.d(b,{CU:()=>k,Lx:()=>i,h1:()=>h,hx:()=>g,u3:()=>j});var d=c(53968),e=c(72001),f=(0,d.Z0)({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(a,b){a.size.width=b.payload.width,a.size.height=b.payload.height},setLegendSettings(a,b){a.settings.align=b.payload.align,a.settings.layout=b.payload.layout,a.settings.verticalAlign=b.payload.verticalAlign,a.settings.itemSorter=b.payload.itemSorter},addLegendPayload(a,b){a.payload.push((0,e.h4)(b.payload))},removeLegendPayload(a,b){var c=(0,e.ss)(a).payload.indexOf((0,e.h4)(b.payload));c>-1&&a.payload.splice(c,1)}}}),{setLegendSize:g,setLegendSettings:h,addLegendPayload:i,removeLegendPayload:j}=f.actions,k=f.reducer},76265:(a,b,c)=>{"use strict";c.d(b,{BZ:()=>ak,eE:()=>ao,Xb:()=>al,JG:()=>ar,A2:()=>aj,yn:()=>ap,gL:()=>aa,R4:()=>ae,n4:()=>H});var d=c(54985),e=c(60343),f=c(15379),g=c(79241),h=c(8693),i=c(14851),j=c(22688),k=c(44611),l=c(5142),m=c(42911),n=c(62761),o=c(33648),p=c(90481),q=c(35268),r=c(92173),s=c(52063),t=c(32187),u=c(75639),v=c(61508),w=c(50088),x=c(34729),y=c(25401),z=c(76495),A=c(61711),B=(0,d.Mz)([y.D,f.fz,e.um,i.iO,x.R],e.sr),C=(0,d.Mz)([a=>a.graphicalItems.cartesianItems,a=>a.graphicalItems.polarItems],(a,b)=>[...a,...b]),D=(0,d.Mz)([x.R,w.M],e.eo),E=(0,d.Mz)([C,y.D,D],e.ec),F=(0,d.Mz)([E],a=>a.filter(A.g)),G=(0,d.Mz)([E],e.rj),H=(0,d.Mz)([G,h.LF],e.Nk),I=(0,d.Mz)([F,h.LF,y.D],z.A),J=(0,d.Mz)([H,y.D,E],e.fb),K=(0,d.Mz)([y.D],e.S5),L=(0,d.Mz)([E],a=>a.filter(A.g)),M=(0,d.Mz)([I,L,i.eC],e.MK),N=(0,d.Mz)([M,h.LF,x.R],e.pM),O=(0,d.Mz)([E],e.IO),P=(0,d.Mz)([H,y.D,O,e.CH,x.R],e.kz),Q=(0,d.Mz)([e.Kr,x.R,w.M],e.P9),R=(0,d.Mz)([Q,x.R],e.Oz),S=(0,d.Mz)([e.gT,x.R,w.M],e.P9),T=(0,d.Mz)([S,x.R],e.q),U=(0,d.Mz)([e.$X,x.R,w.M],e.P9),V=(0,d.Mz)([U,x.R],e.bb),W=(0,d.Mz)([R,V,T],e.yi),X=(0,d.Mz)([y.D,K,N,P,W,f.fz,x.R],e.wL),Y=(0,d.Mz)([y.D,f.fz,H,J,i.eC,x.R,X],e.tP),Z=(0,d.Mz)([Y,y.D,B],e.xp),$=(0,d.Mz)([y.D,Y,Z,x.R],e.g1),_=a=>{var b=(0,x.R)(a),c=(0,w.M)(a);return(0,e.D5)(a,b,c,!1)},aa=(0,d.Mz)([y.D,_],k.I),ab=(0,d.Mz)([y.D,B,$,aa],e.Qn),ac=(0,d.Mz)([f.fz,J,y.D,x.R],e.tF),ad=(0,d.Mz)([f.fz,J,y.D,x.R],e.iv),ae=(0,d.Mz)([f.fz,y.D,B,ab,_,ac,ad,x.R],(a,b,c,d,e,f,h,i)=>{if(b){var{type:k}=b,l=(0,g._L)(a,i);if(d){var m="scaleBand"===c&&d.bandwidth?d.bandwidth()/2:2,n="category"===k&&d.bandwidth?d.bandwidth()/m:0;return(n="angleAxis"===i&&null!=e&&(null==e?void 0:e.length)>=2?2*(0,j.sA)(e[0]-e[1])*n:n,l&&h)?h.map((a,b)=>({coordinate:d(a)+n,value:a,index:b,offset:n})):d.domain().map((a,b)=>({coordinate:d(a)+n,value:f?f[a]:a,index:b,offset:n}))}}}),af=(0,d.Mz)([l.xH,l.Hw,a=>a.tooltip.settings],(a,b,c)=>(0,l.$g)(c.shared,a,b)),ag=a=>a.tooltip.settings.trigger,ah=a=>a.tooltip.settings.defaultIndex,ai=(0,d.Mz)([u.J,af,ag,ah],n.i),aj=(0,d.Mz)([ai,H],o.P),ak=(0,d.Mz)([ae,aj],m.E),al=(0,d.Mz)([ai],a=>{if(a)return a.dataKey}),am=(0,d.Mz)([u.J,af,ag,ah],s.q),an=(0,d.Mz)([q.Lp,q.A$,f.fz,r.HZ,ae,ah,am,t.x],p.o),ao=(0,d.Mz)([ai,an],(a,b)=>null!=a&&a.coordinate?a.coordinate:b),ap=(0,d.Mz)([ai],a=>a.active),aq=(0,d.Mz)([am,aj,h.LF,y.D,ak,t.x,af],v.N),ar=(0,d.Mz)([aq],a=>{if(null!=a)return Array.from(new Set(a.map(a=>a.payload).filter(a=>null!=a)))})},76341:(a,b,c)=>{"use strict";c.d(b,{A:()=>g,_:()=>h}),c(38301);var d=c(53530),e=c(15379),f=c(56998);function g(a){var{legendPayload:b}=a;return(0,f.j)(),(0,d.r)(),null}function h(a){var{legendPayload:b}=a;return(0,f.j)(),(0,f.G)(e.fz),null}c(75959)},76495:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(75309),e=c(79241);function f(a,b,c){var{chartData:f=[]}=b,g=null==c?void 0:c.dataKey,h=new Map;return a.forEach(a=>{var b,c=null!=(b=a.data)?b:f;if(null!=c&&0!==c.length){var i=(0,d.x)(a);c.forEach((b,c)=>{var d,f=null==g?c:String((0,e.kr)(b,g,null)),j=(0,e.kr)(b,a.dataKey,0);Object.assign(d=h.has(f)?h.get(f):{},{[i]:j}),h.set(f,d)})}}),Array.from(h.values())}},77241:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.toPath=function(a){let b=[],c=a.length;if(0===c)return b;let d=0,e="",f="",g=!1;for(46===a.charCodeAt(0)&&(b.push(""),d++);d<c;){let h=a[d];f?"\\"===h&&d+1<c?e+=a[++d]:h===f?f="":e+=h:g?'"'===h||"'"===h?f=h:"]"===h?(g=!1,b.push(e),e=""):e+=h:"["===h?(g=!0,e&&(b.push(e),e="")):"."===h?e&&(b.push(e),e=""):e+=h,d++}return e&&b.push(e),b}},77542:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(15261);b.isArrayLike=function(a){return null!=a&&"function"!=typeof a&&d.isLength(a.length)}},78757:(a,b)=>{"use strict";function c(a){let{widthInt:b,heightInt:c,blurWidth:d,blurHeight:e,blurDataURL:f,objectFit:g}=a,h=d?40*d:b,i=e?40*e:c,j=h&&i?"viewBox='0 0 "+h+" "+i+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+j+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(j?"none":"contain"===g?"xMidYMid":"cover"===g?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+f+"'/%3E%3C/svg%3E"}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"getImageBlurSvg",{enumerable:!0,get:function(){return c}})},79241:(a,b,c)=>{"use strict";c.d(b,{qx:()=>I,IH:()=>H,s0:()=>u,gH:()=>t,SW:()=>O,YB:()=>y,bk:()=>N,Hj:()=>J,DW:()=>F,y2:()=>E,nb:()=>D,PW:()=>w,Mk:()=>G,$8:()=>C,yy:()=>B,Rh:()=>x,GF:()=>K,uM:()=>L,kr:()=>s,r4:()=>M,_L:()=>v,_f:()=>z});var d=c(45608),e=c.n(d),f=c(37177),g=c.n(f);function h(a,b){if((e=a.length)>1)for(var c,d,e,f=1,g=a[b[0]],h=g.length;f<e;++f)for(d=g,g=a[b[f]],c=0;c<h;++c)g[c][1]+=g[c][0]=isNaN(d[c][1])?d[c][0]:d[c][1]}var i=c(72706),j=c(89311);function k(a){for(var b=a.length,c=Array(b);--b>=0;)c[b]=b;return c}function l(a,b){return a[b]}function m(a){let b=[];return b.key=a,b}var n=c(22688),o=c(29160),p=c(60335);function q(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function r(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?q(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):q(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function s(a,b,c){return(0,n.uy)(a)||(0,n.uy)(b)?c:(0,n.vh)(b)?g()(a,b,c):"function"==typeof b?b(a):c}var t=(a,b,c,d,e)=>{var f,g=-1,h=null!=(f=null==b?void 0:b.length)?f:0;if(h<=1||null==a)return 0;if("angleAxis"===d&&null!=e&&1e-6>=Math.abs(Math.abs(e[1]-e[0])-360))for(var i=0;i<h;i++){var j=i>0?c[i-1].coordinate:c[h-1].coordinate,k=c[i].coordinate,l=i>=h-1?c[0].coordinate:c[i+1].coordinate,m=void 0;if((0,n.sA)(k-j)!==(0,n.sA)(l-k)){var o=[];if((0,n.sA)(l-k)===(0,n.sA)(e[1]-e[0])){m=l;var p=k+e[1]-e[0];o[0]=Math.min(p,(p+j)/2),o[1]=Math.max(p,(p+j)/2)}else{m=j;var q=l+e[1]-e[0];o[0]=Math.min(k,(q+k)/2),o[1]=Math.max(k,(q+k)/2)}var r=[Math.min(k,(m+k)/2),Math.max(k,(m+k)/2)];if(a>r[0]&&a<=r[1]||a>=o[0]&&a<=o[1]){({index:g}=c[i]);break}}else{var s=Math.min(j,l),t=Math.max(j,l);if(a>(s+k)/2&&a<=(t+k)/2){({index:g}=c[i]);break}}}else if(b){for(var u=0;u<h;u++)if(0===u&&a<=(b[u].coordinate+b[u+1].coordinate)/2||u>0&&u<h-1&&a>(b[u].coordinate+b[u-1].coordinate)/2&&a<=(b[u].coordinate+b[u+1].coordinate)/2||u===h-1&&a>(b[u].coordinate+b[u-1].coordinate)/2){({index:g}=b[u]);break}}return g},u=(a,b,c)=>{if(b&&c){var{width:d,height:e}=c,{align:f,verticalAlign:g,layout:h}=b;if(("vertical"===h||"horizontal"===h&&"middle"===g)&&"center"!==f&&(0,n.Et)(a[f]))return r(r({},a),{},{[f]:a[f]+(d||0)});if(("horizontal"===h||"vertical"===h&&"center"===f)&&"middle"!==g&&(0,n.Et)(a[g]))return r(r({},a),{},{[g]:a[g]+(e||0)})}return a},v=(a,b)=>"horizontal"===a&&"xAxis"===b||"vertical"===a&&"yAxis"===b||"centric"===a&&"angleAxis"===b||"radial"===a&&"radiusAxis"===b,w=(a,b,c,d)=>{if(d)return a.map(a=>a.coordinate);var e,f,g=a.map(a=>(a.coordinate===b&&(e=!0),a.coordinate===c&&(f=!0),a.coordinate));return e||g.push(b),f||g.push(c),g},x=(a,b,c)=>{if(!a)return null;var{duplicateDomain:d,type:e,range:f,scale:g,realScaleType:h,isCategorical:i,categoricalDomain:j,tickCount:k,ticks:l,niceTicks:m,axisType:o}=a;if(!g)return null;var p="scaleBand"===h&&g.bandwidth?g.bandwidth()/2:2,q=(b||c)&&"category"===e&&g.bandwidth?g.bandwidth()/p:0;return(q="angleAxis"===o&&f&&f.length>=2?2*(0,n.sA)(f[0]-f[1])*q:q,b&&(l||m))?(l||m||[]).map((a,b)=>({coordinate:g(d?d.indexOf(a):a)+q,value:a,offset:q,index:b})).filter(a=>!(0,n.M8)(a.coordinate)):i&&j?j.map((a,b)=>({coordinate:g(a)+q,value:a,index:b,offset:q})):g.ticks&&!c&&null!=k?g.ticks(k).map((a,b)=>({coordinate:g(a)+q,value:a,offset:q,index:b})):g.domain().map((a,b)=>({coordinate:g(a)+q,value:d?d[a]:a,index:b,offset:q}))},y=a=>{var b=a.domain();if(b&&!(b.length<=2)){var c=b.length,d=a.range(),e=Math.min(d[0],d[1])-1e-4,f=Math.max(d[0],d[1])+1e-4,g=a(b[0]),h=a(b[c-1]);(g<e||g>f||h<e||h>f)&&a.domain([b[0],b[c-1]])}},z=(a,b)=>{if(!b||2!==b.length||!(0,n.Et)(b[0])||!(0,n.Et)(b[1]))return a;var c=Math.min(b[0],b[1]),d=Math.max(b[0],b[1]),e=[a[0],a[1]];return(!(0,n.Et)(a[0])||a[0]<c)&&(e[0]=c),(!(0,n.Et)(a[1])||a[1]>d)&&(e[1]=d),e[0]>d&&(e[0]=d),e[1]<c&&(e[1]=c),e},A={sign:a=>{var b=a.length;if(!(b<=0))for(var c=0,d=a[0].length;c<d;++c)for(var e=0,f=0,g=0;g<b;++g){var h=(0,n.M8)(a[g][c][1])?a[g][c][0]:a[g][c][1];h>=0?(a[g][c][0]=e,a[g][c][1]=e+h,e=a[g][c][1]):(a[g][c][0]=f,a[g][c][1]=f+h,f=a[g][c][1])}},expand:function(a,b){if((d=a.length)>0){for(var c,d,e,f=0,g=a[0].length;f<g;++f){for(e=c=0;c<d;++c)e+=a[c][f][1]||0;if(e)for(c=0;c<d;++c)a[c][f][1]/=e}h(a,b)}},none:h,silhouette:function(a,b){if((c=a.length)>0){for(var c,d=0,e=a[b[0]],f=e.length;d<f;++d){for(var g=0,i=0;g<c;++g)i+=a[g][d][1]||0;e[d][1]+=e[d][0]=-i/2}h(a,b)}},wiggle:function(a,b){if((e=a.length)>0&&(d=(c=a[b[0]]).length)>0){for(var c,d,e,f=0,g=1;g<d;++g){for(var i=0,j=0,k=0;i<e;++i){for(var l=a[b[i]],m=l[g][1]||0,n=(m-(l[g-1][1]||0))/2,o=0;o<i;++o){var p=a[b[o]];n+=(p[g][1]||0)-(p[g-1][1]||0)}j+=m,k+=n*m}c[g-1][1]+=c[g-1][0]=f,j&&(f-=k/j)}c[g-1][1]+=c[g-1][0]=f,h(a,b)}},positive:a=>{var b=a.length;if(!(b<=0))for(var c=0,d=a[0].length;c<d;++c)for(var e=0,f=0;f<b;++f){var g=(0,n.M8)(a[f][c][1])?a[f][c][0]:a[f][c][1];g>=0?(a[f][c][0]=e,a[f][c][1]=e+g,e=a[f][c][1]):(a[f][c][0]=0,a[f][c][1]=0)}}},B=(a,b,c)=>{var d=A[c];return(function(){var a=(0,j.A)([]),b=k,c=h,d=l;function e(e){var f,g,h=Array.from(a.apply(this,arguments),m),j=h.length,k=-1;for(let a of e)for(f=0,++k;f<j;++f)(h[f][k]=[0,+d(a,h[f].key,k,e)]).data=a;for(f=0,g=(0,i.A)(b(h));f<j;++f)h[g[f]].index=f;return c(h,g),h}return e.keys=function(b){return arguments.length?(a="function"==typeof b?b:(0,j.A)(Array.from(b)),e):a},e.value=function(a){return arguments.length?(d="function"==typeof a?a:(0,j.A)(+a),e):d},e.order=function(a){return arguments.length?(b=null==a?k:"function"==typeof a?a:(0,j.A)(Array.from(a)),e):b},e.offset=function(a){return arguments.length?(c=null==a?h:a,e):c},e})().keys(b).value((a,b)=>+s(a,b,0)).order(k).offset(d)(a)};function C(a){return null==a?void 0:String(a)}function D(a){var{axis:b,ticks:c,bandSize:d,entry:e,index:f,dataKey:g}=a;if("category"===b.type){if(!b.allowDuplicatedCategory&&b.dataKey&&!(0,n.uy)(e[b.dataKey])){var h=(0,n.eP)(c,"value",e[b.dataKey]);if(h)return h.coordinate+d/2}return c[f]?c[f].coordinate+d/2:null}var i=s(e,(0,n.uy)(g)?b.dataKey:g);return(0,n.uy)(i)?null:b.scale(i)}var E=a=>{var{axis:b,ticks:c,offset:d,bandSize:e,entry:f,index:g}=a;if("category"===b.type)return c[g]?c[g].coordinate+d:null;var h=s(f,b.dataKey,b.scale.domain()[g]);return(0,n.uy)(h)?null:b.scale(h)-e/2+d},F=a=>{var{numericAxis:b}=a,c=b.scale.domain();if("number"===b.type){var d=Math.min(c[0],c[1]),e=Math.max(c[0],c[1]);return d<=0&&e>=0?0:e<0?e:d}return c[0]},G=(a,b,c)=>{if(null!=a)return(a=>[a[0]===1/0?0:a[0],a[1]===-1/0?0:a[1]])(Object.keys(a).reduce((d,e)=>{var{stackedData:f}=a[e],g=f.reduce((a,d)=>{var e=(a=>{var b=a.flat(2).filter(n.Et);return[Math.min(...b),Math.max(...b)]})((0,p.v)(d,b,c));return[Math.min(a[0],e[0]),Math.max(a[1],e[1])]},[1/0,-1/0]);return[Math.min(g[0],d[0]),Math.max(g[1],d[1])]},[1/0,-1/0]))},H=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,I=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,J=(a,b,c)=>{if(a&&a.scale&&a.scale.bandwidth){var d=a.scale.bandwidth();if(!c||d>0)return d}if(a&&b&&b.length>=2){for(var f=e()(b,a=>a.coordinate),g=1/0,h=1,i=f.length;h<i;h++){var j=f[h],k=f[h-1];g=Math.min((j.coordinate||0)-(k.coordinate||0),g)}return g===1/0?0:g}return c?void 0:0};function K(a){var{tooltipEntrySettings:b,dataKey:c,payload:d,value:e,name:f}=a;return r(r({},b),{},{dataKey:c,payload:d,value:e,name:f})}function L(a,b){return a?String(a):"string"==typeof b?b:void 0}function M(a,b,c,d,e){return"horizontal"===c||"vertical"===c?a>=e.left&&a<=e.left+e.width&&b>=e.top&&b<=e.top+e.height?{x:a,y:b}:null:d?(0,o.yy)({x:a,y:b},d):null}var N=(a,b,c,d)=>{var e=b.find(a=>a&&a.index===c);if(e){if("horizontal"===a)return{x:e.coordinate,y:d.y};if("vertical"===a)return{x:d.x,y:e.coordinate};if("centric"===a){var f=e.coordinate,{radius:g}=d;return r(r(r({},d),(0,o.IZ)(d.cx,d.cy,g,f)),{},{angle:f,radius:g})}var h=e.coordinate,{angle:i}=d;return r(r(r({},d),(0,o.IZ)(d.cx,d.cy,h,i)),{},{angle:i,radius:h})}return{x:0,y:0}},O=(a,b)=>"horizontal"===b?a.x:"vertical"===b?a.y:"centric"===b?a.angle:a.radius},81249:(a,b,c)=>{"use strict";c.d(b,{E:()=>B});var d=c(38301),e=c(43249),f=c(22688),g=c(4702),h=c(72677),i=c(98700),j=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,k=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,l=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,m=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,n={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},o=Object.keys(n);class p{static parse(a){var b,[,c,d]=null!=(b=m.exec(a))?b:[];return new p(parseFloat(c),null!=d?d:"")}constructor(a,b){this.num=a,this.unit=b,this.num=a,this.unit=b,(0,f.M8)(a)&&(this.unit=""),""===b||l.test(b)||(this.num=NaN,this.unit=""),o.includes(b)&&(this.num=a*n[b],this.unit="px")}add(a){return this.unit!==a.unit?new p(NaN,""):new p(this.num+a.num,this.unit)}subtract(a){return this.unit!==a.unit?new p(NaN,""):new p(this.num-a.num,this.unit)}multiply(a){return""!==this.unit&&""!==a.unit&&this.unit!==a.unit?new p(NaN,""):new p(this.num*a.num,this.unit||a.unit)}divide(a){return""!==this.unit&&""!==a.unit&&this.unit!==a.unit?new p(NaN,""):new p(this.num/a.num,this.unit||a.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return(0,f.M8)(this.num)}}function q(a){if(a.includes("NaN"))return"NaN";for(var b=a;b.includes("*")||b.includes("/");){var c,[,d,e,f]=null!=(c=j.exec(b))?c:[],g=p.parse(null!=d?d:""),h=p.parse(null!=f?f:""),i="*"===e?g.multiply(h):g.divide(h);if(i.isNaN())return"NaN";b=b.replace(j,i.toString())}for(;b.includes("+")||/.-\d+(?:\.\d+)?/.test(b);){var l,[,m,n,o]=null!=(l=k.exec(b))?l:[],q=p.parse(null!=m?m:""),r=p.parse(null!=o?o:""),s="+"===n?q.add(r):q.subtract(r);if(s.isNaN())return"NaN";b=b.replace(k,s.toString())}return b}var r=/\(([^()]*)\)/;function s(a){var b=function(a){try{var b;return b=a.replace(/\s+/g,""),b=function(a){for(var b,c=a;null!=(b=r.exec(c));){var[,d]=b;c=c.replace(r,q(d))}return c}(b),b=q(b)}catch(a){return"NaN"}}(a.slice(5,-1));return"NaN"===b?"":b}var t=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],u=["dx","dy","angle","className","breakAll"];function v(){return(v=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function w(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var x=/[ \f\n\r\t\v\u2028\u2029]+/,y=a=>{var{children:b,breakAll:c,style:d}=a;try{var e=[];(0,f.uy)(b)||(e=c?b.toString().split(""):b.toString().split(x));var g=e.map(a=>({word:a,width:(0,i.Pu)(a,d).width})),h=c?0:(0,i.Pu)("\xa0",d).width;return{wordsWithComputedWidth:g,spaceWidth:h}}catch(a){return null}},z=a=>[{words:(0,f.uy)(a)?[]:a.toString().split(x)}],A="#808080",B=(0,d.forwardRef)((a,b)=>{var c,{x:i=0,y:j=0,lineHeight:k="1em",capHeight:l="0.71em",scaleToFit:m=!1,textAnchor:n="start",verticalAnchor:o="end",fill:p=A}=a,q=w(a,t),r=(0,d.useMemo)(()=>(a=>{var{width:b,scaleToFit:c,children:d,style:e,breakAll:h,maxLines:i}=a;if((b||c)&&!g.m.isSsr){var j=y({breakAll:h,children:d,style:e});if(!j)return z(d);var{wordsWithComputedWidth:k,spaceWidth:l}=j;return((a,b,c,d,e)=>{var g,{maxLines:h,children:i,style:j,breakAll:k}=a,l=(0,f.Et)(h),m=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return a.reduce((a,b)=>{var{word:f,width:g}=b,h=a[a.length-1];return h&&(null==d||e||h.width+g+c<Number(d))?(h.words.push(f),h.width+=g+c):a.push({words:[f],width:g}),a},[])},n=m(b),o=a=>a.reduce((a,b)=>a.width>b.width?a:b);if(!l||e||!(n.length>h||o(n).width>Number(d)))return n;for(var p=a=>{var b=m(y({breakAll:k,style:j,children:i.slice(0,a)+"…"}).wordsWithComputedWidth);return[b.length>h||o(b).width>Number(d),b]},q=0,r=i.length-1,s=0;q<=r&&s<=i.length-1;){var t=Math.floor((q+r)/2),[u,v]=p(t-1),[w]=p(t);if(u||w||(q=t+1),u&&w&&(r=t-1),!u&&w){g=v;break}s++}return g||n})({breakAll:h,children:d,maxLines:i,style:e},k,l,b,c)}return z(d)})({breakAll:q.breakAll,children:q.children,maxLines:q.maxLines,scaleToFit:m,style:q.style,width:q.width}),[q.breakAll,q.children,q.maxLines,m,q.style,q.width]),{dx:x,dy:B,angle:C,className:D,breakAll:E}=q,F=w(q,u);if(!(0,f.vh)(i)||!(0,f.vh)(j))return null;var G=i+((0,f.Et)(x)?x:0),H=j+((0,f.Et)(B)?B:0);switch(o){case"start":c=s("calc(".concat(l,")"));break;case"middle":c=s("calc(".concat((r.length-1)/2," * -").concat(k," + (").concat(l," / 2))"));break;default:c=s("calc(".concat(r.length-1," * -").concat(k,")"))}var I=[];if(m){var J=r[0].width,{width:K}=q;I.push("scale(".concat((0,f.Et)(K)?K/J:1,")"))}return C&&I.push("rotate(".concat(C,", ").concat(G,", ").concat(H,")")),I.length&&(F.transform=I.join(" ")),d.createElement("text",v({},(0,h.J9)(F,!0),{ref:b,x:G,y:H,className:(0,e.$)("recharts-text",D),textAnchor:n,fill:p.includes("url")?A:p}),r.map((a,b)=>{var e=a.words.join(E?"":" ");return d.createElement("tspan",{x:G,dy:0===b?c:k,key:"".concat(e,"-").concat(b)},e)}))});B.displayName="Text"},81578:(a,b,c)=>{"use strict";a.exports=c(56796).vendored.contexts.AmpContext},81851:(a,b)=>{"use strict";var c="function"==typeof Symbol&&Symbol.for,d=c?Symbol.for("react.element"):60103,e=c?Symbol.for("react.portal"):60106,f=c?Symbol.for("react.fragment"):60107,g=c?Symbol.for("react.strict_mode"):60108,h=c?Symbol.for("react.profiler"):60114,i=c?Symbol.for("react.provider"):60109,j=c?Symbol.for("react.context"):60110,k=c?Symbol.for("react.async_mode"):60111,l=c?Symbol.for("react.concurrent_mode"):60111,m=c?Symbol.for("react.forward_ref"):60112,n=c?Symbol.for("react.suspense"):60113,o=(c&&Symbol.for("react.suspense_list"),c?Symbol.for("react.memo"):60115),p=c?Symbol.for("react.lazy"):60116;function q(a){if("object"==typeof a&&null!==a){var b=a.$$typeof;switch(b){case d:switch(a=a.type){case k:case l:case f:case h:case g:case n:return a;default:switch(a=a&&a.$$typeof){case j:case m:case p:case o:case i:return a;default:return b}}case e:return b}}}c&&Symbol.for("react.block"),c&&Symbol.for("react.fundamental"),c&&Symbol.for("react.responder"),c&&Symbol.for("react.scope");b.isFragment=function(a){return q(a)===f}},83790:(a,b,c)=>{"use strict";c.d(b,{f:()=>d});var d=a=>null;d.displayName="Cell"},84224:(a,b,c)=>{"use strict";c.d(b,{yl:()=>i});var d=(a,b)=>[0,3*a,3*b-6*a,3*a-3*b+1],e=(a,b)=>a.map((a,c)=>a*b**c).reduce((a,b)=>a+b),f=(a,b)=>c=>e(d(a,b),c),g=function(){let a,b;for(var c,g,h,i,j=arguments.length,k=Array(j),l=0;l<j;l++)k[l]=arguments[l];if(1===k.length)switch(k[0]){case"linear":[c,h,g,i]=[0,0,1,1];break;case"ease":[c,h,g,i]=[.25,.1,.25,1];break;case"ease-in":[c,h,g,i]=[.42,0,1,1];break;case"ease-out":[c,h,g,i]=[.42,0,.58,1];break;case"ease-in-out":[c,h,g,i]=[0,0,.58,1];break;default:var m=k[0].split("(");"cubic-bezier"===m[0]&&4===m[1].split(")")[0].split(",").length&&([c,h,g,i]=m[1].split(")")[0].split(",").map(a=>parseFloat(a)))}else 4===k.length&&([c,h,g,i]=k);var n=f(c,g),o=f(h,i),p=(a=c,b=g,c=>e([...d(a,b).map((a,b)=>a*b).slice(1),0],c)),q=a=>a>1?1:a<0?0:a,r=a=>{for(var b=a>1?1:a,c=b,d=0;d<8;++d){var e=n(c)-b,f=p(c);if(1e-4>Math.abs(e-b)||f<1e-4)break;c=q(c-e/f)}return o(c)};return r.isStepper=!1,r},h=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:b=100,damping:c=8,dt:d=17}=a,e=(a,e,f)=>{var g=f+(-(a-e)*b-f*c)*d/1e3,h=f*d/1e3+a;return 1e-4>Math.abs(h-e)&&1e-4>Math.abs(g)?[e,0]:[h,g]};return e.isStepper=!0,e.dt=d,e},i=a=>{if("string"==typeof a)switch(a){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return g(a);case"spring":return h();default:if("cubic-bezier"===a.split("(")[0])return g(a)}return"function"==typeof a?a:null}},85964:a=>{"use strict";var b=Object.prototype.hasOwnProperty,c="~";function d(){}function e(a,b,c){this.fn=a,this.context=b,this.once=c||!1}function f(a,b,d,f,g){if("function"!=typeof d)throw TypeError("The listener must be a function");var h=new e(d,f||a,g),i=c?c+b:b;return a._events[i]?a._events[i].fn?a._events[i]=[a._events[i],h]:a._events[i].push(h):(a._events[i]=h,a._eventsCount++),a}function g(a,b){0==--a._eventsCount?a._events=new d:delete a._events[b]}function h(){this._events=new d,this._eventsCount=0}Object.create&&(d.prototype=Object.create(null),new d().__proto__||(c=!1)),h.prototype.eventNames=function(){var a,d,e=[];if(0===this._eventsCount)return e;for(d in a=this._events)b.call(a,d)&&e.push(c?d.slice(1):d);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(a)):e},h.prototype.listeners=function(a){var b=c?c+a:a,d=this._events[b];if(!d)return[];if(d.fn)return[d.fn];for(var e=0,f=d.length,g=Array(f);e<f;e++)g[e]=d[e].fn;return g},h.prototype.listenerCount=function(a){var b=c?c+a:a,d=this._events[b];return d?d.fn?1:d.length:0},h.prototype.emit=function(a,b,d,e,f,g){var h=c?c+a:a;if(!this._events[h])return!1;var i,j,k=this._events[h],l=arguments.length;if(k.fn){switch(k.once&&this.removeListener(a,k.fn,void 0,!0),l){case 1:return k.fn.call(k.context),!0;case 2:return k.fn.call(k.context,b),!0;case 3:return k.fn.call(k.context,b,d),!0;case 4:return k.fn.call(k.context,b,d,e),!0;case 5:return k.fn.call(k.context,b,d,e,f),!0;case 6:return k.fn.call(k.context,b,d,e,f,g),!0}for(j=1,i=Array(l-1);j<l;j++)i[j-1]=arguments[j];k.fn.apply(k.context,i)}else{var m,n=k.length;for(j=0;j<n;j++)switch(k[j].once&&this.removeListener(a,k[j].fn,void 0,!0),l){case 1:k[j].fn.call(k[j].context);break;case 2:k[j].fn.call(k[j].context,b);break;case 3:k[j].fn.call(k[j].context,b,d);break;case 4:k[j].fn.call(k[j].context,b,d,e);break;default:if(!i)for(m=1,i=Array(l-1);m<l;m++)i[m-1]=arguments[m];k[j].fn.apply(k[j].context,i)}}return!0},h.prototype.on=function(a,b,c){return f(this,a,b,c,!1)},h.prototype.once=function(a,b,c){return f(this,a,b,c,!0)},h.prototype.removeListener=function(a,b,d,e){var f=c?c+a:a;if(!this._events[f])return this;if(!b)return g(this,f),this;var h=this._events[f];if(h.fn)h.fn!==b||e&&!h.once||d&&h.context!==d||g(this,f);else{for(var i=0,j=[],k=h.length;i<k;i++)(h[i].fn!==b||e&&!h[i].once||d&&h[i].context!==d)&&j.push(h[i]);j.length?this._events[f]=1===j.length?j[0]:j:g(this,f)}return this},h.prototype.removeAllListeners=function(a){var b;return a?(b=c?c+a:a,this._events[b]&&g(this,b)):(this._events=new d,this._eventsCount=0),this},h.prototype.off=h.prototype.removeListener,h.prototype.addListener=h.prototype.on,h.prefixed=c,h.EventEmitter=h,a.exports=h},86493:(a,b,c)=>{"use strict";c.d(b,{r:()=>u});var d=c(38301),e=c(29072),f=c(35538),g=c(18189),h=c(20853),i=c(11267),j=c(56998);function k(a){return(0,j.j)(),null}c(74030);var l=c(20888),m=c(65764),n=c(53053),o=["width","height","layout"];function p(){return(p=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var q={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},r=(0,d.forwardRef)(function(a,b){var c,e=(0,m.e)(a.categoricalChartProps,q),{width:j,height:r,layout:s}=e,t=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(e,o);if(!(0,n.F)(j)||!(0,n.F)(r))return null;var{chartName:u,defaultTooltipEventType:v,validateTooltipEventTypes:w,tooltipPayloadSearcher:x}=a;return d.createElement(f.J,{preloadedState:{options:{chartName:u,defaultTooltipEventType:v,validateTooltipEventTypes:w,tooltipPayloadSearcher:x,eventEmitter:void 0}},reduxStoreName:null!=(c=e.id)?c:u},d.createElement(g.TK,{chartData:e.data}),d.createElement(h.s,{width:j,height:r,layout:s,margin:e.margin}),d.createElement(i.p,{accessibilityLayer:e.accessibilityLayer,barCategoryGap:e.barCategoryGap,maxBarSize:e.maxBarSize,stackOffset:e.stackOffset,barGap:e.barGap,barSize:e.barSize,syncId:e.syncId,syncMethod:e.syncMethod,className:e.className}),d.createElement(k,{cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius}),d.createElement(l.L,p({width:j,height:r},t,{ref:b})))}),s=["item"],t={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},u=(0,d.forwardRef)((a,b)=>{var c=(0,m.e)(a,t);return d.createElement(r,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:s,tooltipPayloadSearcher:e.uN,categoricalChartProps:c,ref:b})})},86773:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},86852:(a,b,c)=>{"use strict";c.d(b,{y:()=>S});var d=c(38301),e=c(61526),f=c.n(e),g=c(43249),h=c(72677),i=c(65764),j=c(23541),k=c.n(j),l=c(84224),m=c(17906),n=c(63409),o=c(57682),p=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function q(){return(q=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function r(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function s(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?r(Object(c),!0).forEach(function(b){t(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):r(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function t(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class u extends d.PureComponent{constructor(a,b){super(a,b),t(this,"mounted",!1),t(this,"manager",void 0),t(this,"stopJSAnimation",null),t(this,"unSubscribe",null);var{isActive:c,attributeName:d,from:e,to:f,children:g,duration:h,animationManager:i}=this.props;if(this.manager=i,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!c||h<=0){this.state={style:{}},"function"==typeof g&&(this.state={style:f});return}if(e){if("function"==typeof g){this.state={style:e};return}this.state={style:d?{[d]:e}:e}}else this.state={style:{}}}componentDidMount(){var{isActive:a,canBegin:b}=this.props;this.mounted=!0,a&&b&&this.runAnimation(this.props)}componentDidUpdate(a){var{isActive:b,canBegin:c,attributeName:d,shouldReAnimate:e,to:f,from:g}=this.props,{style:h}=this.state;if(c){if(!b){this.state&&h&&(d&&h[d]!==f||!d&&h!==f)&&this.setState({style:d?{[d]:f}:f});return}if(!k()(a.to,f)||!a.canBegin||!a.isActive){var i=!a.canBegin||!a.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var j=i||e?g:a.to;this.state&&h&&(d&&h[d]!==j||!d&&h!==j)&&this.setState({style:d?{[d]:j}:j}),this.runAnimation(s(s({},this.props),{},{from:j,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:a}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),a&&a()}handleStyleChange(a){this.changeStyle(a)}changeStyle(a){this.mounted&&this.setState({style:a})}runJSAnimation(a){var{from:b,to:c,duration:d,easing:e,begin:f,onAnimationEnd:g,onAnimationStart:h}=a,i=(0,m.A)(b,c,(0,l.yl)(e),d,this.changeStyle,this.manager.getTimeoutController()),j=()=>{this.stopJSAnimation=i()};this.manager.start([h,f,j,d,g])}runAnimation(a){var{begin:b,duration:c,attributeName:d,to:e,easing:f,onAnimationStart:g,onAnimationEnd:h,children:i}=a;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof f||"function"==typeof i||"spring"===f)return void this.runJSAnimation(a);var j=d?{[d]:e}:e,k=(0,n.dl)(Object.keys(j),c,f);this.manager.start([g,b,s(s({},j),{},{transition:k}),c,h])}render(){var a=this.props,{children:b,begin:c,duration:e,attributeName:f,easing:g,isActive:h,from:i,to:j,canBegin:k,onAnimationEnd:l,shouldReAnimate:m,onAnimationReStart:n,animationManager:o}=a,q=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,p),r=d.Children.count(b),t=this.state.style;if("function"==typeof b)return b(t);if(!h||0===r||e<=0)return b;var u=a=>{var{style:b={},className:c}=a.props;return(0,d.cloneElement)(a,s(s({},q),{},{style:s(s({},b),t),className:c}))};return 1===r?u(d.Children.only(b)):d.createElement("div",null,d.Children.map(b,a=>u(a)))}}function v(a){var b,c=(0,o.L)(null!=(b=a.attributeName)?b:Object.keys(a.to).join(","),a.animationManager);return d.createElement(u,q({},a,{animationManager:c}))}function w(){return(w=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}t(u,"displayName","Animate"),t(u,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var x=(a,b,c,d,e)=>{var f,g=Math.min(Math.abs(c)/2,Math.abs(d)/2),h=d>=0?1:-1,i=c>=0?1:-1,j=+(d>=0&&c>=0||d<0&&c<0);if(g>0&&e instanceof Array){for(var k=[0,0,0,0],l=0;l<4;l++)k[l]=e[l]>g?g:e[l];f="M".concat(a,",").concat(b+h*k[0]),k[0]>0&&(f+="A ".concat(k[0],",").concat(k[0],",0,0,").concat(j,",").concat(a+i*k[0],",").concat(b)),f+="L ".concat(a+c-i*k[1],",").concat(b),k[1]>0&&(f+="A ".concat(k[1],",").concat(k[1],",0,0,").concat(j,",\n        ").concat(a+c,",").concat(b+h*k[1])),f+="L ".concat(a+c,",").concat(b+d-h*k[2]),k[2]>0&&(f+="A ".concat(k[2],",").concat(k[2],",0,0,").concat(j,",\n        ").concat(a+c-i*k[2],",").concat(b+d)),f+="L ".concat(a+i*k[3],",").concat(b+d),k[3]>0&&(f+="A ".concat(k[3],",").concat(k[3],",0,0,").concat(j,",\n        ").concat(a,",").concat(b+d-h*k[3])),f+="Z"}else if(g>0&&e===+e&&e>0){var m=Math.min(g,e);f="M ".concat(a,",").concat(b+h*m,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+i*m,",").concat(b,"\n            L ").concat(a+c-i*m,",").concat(b,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+c,",").concat(b+h*m,"\n            L ").concat(a+c,",").concat(b+d-h*m,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+c-i*m,",").concat(b+d,"\n            L ").concat(a+i*m,",").concat(b+d,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a,",").concat(b+d-h*m," Z")}else f="M ".concat(a,",").concat(b," h ").concat(c," v ").concat(d," h ").concat(-c," Z");return f},y={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},z=a=>{var b=(0,i.e)(a,y),c=(0,d.useRef)(null),[e,f]=(0,d.useState)(-1);(0,d.useEffect)(()=>{if(c.current&&c.current.getTotalLength)try{var a=c.current.getTotalLength();a&&f(a)}catch(a){}},[]);var{x:j,y:k,width:l,height:m,radius:n,className:o}=b,{animationEasing:p,animationDuration:q,animationBegin:r,isAnimationActive:s,isUpdateAnimationActive:t}=b;if(j!==+j||k!==+k||l!==+l||m!==+m||0===l||0===m)return null;var u=(0,g.$)("recharts-rectangle",o);return t?d.createElement(v,{canBegin:e>0,from:{width:l,height:m,x:j,y:k},to:{width:l,height:m,x:j,y:k},duration:q,animationEasing:p,isActive:t},a=>{var{width:f,height:g,x:i,y:j}=a;return d.createElement(v,{canBegin:e>0,from:"0px ".concat(-1===e?1:e,"px"),to:"".concat(e,"px 0px"),attributeName:"strokeDasharray",begin:r,duration:q,isActive:s,easing:p},d.createElement("path",w({},(0,h.J9)(b,!0),{className:u,d:x(i,j,f,g,n),ref:c})))}):d.createElement("path",w({},(0,h.J9)(b,!0),{className:u,d:x(j,k,l,m,n)}))};function A(){return(A=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var B=(a,b,c,d,e)=>{var f=c-d;return"M ".concat(a,",").concat(b)+"L ".concat(a+c,",").concat(b)+"L ".concat(a+c-f/2,",").concat(b+e)+"L ".concat(a+c-f/2-d,",").concat(b+e)+"L ".concat(a,",").concat(b," Z")},C={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},D=a=>{var b=(0,i.e)(a,C),c=(0,d.useRef)(),[e,f]=(0,d.useState)(-1);(0,d.useEffect)(()=>{if(c.current&&c.current.getTotalLength)try{var a=c.current.getTotalLength();a&&f(a)}catch(a){}},[]);var{x:j,y:k,upperWidth:l,lowerWidth:m,height:n,className:o}=b,{animationEasing:p,animationDuration:q,animationBegin:r,isUpdateAnimationActive:s}=b;if(j!==+j||k!==+k||l!==+l||m!==+m||n!==+n||0===l&&0===m||0===n)return null;var t=(0,g.$)("recharts-trapezoid",o);return s?d.createElement(v,{canBegin:e>0,from:{upperWidth:0,lowerWidth:0,height:n,x:j,y:k},to:{upperWidth:l,lowerWidth:m,height:n,x:j,y:k},duration:q,animationEasing:p,isActive:s},a=>{var{upperWidth:f,lowerWidth:g,height:i,x:j,y:k}=a;return d.createElement(v,{canBegin:e>0,from:"0px ".concat(-1===e?1:e,"px"),to:"".concat(e,"px 0px"),attributeName:"strokeDasharray",begin:r,duration:q,easing:p},d.createElement("path",A({},(0,h.J9)(b,!0),{className:t,d:B(j,k,f,g,i),ref:c})))}):d.createElement("g",null,d.createElement("path",A({},(0,h.J9)(b,!0),{className:t,d:B(j,k,l,m,n)})))},E=c(29160),F=c(22688);function G(){return(G=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var H=a=>{var{cx:b,cy:c,radius:d,angle:e,sign:f,isExternal:g,cornerRadius:h,cornerIsExternal:i}=a,j=h*(g?1:-1)+d,k=Math.asin(h/j)/E.Kg,l=i?e:e+f*k,m=(0,E.IZ)(b,c,j,l);return{center:m,circleTangency:(0,E.IZ)(b,c,d,l),lineTangency:(0,E.IZ)(b,c,j*Math.cos(k*E.Kg),i?e-f*k:e),theta:k}},I=a=>{var{cx:b,cy:c,innerRadius:d,outerRadius:e,startAngle:f,endAngle:g}=a,h=((a,b)=>(0,F.sA)(b-a)*Math.min(Math.abs(b-a),359.999))(f,g),i=f+h,j=(0,E.IZ)(b,c,e,f),k=(0,E.IZ)(b,c,e,i),l="M ".concat(j.x,",").concat(j.y,"\n    A ").concat(e,",").concat(e,",0,\n    ").concat(+(Math.abs(h)>180),",").concat(+(f>i),",\n    ").concat(k.x,",").concat(k.y,"\n  ");if(d>0){var m=(0,E.IZ)(b,c,d,f),n=(0,E.IZ)(b,c,d,i);l+="L ".concat(n.x,",").concat(n.y,"\n            A ").concat(d,",").concat(d,",0,\n            ").concat(+(Math.abs(h)>180),",").concat(+(f<=i),",\n            ").concat(m.x,",").concat(m.y," Z")}else l+="L ".concat(b,",").concat(c," Z");return l},J={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},K=a=>{var b,c=(0,i.e)(a,J),{cx:e,cy:f,innerRadius:j,outerRadius:k,cornerRadius:l,forceCornerRadius:m,cornerIsExternal:n,startAngle:o,endAngle:p,className:q}=c;if(k<j||o===p)return null;var r=(0,g.$)("recharts-sector",q),s=k-j,t=(0,F.F4)(l,s,0,!0);return b=t>0&&360>Math.abs(o-p)?(a=>{var{cx:b,cy:c,innerRadius:d,outerRadius:e,cornerRadius:f,forceCornerRadius:g,cornerIsExternal:h,startAngle:i,endAngle:j}=a,k=(0,F.sA)(j-i),{circleTangency:l,lineTangency:m,theta:n}=H({cx:b,cy:c,radius:e,angle:i,sign:k,cornerRadius:f,cornerIsExternal:h}),{circleTangency:o,lineTangency:p,theta:q}=H({cx:b,cy:c,radius:e,angle:j,sign:-k,cornerRadius:f,cornerIsExternal:h}),r=h?Math.abs(i-j):Math.abs(i-j)-n-q;if(r<0)return g?"M ".concat(m.x,",").concat(m.y,"\n        a").concat(f,",").concat(f,",0,0,1,").concat(2*f,",0\n        a").concat(f,",").concat(f,",0,0,1,").concat(-(2*f),",0\n      "):I({cx:b,cy:c,innerRadius:d,outerRadius:e,startAngle:i,endAngle:j});var s="M ".concat(m.x,",").concat(m.y,"\n    A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(l.x,",").concat(l.y,"\n    A").concat(e,",").concat(e,",0,").concat(+(r>180),",").concat(+(k<0),",").concat(o.x,",").concat(o.y,"\n    A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(p.x,",").concat(p.y,"\n  ");if(d>0){var{circleTangency:t,lineTangency:u,theta:v}=H({cx:b,cy:c,radius:d,angle:i,sign:k,isExternal:!0,cornerRadius:f,cornerIsExternal:h}),{circleTangency:w,lineTangency:x,theta:y}=H({cx:b,cy:c,radius:d,angle:j,sign:-k,isExternal:!0,cornerRadius:f,cornerIsExternal:h}),z=h?Math.abs(i-j):Math.abs(i-j)-v-y;if(z<0&&0===f)return"".concat(s,"L").concat(b,",").concat(c,"Z");s+="L".concat(x.x,",").concat(x.y,"\n      A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(w.x,",").concat(w.y,"\n      A").concat(d,",").concat(d,",0,").concat(+(z>180),",").concat(+(k>0),",").concat(t.x,",").concat(t.y,"\n      A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(u.x,",").concat(u.y,"Z")}else s+="L".concat(b,",").concat(c,"Z");return s})({cx:e,cy:f,innerRadius:j,outerRadius:k,cornerRadius:Math.min(t,s/2),forceCornerRadius:m,cornerIsExternal:n,startAngle:o,endAngle:p}):I({cx:e,cy:f,innerRadius:j,outerRadius:k,startAngle:o,endAngle:p}),d.createElement("path",G({},(0,h.J9)(c,!0),{className:r,d:b}))},L=c(55413),M=c(97652),N=["option","shapeType","propTransformer","activeClassName","isActive"];function O(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function P(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?O(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):O(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function Q(a,b){return P(P({},b),a)}function R(a){var{shapeType:b,elementProps:c}=a;switch(b){case"rectangle":return d.createElement(z,c);case"trapezoid":return d.createElement(D,c);case"sector":return d.createElement(K,c);case"symbols":if("symbols"===b)return d.createElement(M.i,c);break;default:return null}}function S(a){var b,{option:c,shapeType:e,propTransformer:g=Q,activeClassName:h="recharts-active-shape",isActive:i}=a,j=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,N);if((0,d.isValidElement)(c))b=(0,d.cloneElement)(c,P(P({},j),(0,d.isValidElement)(c)?c.props:c));else if("function"==typeof c)b=c(j);else if(f()(c)&&"boolean"!=typeof c){var k=g(c,j);b=d.createElement(R,{shapeType:e,elementProps:k})}else b=d.createElement(R,{shapeType:e,elementProps:j});return i?d.createElement(L.W,{className:h},b):b}},86941:(a,b,c)=>{"use strict";c.d(b,{C:()=>h,U:()=>i});var d=c(54985),e=c(92173),f=c(35268),g=c(22688),h=a=>a.brush,i=(0,d.Mz)([h,e.HZ,f.HK],(a,b,c)=>({height:a.height,x:(0,g.Et)(a.x)?a.x:b.left,y:(0,g.Et)(a.y)?a.y:b.top+b.height+b.brushBottom-((null==c?void 0:c.bottom)||0),width:(0,g.Et)(a.width)?a.width:b.width}))},87516:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{default:function(){return i},getImageProps:function(){return h}});let d=c(35288),e=c(63974),f=c(40106),g=d._(c(49656));function h(a){let{props:b}=(0,e.getImgProps)(a,{defaultLoader:g.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[a,c]of Object.entries(b))void 0===c&&delete b[a];return{props:b}}let i=f.Image},88285:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},88485:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.uniqBy=function(a,b){let c=new Map;for(let d=0;d<a.length;d++){let e=a[d],f=b(e);c.has(f)||c.set(f,e)}return Array.from(c.values())}},89196:(a,b,c)=>{"use strict";c.d(b,{i:()=>i});let d=Math.PI,e=2*d,f=e-1e-6;function g(a){this._+=a[0];for(let b=1,c=a.length;b<c;++b)this._+=arguments[b]+a[b]}class h{constructor(a){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==a?g:function(a){let b=Math.floor(a);if(!(b>=0))throw Error(`invalid digits: ${a}`);if(b>15)return g;let c=10**b;return function(a){this._+=a[0];for(let b=1,d=a.length;b<d;++b)this._+=Math.round(arguments[b]*c)/c+a[b]}}(a)}moveTo(a,b){this._append`M${this._x0=this._x1=+a},${this._y0=this._y1=+b}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(a,b){this._append`L${this._x1=+a},${this._y1=+b}`}quadraticCurveTo(a,b,c,d){this._append`Q${+a},${+b},${this._x1=+c},${this._y1=+d}`}bezierCurveTo(a,b,c,d,e,f){this._append`C${+a},${+b},${+c},${+d},${this._x1=+e},${this._y1=+f}`}arcTo(a,b,c,e,f){if(a*=1,b*=1,c*=1,e*=1,(f*=1)<0)throw Error(`negative radius: ${f}`);let g=this._x1,h=this._y1,i=c-a,j=e-b,k=g-a,l=h-b,m=k*k+l*l;if(null===this._x1)this._append`M${this._x1=a},${this._y1=b}`;else if(m>1e-6)if(Math.abs(l*i-j*k)>1e-6&&f){let n=c-g,o=e-h,p=i*i+j*j,q=Math.sqrt(p),r=Math.sqrt(m),s=f*Math.tan((d-Math.acos((p+m-(n*n+o*o))/(2*q*r)))/2),t=s/r,u=s/q;Math.abs(t-1)>1e-6&&this._append`L${a+t*k},${b+t*l}`,this._append`A${f},${f},0,0,${+(l*n>k*o)},${this._x1=a+u*i},${this._y1=b+u*j}`}else this._append`L${this._x1=a},${this._y1=b}`}arc(a,b,c,g,h,i){if(a*=1,b*=1,c*=1,i=!!i,c<0)throw Error(`negative radius: ${c}`);let j=c*Math.cos(g),k=c*Math.sin(g),l=a+j,m=b+k,n=1^i,o=i?g-h:h-g;null===this._x1?this._append`M${l},${m}`:(Math.abs(this._x1-l)>1e-6||Math.abs(this._y1-m)>1e-6)&&this._append`L${l},${m}`,c&&(o<0&&(o=o%e+e),o>f?this._append`A${c},${c},0,1,${n},${a-j},${b-k}A${c},${c},0,1,${n},${this._x1=l},${this._y1=m}`:o>1e-6&&this._append`A${c},${c},0,${+(o>=d)},${n},${this._x1=a+c*Math.cos(h)},${this._y1=b+c*Math.sin(h)}`)}rect(a,b,c,d){this._append`M${this._x0=this._x1=+a},${this._y0=this._y1=+b}h${c*=1}v${+d}h${-c}Z`}toString(){return this._}}function i(a){let b=3;return a.digits=function(c){if(!arguments.length)return b;if(null==c)b=null;else{let a=Math.floor(c);if(!(a>=0))throw RangeError(`invalid digits: ${c}`);b=a}return a},()=>new h(b)}h.prototype},89311:(a,b,c)=>{"use strict";function d(a){return function(){return a}}c.d(b,{A:()=>d})},90469:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.debounce=function(a,b,{signal:c,edges:d}={}){let e,f=null,g=null!=d&&d.includes("leading"),h=null==d||d.includes("trailing"),i=()=>{null!==f&&(a.apply(e,f),e=void 0,f=null)},j=null,k=()=>{null!=j&&clearTimeout(j),j=setTimeout(()=>{j=null,h&&i(),l()},b)},l=()=>{null!==j&&(clearTimeout(j),j=null),e=void 0,f=null},m=function(...a){if(c?.aborted)return;e=this,f=a;let b=null==j;k(),g&&b&&i()};return m.schedule=k,m.cancel=l,m.flush=()=>{i()},c?.addEventListener("abort",l,{once:!0}),m}},90478:(a,b,c)=>{"use strict";c(94040)},90481:(a,b,c)=>{"use strict";c.d(b,{o:()=>d});var d=(a,b,c,d,e,f,g,h)=>{if(null!=f&&null!=h){var i=g[0],j=null==i?void 0:h(i.positions,f);if(null!=j)return j;var k=null==e?void 0:e[Number(f)];if(k)if("horizontal"===c)return{x:k.coordinate,y:(d.top+b)/2};else return{x:(d.left+a)/2,y:k.coordinate}}}},91282:(a,b,c)=>{"use strict";c.d(b,{LV:()=>h,M:()=>f,hq:()=>e});var d=(0,c(53968).Z0)({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(a,b){if(a.chartData=b.payload,null==b.payload){a.dataStartIndex=0,a.dataEndIndex=0;return}b.payload.length>0&&a.dataEndIndex!==b.payload.length-1&&(a.dataEndIndex=b.payload.length-1)},setComputedData(a,b){a.computedData=b.payload},setDataStartEndIndexes(a,b){var{startIndex:c,endIndex:d}=b.payload;null!=c&&(a.dataStartIndex=c),null!=d&&(a.dataEndIndex=d)}}}),{setChartData:e,setDataStartEndIndexes:f,setComputedData:g}=d.actions,h=d.reducer},92173:(a,b,c)=>{"use strict";c.d(b,{Ds:()=>o,HZ:()=>n,c2:()=>p});var d=c(54985),e=c(37177),f=c.n(e),g=c(54758),h=c(79241),i=c(35268),j=c(21581),k=c(71156);function l(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function m(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?l(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):l(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var n=(0,d.Mz)([i.Lp,i.A$,i.HK,a=>a.brush.height,j.h,j.W,g.ff,g.dc],(a,b,c,d,e,g,i,j)=>{var l=g.reduce((a,b)=>{var{orientation:c}=b;if(!b.mirror&&!b.hide){var d="number"==typeof b.width?b.width:k.tQ;return m(m({},a),{},{[c]:a[c]+d})}return a},{left:c.left||0,right:c.right||0}),n=e.reduce((a,b)=>{var{orientation:c}=b;return b.mirror||b.hide?a:m(m({},a),{},{[c]:f()(a,"".concat(c))+b.height})},{top:c.top||0,bottom:c.bottom||0}),o=m(m({},n),l),p=o.bottom;o.bottom+=d;var q=a-(o=(0,h.s0)(o,i,j)).left-o.right,r=b-o.top-o.bottom;return m(m({brushBottom:p},o),{},{width:Math.max(q,0),height:Math.max(r,0)})}),o=(0,d.Mz)(n,a=>({x:a.left,y:a.top,width:a.width,height:a.height})),p=(0,d.Mz)(i.Lp,i.A$,(a,b)=>({x:0,y:0,width:a,height:b}))},92264:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(46153),e=c(10680),f=c(46193),g=c(77241);b.get=function a(b,c,h){if(null==b)return h;switch(typeof c){case"string":{if(d.isUnsafeProperty(c))return h;let f=b[c];if(void 0===f)if(e.isDeepKey(c))return a(b,g.toPath(c),h);else return h;return f}case"number":case"symbol":{"number"==typeof c&&(c=f.toKey(c));let a=b[c];if(void 0===a)return h;return a}default:{if(Array.isArray(c)){var i=b,j=c,k=h;if(0===j.length)return k;let a=i;for(let b=0;b<j.length;b++){if(null==a||d.isUnsafeProperty(j[b]))return k;a=a[j[b]]}return void 0===a?k:a}if(c=Object.is(c?.valueOf(),-0)?"-0":String(c),d.isUnsafeProperty(c))return h;let a=b[c];if(void 0===a)return h;return a}}}},93350:(a,b,c)=>{"use strict";a.exports=c(2958)},94040:(a,b,c)=>{"use strict";var d=c(38301);"function"==typeof Object.is&&Object.is,d.useSyncExternalStore,d.useRef,d.useEffect,d.useMemo,d.useDebugValue},94398:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isPlainObject=function(a){if(!a||"object"!=typeof a)return!1;let b=Object.getPrototypeOf(a);return(null===b||b===Object.prototype||null===Object.getPrototypeOf(b))&&"[object Object]"===Object.prototype.toString.call(a)}},94684:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},94741:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(44029),e=c(63004);b.matches=function(a){return a=e.cloneDeep(a),b=>d.isMatch(b,a)}},95325:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(49449),e=c(13770);b.isEqual=function(a,b){return d.isEqualWith(a,b,e.noop)}},96061:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let c=/^(?:0|[1-9]\d*)$/;b.isIndex=function(a,b=Number.MAX_SAFE_INTEGER){switch(typeof a){case"number":return Number.isInteger(a)&&a>=0&&a<b;case"symbol":return!1;case"string":return c.test(a)}}},96136:(a,b,c)=>{"use strict";c.d(b,{Cj:()=>f,Pg:()=>g,Ub:()=>h});var d=c(56998),e=c(46279),f=(a,b)=>{var c=(0,d.j)();return(d,f)=>g=>{null==a||a(d,f,g),c((0,e.RD)({activeIndex:String(f),activeDataKey:b,activeCoordinate:d.tooltipPosition}))}},g=a=>{var b=(0,d.j)();return(c,d)=>f=>{null==a||a(c,d,f),b((0,e.oP)())}},h=(a,b)=>{var c=(0,d.j)();return(d,f)=>g=>{null==a||a(d,f,g),c((0,e.ML)({activeIndex:String(f),activeDataKey:b,activeCoordinate:d.tooltipPosition}))}}},97652:(a,b,c)=>{"use strict";c.d(b,{i:()=>E});var d=c(38301);let e=Math.cos,f=Math.sin,g=Math.sqrt,h=Math.PI,i=2*h,j={draw(a,b){let c=g(b/h);a.moveTo(c,0),a.arc(0,0,c,0,i)}},k=g(1/3),l=2*k,m=f(h/10)/f(7*h/10),n=f(i/10)*m,o=-e(i/10)*m,p=g(3),q=g(3)/2,r=1/g(12),s=(r/2+1)*3;var t=c(89311),u=c(89196);g(3),g(3);var v=c(43249),w=c(72677),x=c(22688),y=["type","size","sizeType"];function z(){return(z=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function A(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function B(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?A(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):A(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var C={symbolCircle:j,symbolCross:{draw(a,b){let c=g(b/5)/2;a.moveTo(-3*c,-c),a.lineTo(-c,-c),a.lineTo(-c,-3*c),a.lineTo(c,-3*c),a.lineTo(c,-c),a.lineTo(3*c,-c),a.lineTo(3*c,c),a.lineTo(c,c),a.lineTo(c,3*c),a.lineTo(-c,3*c),a.lineTo(-c,c),a.lineTo(-3*c,c),a.closePath()}},symbolDiamond:{draw(a,b){let c=g(b/l),d=c*k;a.moveTo(0,-c),a.lineTo(d,0),a.lineTo(0,c),a.lineTo(-d,0),a.closePath()}},symbolSquare:{draw(a,b){let c=g(b),d=-c/2;a.rect(d,d,c,c)}},symbolStar:{draw(a,b){let c=g(.8908130915292852*b),d=n*c,h=o*c;a.moveTo(0,-c),a.lineTo(d,h);for(let b=1;b<5;++b){let g=i*b/5,j=e(g),k=f(g);a.lineTo(k*c,-j*c),a.lineTo(j*d-k*h,k*d+j*h)}a.closePath()}},symbolTriangle:{draw(a,b){let c=-g(b/(3*p));a.moveTo(0,2*c),a.lineTo(-p*c,-c),a.lineTo(p*c,-c),a.closePath()}},symbolWye:{draw(a,b){let c=g(b/s),d=c/2,e=c*r,f=c*r+c,h=-d;a.moveTo(d,e),a.lineTo(d,f),a.lineTo(h,f),a.lineTo(-.5*d-q*e,q*d+-.5*e),a.lineTo(-.5*d-q*f,q*d+-.5*f),a.lineTo(-.5*h-q*f,q*h+-.5*f),a.lineTo(-.5*d+q*e,-.5*e-q*d),a.lineTo(-.5*d+q*f,-.5*f-q*d),a.lineTo(-.5*h+q*f,-.5*f-q*h),a.closePath()}}},D=Math.PI/180,E=a=>{var{type:b="circle",size:c=64,sizeType:e="area"}=a,f=B(B({},function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,y)),{},{type:b,size:c,sizeType:e}),{className:g,cx:h,cy:i}=f,k=(0,w.J9)(f,!0);return h===+h&&i===+i&&c===+c?d.createElement("path",z({},k,{className:(0,v.$)("recharts-symbols",g),transform:"translate(".concat(h,", ").concat(i,")"),d:(()=>{var a=C["symbol".concat((0,x.Zb)(b))]||j;return(function(a,b){let c=null,d=(0,u.i)(e);function e(){let e;if(c||(c=e=d()),a.apply(this,arguments).draw(c,+b.apply(this,arguments)),e)return c=null,e+""||null}return a="function"==typeof a?a:(0,t.A)(a||j),b="function"==typeof b?b:(0,t.A)(void 0===b?64:+b),e.type=function(b){return arguments.length?(a="function"==typeof b?b:(0,t.A)(b),e):a},e.size=function(a){return arguments.length?(b="function"==typeof a?a:(0,t.A)(+a),e):b},e.context=function(a){return arguments.length?(c=null==a?null:a,e):c},e})().type(a).size(((a,b,c)=>{if("area"===b)return a;switch(c){case"cross":return 5*a*a/9;case"diamond":return .5*a*a/Math.sqrt(3);case"square":return a*a;case"star":var d=18*D;return 1.25*a*a*(Math.tan(d)-Math.tan(2*d)*Math.tan(d)**2);case"triangle":return Math.sqrt(3)*a*a/4;case"wye":return(21-10*Math.sqrt(3))*a*a/8;default:return Math.PI*a*a/4}})(c,e,b))()})()})):null};E.registerSymbol=(a,b)=>{C["symbol".concat((0,x.Zb)(a))]=b}},98246:(a,b,c)=>{"use strict";c.d(b,{g:()=>k});var d=c(54985),e=c(15379),f=c(76265),g=c(92173),h=c(40749),i=c(65168),j=c(34729),k=(0,d.Mz)([(a,b)=>b,e.fz,i.D0,j.R,f.gL,f.R4,h.r1,g.HZ],h.aX)},98700:(a,b,c)=>{"use strict";c.d(b,{Pu:()=>l});var d=c(4702);class e{constructor(a){!function(a,b,c){var d;(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c}(this,"cache",new Map),this.maxSize=a}get(a){var b=this.cache.get(a);return void 0!==b&&(this.cache.delete(a),this.cache.set(a,b)),b}set(a,b){if(this.cache.has(a))this.cache.delete(a);else if(this.cache.size>=this.maxSize){var c=this.cache.keys().next().value;this.cache.delete(c)}this.cache.set(a,b)}clear(){this.cache.clear()}size(){return this.cache.size}}function f(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}var g=function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?f(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):f(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({},{cacheSize:2e3,enableCache:!0}),h=new e(g.cacheSize),i={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},j="recharts_measurement_span",k=(a,b)=>{try{var c=document.getElementById(j);c||((c=document.createElement("span")).setAttribute("id",j),c.setAttribute("aria-hidden","true"),document.body.appendChild(c)),Object.assign(c.style,i,b),c.textContent="".concat(a);var d=c.getBoundingClientRect();return{width:d.width,height:d.height}}catch(a){return{width:0,height:0}}},l=function(a){var b,c,e,f,i,j,l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==a||d.m.isSsr)return{width:0,height:0};if(!g.enableCache)return k(a,l);var m=(b=l.fontSize||"",c=l.fontFamily||"",e=l.fontWeight||"",f=l.fontStyle||"",i=l.letterSpacing||"",j=l.textTransform||"","".concat(a,"|").concat(b,"|").concat(c,"|").concat(e,"|").concat(f,"|").concat(i,"|").concat(j)),n=h.get(m);if(n)return n;var o=k(a,l);return h.set(m,o),o}},99032:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isPrimitive=function(a){return null==a||"object"!=typeof a&&"function"!=typeof a}},99088:(a,b,c)=>{"use strict";a.exports=c(42797)},99539:(a,b,c)=>{"use strict";c.d(b,{u:()=>i});var d=c(38301),e=c(43249),f=c(72677),g=["children","width","height","viewBox","className","style","title","desc"];function h(){return(h=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var i=(0,d.forwardRef)((a,b)=>{var{children:c,width:i,height:j,viewBox:k,className:l,style:m,title:n,desc:o}=a,p=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,g),q=k||{width:i,height:j,x:0,y:0},r=(0,e.$)("recharts-surface",l);return d.createElement("svg",h({},(0,f.J9)(p,!0,"svg"),{className:r,width:i,height:j,style:m,viewBox:"".concat(q.x," ").concat(q.y," ").concat(q.width," ").concat(q.height),ref:b}),d.createElement("title",null,n),d.createElement("desc",null,o),c)})}};