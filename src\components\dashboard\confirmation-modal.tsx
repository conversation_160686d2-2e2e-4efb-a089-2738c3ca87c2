"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  CheckCircle, 
  AlertCircle, 
  X, 
  Eye, 
  EyeOff,
  Lock
} from "lucide-react"

interface ConfirmationModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  amount: string
  recipient?: string
  description?: string
  fee?: string
  total?: string
  type: 'transfer' | 'loan' | 'withdrawal' | 'bill_payment' | 'deposit'
}

export function ConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  amount,
  recipient,
  description,
  fee = "0.00",
  total,
  type
}: ConfirmationModalProps) {
  const [pin, setPin] = useState("")
  const [showPin, setShowPin] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)

  if (!isOpen) return null

  const handleConfirm = async () => {
    if (pin.length !== 4) {
      alert("Please enter your 4-digit PIN")
      return
    }

    setIsProcessing(true)
    
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false)
      onConfirm()
    }, 2000)
  }

  const getTypeColor = () => {
    switch (type) {
      case 'transfer':
        return '#059AD1'
      case 'loan':
        return '#F2B134'
      case 'withdrawal':
        return '#FF6B6B'
      case 'bill_payment':
        return '#16DBCC'
      case 'deposit':
        return '#4CAF50'
      default:
        return '#059AD1'
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md">
        <CardContent className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-[#333B69]">{title}</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="p-1 h-auto"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Transaction Details */}
          <div className="space-y-4 mb-6">
            <div className="text-center">
              <p className="text-[#6E6E6E] mb-2">Amount</p>
              <p className="text-3xl font-bold" style={{ color: getTypeColor() }}>
                ${amount}
              </p>
            </div>

            <div className="space-y-3 text-sm">
              {recipient && (
                <div className="flex justify-between">
                  <span className="text-[#6E6E6E]">Recipient:</span>
                  <span className="font-medium text-[#333B69]">{recipient}</span>
                </div>
              )}
              
              {description && (
                <div className="flex justify-between">
                  <span className="text-[#6E6E6E]">Description:</span>
                  <span className="font-medium text-[#333B69]">{description}</span>
                </div>
              )}

              <div className="flex justify-between">
                <span className="text-[#6E6E6E]">Transaction Fee:</span>
                <span className="font-medium text-[#333B69]">${fee}</span>
              </div>

              <hr className="my-3" />

              <div className="flex justify-between font-semibold">
                <span>Total Amount:</span>
                <span style={{ color: getTypeColor() }}>
                  ${total || (parseFloat(amount) + parseFloat(fee)).toFixed(2)}
                </span>
              </div>
            </div>
          </div>

          {/* PIN Input */}
          <div className="mb-6">
            <Label htmlFor="pin" className="flex items-center gap-2 mb-2">
              <Lock className="h-4 w-4" />
              Enter your 4-digit PIN
            </Label>
            <div className="relative">
              <Input
                id="pin"
                type={showPin ? "text" : "password"}
                placeholder="••••"
                value={pin}
                onChange={(e) => setPin(e.target.value.slice(0, 4))}
                className="text-center text-lg tracking-widest"
                maxLength={4}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-2 top-1/2 transform -translate-y-1/2"
                onClick={() => setShowPin(!showPin)}
              >
                {showPin ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          {/* Security Notice */}
          <div className="bg-[#F1F6FD] border border-[#A7C5FD] rounded-lg p-3 mb-6">
            <div className="flex gap-2">
              <AlertCircle className="h-4 w-4 text-[#0052EA] mt-0.5 flex-shrink-0" />
              <p className="text-xs text-[#333B69]">
                Your transaction will be processed securely. Please verify all details before confirming.
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="grid grid-cols-2 gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isProcessing}
              className="h-12"
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirm}
              disabled={pin.length !== 4 || isProcessing}
              className="h-12"
              style={{ backgroundColor: getTypeColor() }}
            >
              {isProcessing ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Processing...
                </div>
              ) : (
                'Confirm Transaction'
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
