"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowUpRight, ArrowDownLeft } from "lucide-react"

export function Transfer() {
  return (
    <div className="space-y-6">
      {/* Transfer Button */}
      <Card className="bg-[#1814F3] rounded-[25px] border-none shadow-lg text-white">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-white/20 rounded-full">
                <ArrowUpRight className="h-6 w-6" />
              </div>
              <span className="text-lg font-medium">Transfer</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Withdraw Button */}
      <Card className="bg-white rounded-[25px] border border-gray-200 shadow-lg">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-gray-100 rounded-full">
                <ArrowDownLeft className="h-6 w-6 text-[#718EBF]" />
              </div>
              <span className="text-lg font-medium text-[#718EBF]">Withdraw</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Deposit Button */}
      <Card className="bg-white rounded-[25px] border border-gray-200 shadow-lg">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-gray-100 rounded-full">
                <ArrowDownLeft className="h-6 w-6 text-[#718EBF]" />
              </div>
              <span className="text-lg font-medium text-[#718EBF]">Deposit</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
