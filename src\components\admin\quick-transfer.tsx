"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Send, RefreshCw, ArrowUpDown, ArrowDownLeft } from "lucide-react"
import { useState } from "react"

export function QuickTransfer() {
  const [amount, setAmount] = useState("525.50")

  const transferOptions = [
    {
      id: 'refund',
      label: 'Refund transaction',
      icon: RefreshCw,
      color: 'bg-orange-100 text-orange-600'
    },
    {
      id: 'fund-agent',
      label: 'Fund agent',
      icon: ArrowUpDown,
      color: 'bg-blue-100 text-blue-600'
    },
    {
      id: 'settle-merchant',
      label: 'Settle Merchant',
      icon: ArrowDownLeft,
      color: 'bg-green-100 text-green-600'
    }
  ]

  return (
    <Card className="bg-white rounded-[25px] border-none shadow-lg">
      <CardHeader>
        <CardTitle className="text-[#343C6A] text-xl font-semibold">Quick Transfer</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Recipients */}
        <div className="flex items-center space-x-4">
          <div className="flex -space-x-2">
            {[1, 2, 3].map((i) => (
              <div 
                key={i}
                className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 border-2 border-white flex items-center justify-center text-white font-semibold"
              >
                U{i}
              </div>
            ))}
          </div>
          <Button variant="outline" size="icon" className="rounded-full">
            <Send className="h-4 w-4" />
          </Button>
        </div>

        {/* Amount Input */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-[#718EBF]">Write Amount</label>
          <div className="flex items-center space-x-2">
            <div className="flex-1 relative">
              <Input
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="bg-[#EDF1F7] border-none text-lg font-medium h-12"
              />
            </div>
            <Button className="bg-[#1814F3] hover:bg-[#1814F3]/90 h-12 px-6">
              <Send className="h-4 w-4 mr-2" />
              Send
            </Button>
          </div>
        </div>

        {/* Transfer Options */}
        <div className="grid grid-cols-3 gap-4">
          {transferOptions.map((option) => {
            const Icon = option.icon
            return (
              <div key={option.id} className="text-center space-y-2">
                <div className={`w-16 h-16 rounded-full ${option.color} flex items-center justify-center mx-auto`}>
                  <Icon className="h-6 w-6" />
                </div>
                <span className="text-xs text-[#718EBF] font-medium">{option.label}</span>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
