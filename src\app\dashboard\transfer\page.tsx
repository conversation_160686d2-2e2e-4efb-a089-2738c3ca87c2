"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/dashboard/header"
import { Sidebar } from "@/components/dashboard/sidebar"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ArrowRight, Users, Store, UserCheck } from "lucide-react"

interface TransferOption {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  color: string
}

const transferOptions: TransferOption[] = [
  {
    id: 'p2p',
    title: 'Peer-to-Peer (P2P)',
    description: 'Transfer instantly to AeTrust users.',
    icon: <Users className="h-6 w-6" />,
    color: '#059AD1'
  },
  {
    id: 'p2b',
    title: 'Peer-to-Business (P2B)',
    description: 'Pay merchants, bills, and services.',
    icon: <Store className="h-6 w-6" />,
    color: '#009639'
  },
  {
    id: 'agent',
    title: 'Agent Assisted',
    description: 'Send or receive via AeTrust agent.',
    icon: <UserCheck className="h-6 w-6" />,
    color: '#F2B134'
  }
]

export default function TransferPage() {
  const [activeMenuItem, setActiveMenuItem] = useState("transfer")

  const handleTransferOptionClick = (optionId: string) => {
    // Navigate to specific transfer type
    switch (optionId) {
      case 'p2p':
        window.location.href = '/dashboard/transfer/p2p'
        break
      case 'p2b':
        window.location.href = '/dashboard/transfer/p2b'
        break
      case 'agent':
        window.location.href = '/dashboard/transfer/agent'
        break
    }
  }

  return (
    <div className="flex h-screen bg-[#F5F7FA]">
      <Sidebar activeItem={activeMenuItem} onItemClick={setActiveMenuItem} />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header 
          title="Transfer" 
          userName="James Bond"
        />
        
        <main className="flex-1 overflow-auto p-4 md:p-8">
          <div className="max-w-4xl mx-auto">
            <div className="mb-8">
              <h1 className="text-2xl font-semibold text-[#333B69] mb-2">My account</h1>
            </div>

            {/* Account Card */}
            <Card className="bg-gradient-to-r from-[#1B263B] to-[#059AD1] text-white mb-8 overflow-hidden relative">
              <CardContent className="p-8">
                <div className="flex justify-between items-start">
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium mb-1">James Bond</h3>
                      <div className="flex items-center gap-3">
                        <span className="text-lg">*** **** * 213</span>
                        <Button variant="ghost" size="sm" className="text-white hover:bg-white/20 p-1">
                          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                        </Button>
                      </div>
                    </div>
                    
                    <div>
                      <p className="text-sm opacity-80 mb-1">Your balance</p>
                      <div className="flex items-center gap-3">
                        <span className="text-3xl font-bold">$35,673</span>
                        <Button variant="ghost" size="sm" className="text-white hover:bg-white/20 p-1">
                          <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col items-end">
                    <Button className="bg-white/20 hover:bg-white/30 text-white border-0 mb-4">
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </Button>
                    
                    <div className="flex items-center gap-2 text-sm">
                      <span className="bg-[#F2B134] text-white px-3 py-1 rounded-full font-medium">Tier 1</span>
                      <svg className="h-5 w-5 text-[#F2B134]" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                      </svg>
                    </div>
                  </div>
                </div>
                
                {/* Card decoration */}
                <div className="absolute top-0 right-0 w-32 h-8 bg-[#F2B134] rounded-bl-lg"></div>
              </CardContent>
            </Card>

            {/* Transfer Options */}
            <div className="space-y-4">
              {transferOptions.map((option) => (
                <Card 
                  key={option.id} 
                  className="cursor-pointer hover:shadow-md transition-shadow border border-gray-200"
                  onClick={() => handleTransferOptionClick(option.id)}
                >
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div 
                          className="w-14 h-14 rounded-xl flex items-center justify-center border"
                          style={{ borderColor: option.color }}
                        >
                          <div style={{ color: option.color }}>
                            {option.icon}
                          </div>
                        </div>
                        
                        <div>
                          <h3 className="font-medium text-[#1C1C1E] mb-1">{option.title}</h3>
                          <p className="text-sm text-[#6E6E6E]">{option.description}</p>
                        </div>
                      </div>
                      
                      <ArrowRight className="h-5 w-5 text-[#60708F]" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
