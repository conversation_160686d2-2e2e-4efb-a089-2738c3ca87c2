"use client"

import { useState } from "react"
import { Head<PERSON> } from "@/components/dashboard/header"
import { Sidebar } from "@/components/dashboard/sidebar"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ArrowLeft, Eye, Copy } from "lucide-react"

const quickAmounts = [50, 100, 200, 500]

export default function WalletToWalletPage() {
  const [activeMenuItem, setActiveMenuItem] = useState("transfer")
  const [amount, setAmount] = useState("3000")
  const [selectedQuickAmount, setSelectedQuickAmount] = useState<number | null>(null)
  const [recipientWallet, setRecipientWallet] = useState("")
  const [description, setDescription] = useState("")

  const handleQuickAmountClick = (value: number) => {
    setAmount(value.toString())
    setSelectedQuickAmount(value)
  }

  const handleAmountChange = (value: string) => {
    setAmount(value)
    setSelectedQuickAmount(null)
  }

  return (
    <div className="flex h-screen bg-[#F5F7FA]">
      <Sidebar activeItem={activeMenuItem} onItemClick={setActiveMenuItem} />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header 
          title="Wallet to Wallet" 
          userName="James Bond"
        />
        
        <main className="flex-1 overflow-auto p-4 md:p-8">
          <div className="max-w-4xl mx-auto">
            {/* Back Button */}
            <Button 
              variant="ghost" 
              className="mb-6 text-[#343C6A] hover:bg-gray-100"
              onClick={() => window.history.back()}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column - Amount Entry */}
              <div>
                <Card className="bg-[#1B263B] text-white">
                  <CardContent className="p-8">
                    <div className="text-center space-y-6">
                      <div>
                        <p className="text-[#AFABAB] text-lg mb-2">Enter Amount</p>
                        <div className="relative">
                          <span className="text-6xl font-semibold">${amount}</span>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <p className="text-[#AFABAB] text-left">Quick Amounts</p>
                        <div className="grid grid-cols-4 gap-3">
                          {quickAmounts.map((value) => (
                            <Button
                              key={value}
                              variant="outline"
                              className={`border-[#059AD1] text-white hover:bg-[#059AD1]/20 ${
                                selectedQuickAmount === value ? 'bg-[#059AD1]/20' : 'bg-black/30'
                              }`}
                              onClick={() => handleQuickAmountClick(value)}
                            >
                              ${value}
                            </Button>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Right Column - Transfer Details */}
              <div className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="recipientWallet" className="text-[#585858] font-semibold">
                      Recipient Wallet Address
                    </Label>
                    <Input
                      id="recipientWallet"
                      placeholder="Enter wallet address or phone number"
                      value={recipientWallet}
                      onChange={(e) => setRecipientWallet(e.target.value)}
                      className="mt-2 h-16 border-[#CBCBCB] rounded-3xl px-6"
                    />
                  </div>

                  <div>
                    <Label htmlFor="description" className="text-[#585858] font-semibold">
                      Description (Optional)
                    </Label>
                    <Input
                      id="description"
                      placeholder="Add a note for this transfer"
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      className="mt-2 h-16 border-[#CBCBCB] rounded-3xl px-6"
                    />
                  </div>
                </div>

                {/* Transfer Summary */}
                <Card className="bg-white border border-gray-200">
                  <CardContent className="p-6">
                    <h3 className="font-semibold text-[#333B69] mb-4">Transfer Summary</h3>
                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-[#6E6E6E]">Transfer Amount:</span>
                        <span className="font-medium">${amount}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-[#6E6E6E]">Transaction Fee:</span>
                        <span className="font-medium">$0.50</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-[#6E6E6E]">Processing Time:</span>
                        <span className="font-medium">Instant</span>
                      </div>
                      <hr className="my-3" />
                      <div className="flex justify-between font-semibold">
                        <span>Total Debit:</span>
                        <span>${(parseFloat(amount) + 0.50).toFixed(2)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Recent Recipients */}
                <Card className="bg-white border border-gray-200">
                  <CardContent className="p-6">
                    <h3 className="font-semibold text-[#333B69] mb-4">Recent Recipients</h3>
                    <div className="space-y-3">
                      {[
                        { name: "John Doe", wallet: "+234 801 234 5678", avatar: "JD" },
                        { name: "Sarah Wilson", wallet: "+234 802 345 6789", avatar: "SW" },
                        { name: "Mike Johnson", wallet: "+234 803 456 7890", avatar: "MJ" }
                      ].map((recipient, index) => (
                        <div 
                          key={index}
                          className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg cursor-pointer"
                          onClick={() => setRecipientWallet(recipient.wallet)}
                        >
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-[#059AD1] text-white rounded-full flex items-center justify-center text-sm font-medium">
                              {recipient.avatar}
                            </div>
                            <div>
                              <p className="font-medium text-sm">{recipient.name}</p>
                              <p className="text-xs text-[#6E6E6E]">{recipient.wallet}</p>
                            </div>
                          </div>
                          <Button variant="ghost" size="sm">
                            Select
                          </Button>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Continue Button */}
            <div className="mt-8 flex justify-center">
              <Button 
                className="bg-[#059AD1] hover:bg-[#059AD1]/90 text-white px-16 py-6 text-lg rounded-2xl"
                onClick={() => {
                  // Navigate to confirmation page
                  window.location.href = '/dashboard/wallet-to-wallet/confirm'
                }}
              >
                Continue
              </Button>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
