"use client"

import { useState } from "react"
import { Head<PERSON> } from "@/components/dashboard/header"
import { Sidebar } from "@/components/dashboard/sidebar"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ArrowLeft, Building, CreditCard, MapPin, Info } from "lucide-react"

const quickAmounts = [50, 100, 200, 500]

const withdrawalMethods = [
  {
    id: 'bank',
    title: 'Bank Transfer',
    description: 'Withdraw to your bank account',
    icon: <Building className="h-6 w-6" />,
    color: '#059AD1',
    fee: 'Free',
    processingTime: '1-3 business days'
  },
  {
    id: 'atm',
    title: 'ATM Withdrawal',
    description: 'Generate ATM withdrawal code',
    icon: <CreditCard className="h-6 w-6" />,
    color: '#16DBCC',
    fee: '$2.00',
    processingTime: 'Instant'
  },
  {
    id: 'agent',
    title: 'Agent Location',
    description: 'Withdraw at AeTrust agent location',
    icon: <MapPin className="h-6 w-6" />,
    color: '#F2B134',
    fee: '$1.00',
    processingTime: 'Instant'
  }
]

export default function WithdrawPage() {
  const [activeMenuItem, setActiveMenuItem] = useState("withdraw")
  const [amount, setAmount] = useState("100")
  const [selectedQuickAmount, setSelectedQuickAmount] = useState<number | null>(null)
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null)
  const [bankDetails, setBankDetails] = useState({
    bankName: "",
    accountNumber: "",
    accountName: ""
  })

  const handleQuickAmountClick = (value: number) => {
    setAmount(value.toString())
    setSelectedQuickAmount(value)
  }

  const handleAmountChange = (value: string) => {
    setAmount(value)
    setSelectedQuickAmount(null)
  }

  const handleMethodSelect = (methodId: string) => {
    setSelectedMethod(methodId)
  }

  const selectedMethodData = withdrawalMethods.find(method => method.id === selectedMethod)

  return (
    <div className="flex h-screen bg-[#F5F7FA]">
      <Sidebar activeItem={activeMenuItem} onItemClick={setActiveMenuItem} />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header 
          title="Withdraw Funds" 
          userName="James Bond"
        />
        
        <main className="flex-1 overflow-auto p-4 md:p-8">
          <div className="max-w-4xl mx-auto">
            {/* Back Button */}
            <Button 
              variant="ghost" 
              className="mb-6 text-[#343C6A] hover:bg-gray-100"
              onClick={() => window.history.back()}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column - Amount Entry */}
              <div className="space-y-6">
                <Card className="bg-[#1B263B] text-white">
                  <CardContent className="p-8">
                    <div className="text-center space-y-6">
                      <div>
                        <p className="text-[#AFABAB] text-lg mb-2">Withdraw Amount</p>
                        <div className="relative">
                          <span className="text-6xl font-semibold">${amount}</span>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <p className="text-[#AFABAB] text-left">Quick Amounts</p>
                        <div className="grid grid-cols-4 gap-3">
                          {quickAmounts.map((value) => (
                            <Button
                              key={value}
                              variant="outline"
                              className={`border-[#059AD1] text-white hover:bg-[#059AD1]/20 ${
                                selectedQuickAmount === value ? 'bg-[#059AD1]/20' : 'bg-black/30'
                              }`}
                              onClick={() => handleQuickAmountClick(value)}
                            >
                              ${value}
                            </Button>
                          ))}
                        </div>
                      </div>

                      <div className="pt-4">
                        <Label htmlFor="customAmount" className="text-[#AFABAB] text-left block mb-2">
                          Or enter custom amount
                        </Label>
                        <Input
                          id="customAmount"
                          type="number"
                          placeholder="0.00"
                          value={amount}
                          onChange={(e) => handleAmountChange(e.target.value)}
                          className="bg-black/30 border-[#059AD1] text-white placeholder:text-[#AFABAB]"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Current Balance */}
                <Card>
                  <CardContent className="p-6">
                    <div className="text-center">
                      <p className="text-[#6E6E6E] mb-2">Available Balance</p>
                      <p className="text-3xl font-bold text-[#333B69]">$35,673.00</p>
                      <p className="text-sm text-[#6E6E6E] mt-2">
                        After withdrawal: ${(35673 - parseFloat(amount || '0')).toLocaleString()}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Right Column - Withdrawal Methods */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold text-[#333B69] mb-4">Select Withdrawal Method</h3>
                  <div className="space-y-4">
                    {withdrawalMethods.map((method) => (
                      <Card 
                        key={method.id} 
                        className={`cursor-pointer transition-all border-2 ${
                          selectedMethod === method.id 
                            ? 'border-[#059AD1] bg-[#059AD1]/5' 
                            : 'border-gray-200 hover:border-[#059AD1]/50'
                        }`}
                        onClick={() => handleMethodSelect(method.id)}
                      >
                        <CardContent className="p-6">
                          <div className="flex items-start gap-4">
                            <div 
                              className="w-12 h-12 rounded-xl flex items-center justify-center"
                              style={{ backgroundColor: `${method.color}20`, color: method.color }}
                            >
                              {method.icon}
                            </div>
                            
                            <div className="flex-1">
                              <h4 className="font-medium text-[#1C1C1E] mb-1">{method.title}</h4>
                              <p className="text-sm text-[#6E6E6E] mb-2">{method.description}</p>
                              <div className="flex gap-4 text-xs text-[#6E6E6E]">
                                <span>Fee: {method.fee}</span>
                                <span>•</span>
                                <span>{method.processingTime}</span>
                              </div>
                            </div>
                            
                            <div className={`w-5 h-5 rounded-full border-2 ${
                              selectedMethod === method.id 
                                ? 'border-[#059AD1] bg-[#059AD1]' 
                                : 'border-gray-300'
                            }`}>
                              {selectedMethod === method.id && (
                                <div className="w-full h-full rounded-full bg-white scale-50"></div>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>

                {/* Bank Details Form (if bank transfer selected) */}
                {selectedMethod === 'bank' && (
                  <Card>
                    <CardContent className="p-6">
                      <h4 className="font-semibold text-[#333B69] mb-4">Bank Account Details</h4>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="bankName">Bank Name</Label>
                          <Input
                            id="bankName"
                            placeholder="Select your bank"
                            value={bankDetails.bankName}
                            onChange={(e) => setBankDetails(prev => ({ ...prev, bankName: e.target.value }))}
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="accountNumber">Account Number</Label>
                          <Input
                            id="accountNumber"
                            placeholder="Enter account number"
                            value={bankDetails.accountNumber}
                            onChange={(e) => setBankDetails(prev => ({ ...prev, accountNumber: e.target.value }))}
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="accountName">Account Name</Label>
                          <Input
                            id="accountName"
                            placeholder="Account holder name"
                            value={bankDetails.accountName}
                            onChange={(e) => setBankDetails(prev => ({ ...prev, accountName: e.target.value }))}
                            className="mt-1"
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Withdrawal Limits Info */}
                <Card className="bg-[#F1F6FD] border border-[#A7C5FD]">
                  <CardContent className="p-4">
                    <div className="flex gap-3">
                      <Info className="h-5 w-5 text-[#0052EA] mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-medium text-[#333B69] mb-2">Withdrawal Limits</h4>
                        <div className="space-y-1 text-sm text-[#6E6E6E]">
                          <p>Daily limit: $5,000</p>
                          <p>Monthly limit: $50,000</p>
                          <p>Minimum withdrawal: $10</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Transaction Summary */}
                {selectedMethod && (
                  <Card>
                    <CardContent className="p-6">
                      <h4 className="font-semibold text-[#333B69] mb-4">Transaction Summary</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex justify-between">
                          <span className="text-[#6E6E6E]">Withdrawal Amount:</span>
                          <span className="font-medium">${amount}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-[#6E6E6E]">Transaction Fee:</span>
                          <span className="font-medium">{selectedMethodData?.fee}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-[#6E6E6E]">Processing Time:</span>
                          <span className="font-medium">{selectedMethodData?.processingTime}</span>
                        </div>
                        <hr className="my-3" />
                        <div className="flex justify-between font-semibold">
                          <span>Total Debit:</span>
                          <span>
                            ${selectedMethodData?.fee === 'Free' 
                              ? amount 
                              : (parseFloat(amount || '0') + parseFloat(selectedMethodData?.fee.replace('$', '') || '0')).toFixed(2)
                            }
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>

            {/* Continue Button */}
            <div className="mt-8 flex justify-center">
              <Button 
                className="bg-[#059AD1] hover:bg-[#059AD1]/90 text-white px-16 py-6 text-lg rounded-2xl"
                disabled={!selectedMethod || !amount}
                onClick={() => {
                  // Navigate to confirmation page
                  window.location.href = '/dashboard/withdraw/confirm'
                }}
              >
                Continue
              </Button>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
