(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[618],{1262:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let n=r(12115),i=n.useLayoutEffect,o=n.useEffect;function a(e){let{headManager:t,reduceComponentsToState:r}=e;function a(){if(t&&t.mountedInstances){let i=n.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(r(i,e))}}return i(()=>{var r;return null==t||null==(r=t.mountedInstances)||r.add(e.children),()=>{var r;null==t||null==(r=t.mountedInstances)||r.delete(e.children)}}),i(()=>(t&&(t._pendingUpdate=a),()=>{t&&(t._pendingUpdate=a)})),o(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},1322:(e,t)=>{"use strict";function r(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:4*(e!=e)}Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.compareValues=(e,t,n)=>{if(e!==t){let i=r(e),o=r(t);if(i===o&&0===i){if(e<t)return"desc"===n?1:-1;if(e>t)return"desc"===n?-1:1}return"desc"===n?o-i:i-o}return 0}},1444:(e,t,r)=>{"use strict";r.d(t,{kz:()=>is,fb:()=>n9,q:()=>i_,tP:()=>iI,g1:()=>iH,iv:()=>oo,Nk:()=>n6,pM:()=>il,Oz:()=>ij,tF:()=>on,rj:()=>n4,ec:()=>n0,bb:()=>iE,xp:()=>i$,wL:()=>iN,sr:()=>iL,Qn:()=>iB,MK:()=>io,IO:()=>n3,P9:()=>ib,S5:()=>ig,PU:()=>nF,cd:()=>nK,eo:()=>nX,yi:()=>iv,CH:()=>ic,ZB:()=>ol,D5:()=>iX,iV:()=>iQ,Hd:()=>nY,Gx:()=>oc,DP:()=>nG,BQ:()=>or,_y:()=>od,AV:()=>iC,um:()=>nV,xM:()=>iU,gT:()=>ix,Kr:()=>im,$X:()=>iM,TC:()=>ia,Zi:()=>ou,CR:()=>os,ld:()=>nJ,L$:()=>i9,Rl:()=>nH,Lw:()=>i8,KR:()=>oe,sf:()=>nq,wP:()=>ot});var n,i,o,a,l,u,s,c={};r.r(c),r.d(c,{scaleBand:()=>x,scaleDiverging:()=>function e(){var t=eq(r8()(e_));return t.copy=function(){return r3(t,e())},y.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=eQ(r8()).domain([.1,1,10]);return t.copy=function(){return r3(t,e()).base(t.base())},y.apply(t,arguments)},scaleDivergingPow:()=>r6,scaleDivergingSqrt:()=>r7,scaleDivergingSymlog:()=>function e(){var t=e2(r8());return t.copy=function(){return r3(t,e()).constant(t.constant())},y.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e*=1)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,ej),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,ej):[0,1],eq(n)},scaleImplicit:()=>b,scaleLinear:()=>function e(){var t=eN();return t.copy=function(){return eT(t,e())},p.apply(t,arguments),eq(t)},scaleLog:()=>function e(){let t=eQ(eC()).domain([1,10]);return t.copy=()=>eT(t,e()).base(t.base()),p.apply(t,arguments),t},scaleOrdinal:()=>w,scalePoint:()=>O,scalePow:()=>e6,scaleQuantile:()=>function e(){var t,r=[],n=[],i=[];function o(){var e=0,t=Math.max(1,n.length);for(i=Array(t-1);++e<t;)i[e-1]=function(e,t,r=D){if(!(!(n=e.length)||isNaN(t*=1))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,o=Math.floor(i),a=+r(e[o],o,e);return a+(r(e[o+1],o+1,e)-a)*(i-o)}}(r,e/t);return a}function a(e){return null==e||isNaN(e*=1)?t:n[I(i,e)]}return a.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?i[t-1]:r[0],t<i.length?i[t]:r[r.length-1]]},a.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t*=1)||r.push(t);return r.sort(k),o()},a.range=function(e){return arguments.length?(n=Array.from(e),o()):n.slice()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.quantiles=function(){return i.slice()},a.copy=function(){return e().domain(r).range(n).unknown(t)},p.apply(a,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,i=1,o=[.5],a=[0,1];function l(e){return null!=e&&e<=e?a[I(o,e,0,i)]:t}function u(){var e=-1;for(o=Array(i);++e<i;)o[e]=((e+1)*n-(e-i)*r)/(i+1);return l}return l.domain=function(e){return arguments.length?([r,n]=e,r*=1,n*=1,u()):[r,n]},l.range=function(e){return arguments.length?(i=(a=Array.from(e)).length-1,u()):a.slice()},l.invertExtent=function(e){var t=a.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,o[0]]:t>=i?[o[i-1],n]:[o[t-1],o[t]]},l.unknown=function(e){return arguments.length&&(t=e),l},l.thresholds=function(){return o.slice()},l.copy=function(){return e().domain([r,n]).range(a).unknown(t)},p.apply(eq(l),arguments)},scaleRadial:()=>function e(){var t,r=eN(),n=[0,1],i=!1;function o(e){var n,o=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(o)?t:i?Math.round(o):o}return o.invert=function(e){return r.invert(e9(e))},o.domain=function(e){return arguments.length?(r.domain(e),o):r.domain()},o.range=function(e){return arguments.length?(r.range((n=Array.from(e,ej)).map(e9)),o):n.slice()},o.rangeRound=function(e){return o.range(e).round(!0)},o.round=function(e){return arguments.length?(i=!!e,o):i},o.clamp=function(e){return arguments.length?(r.clamp(e),o):r.clamp()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.copy=function(){return e(r.domain(),n).round(i).clamp(r.clamp()).unknown(t)},p.apply(o,arguments),eq(o)},scaleSequential:()=>function e(){var t=eq(r2()(e_));return t.copy=function(){return r3(t,e())},y.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=eQ(r2()).domain([1,10]);return t.copy=function(){return r3(t,e()).base(t.base())},y.apply(t,arguments)},scaleSequentialPow:()=>r5,scaleSequentialQuantile:()=>function e(){var t=[],r=e_;function n(e){if(null!=e&&!isNaN(e*=1))return r((I(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r*=1)||t.push(r);return t.sort(k),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t*=1)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n*=1)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t*=1))){if(t<=0||n<2)return tt(e);if(t>=1)return te(e);var n,i=(n-1)*t,o=Math.floor(i),a=te((function e(t,r,n=0,i=1/0,o){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(t.length-1,i)),!(n<=r&&r<=i))return t;for(o=void 0===o?tr:function(e=k){if(e===k)return tr;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(o);i>n;){if(i-n>600){let a=i-n+1,l=r-n+1,u=Math.log(a),s=.5*Math.exp(2*u/3),c=.5*Math.sqrt(u*s*(a-s)/a)*(l-a/2<0?-1:1),f=Math.max(n,Math.floor(r-l*s/a+c)),d=Math.min(i,Math.floor(r+(a-l)*s/a+c));e(t,r,f,d,o)}let a=t[r],l=n,u=i;for(tn(t,n,r),o(t[i],a)>0&&tn(t,n,i);l<u;){for(tn(t,l,u),++l,--u;0>o(t[l],a);)++l;for(;o(t[u],a)>0;)--u}0===o(t[n],a)?tn(t,n,u):tn(t,++u,i),u<=r&&(n=u+1),r<=u&&(i=u-1)}return t})(e,o).subarray(0,o+1));return a+(tt(e.subarray(o+1))-a)*(i-o)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},y.apply(n,arguments)},scaleSequentialSqrt:()=>r4,scaleSequentialSymlog:()=>function e(){var t=e2(r2());return t.copy=function(){return r3(t,e()).constant(t.constant())},y.apply(t,arguments)},scaleSqrt:()=>e7,scaleSymlog:()=>function e(){var t=e2(eC());return t.copy=function(){return eT(t,e()).constant(t.constant())},p.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],i=1;function o(e){return null!=e&&e<=e?n[I(r,e,0,i)]:t}return o.domain=function(e){return arguments.length?(i=Math.min((r=Array.from(e)).length,n.length-1),o):r.slice()},o.range=function(e){return arguments.length?(n=Array.from(e),i=Math.min(r.length,n.length-1),o):n.slice()},o.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},o.unknown=function(e){return arguments.length?(t=e,o):t},o.copy=function(){return e().domain(r).range(n).unknown(t)},p.apply(o,arguments)},scaleTime:()=>r0,scaleUtc:()=>r1,tickFormat:()=>eK});var f=r(76069),d=r(38528),h=r.n(d);function p(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function y(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class g extends Map{constructor(e,t=m){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(v(this,e))}has(e){return super.has(v(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function v({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function m(e){return null!==e&&"object"==typeof e?e.valueOf():e}let b=Symbol("implicit");function w(){var e=new g,t=[],r=[],n=b;function i(i){let o=e.get(i);if(void 0===o){if(n!==b)return n;e.set(i,o=t.push(i)-1)}return r[o%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new g,r))e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return w(t,r).unknown(n)},p.apply(i,arguments),i}function x(){var e,t,r=w().unknown(void 0),n=r.domain,i=r.range,o=0,a=1,l=!1,u=0,s=0,c=.5;function f(){var r=n().length,f=a<o,d=f?a:o,h=f?o:a;e=(h-d)/Math.max(1,r-u+2*s),l&&(e=Math.floor(e)),d+=(h-d-e*(r-u))*c,t=e*(1-u),l&&(d=Math.round(d),t=Math.round(t));var p=(function(e,t,r){e*=1,t*=1,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),o=Array(i);++n<i;)o[n]=e+n*r;return o})(r).map(function(t){return d+e*t});return i(f?p.reverse():p)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([o,a]=e,o*=1,a*=1,f()):[o,a]},r.rangeRound=function(e){return[o,a]=e,o*=1,a*=1,l=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,f()):l},r.padding=function(e){return arguments.length?(u=Math.min(1,s=+e),f()):u},r.paddingInner=function(e){return arguments.length?(u=Math.min(1,e),f()):u},r.paddingOuter=function(e){return arguments.length?(s=+e,f()):s},r.align=function(e){return arguments.length?(c=Math.max(0,Math.min(1,e)),f()):c},r.copy=function(){return x(n(),[o,a]).round(l).paddingInner(u).paddingOuter(s).align(c)},p.apply(f(),arguments)}function O(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(x.apply(null,arguments).paddingInner(1))}let M=Math.sqrt(50),A=Math.sqrt(10),j=Math.sqrt(2);function S(e,t,r){let n,i,o,a=(t-e)/Math.max(0,r),l=Math.floor(Math.log10(a)),u=a/Math.pow(10,l),s=u>=M?10:u>=A?5:u>=j?2:1;return(l<0?(n=Math.round(e*(o=Math.pow(10,-l)/s)),i=Math.round(t*o),n/o<e&&++n,i/o>t&&--i,o=-o):(n=Math.round(e/(o=Math.pow(10,l)*s)),i=Math.round(t/o),n*o<e&&++n,i*o>t&&--i),i<n&&.5<=r&&r<2)?S(e,t,2*r):[n,i,o]}function _(e,t,r){if(t*=1,e*=1,!((r*=1)>0))return[];if(e===t)return[e];let n=t<e,[i,o,a]=n?S(t,e,r):S(e,t,r);if(!(o>=i))return[];let l=o-i+1,u=Array(l);if(n)if(a<0)for(let e=0;e<l;++e)u[e]=-((o-e)/a);else for(let e=0;e<l;++e)u[e]=(o-e)*a;else if(a<0)for(let e=0;e<l;++e)u[e]=-((i+e)/a);else for(let e=0;e<l;++e)u[e]=(i+e)*a;return u}function P(e,t,r){return S(e*=1,t*=1,r*=1)[2]}function E(e,t,r){t*=1,e*=1,r*=1;let n=t<e,i=n?P(t,e,r):P(e,t,r);return(n?-1:1)*(i<0?-(1/i):i)}function k(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function T(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function C(e){let t,r,n;function i(e,n,o=0,a=e.length){if(o<a){if(0!==t(n,n))return a;do{let t=o+a>>>1;0>r(e[t],n)?o=t+1:a=t}while(o<a)}return o}return 2!==e.length?(t=k,r=(t,r)=>k(e(t),r),n=(t,r)=>e(t)-r):(t=e===k||e===T?e:N,r=e,n=e),{left:i,center:function(e,t,r=0,o=e.length){let a=i(e,t,r,o-1);return a>r&&n(e[a-1],t)>-n(e[a],t)?a-1:a},right:function(e,n,i=0,o=e.length){if(i<o){if(0!==t(n,n))return o;do{let t=i+o>>>1;0>=r(e[t],n)?i=t+1:o=t}while(i<o)}return i}}}function N(){return 0}function D(e){return null===e?NaN:+e}let z=C(k),I=z.right;function R(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function L(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function U(){}z.left,C(D).center;var B="\\s*([+-]?\\d+)\\s*",$="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",F="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",H=/^#([0-9a-f]{3,8})$/,K=RegExp(`^rgb\\(${B},${B},${B}\\)$`),q=RegExp(`^rgb\\(${F},${F},${F}\\)$`),W=RegExp(`^rgba\\(${B},${B},${B},${$}\\)$`),Z=RegExp(`^rgba\\(${F},${F},${F},${$}\\)$`),G=RegExp(`^hsl\\(${$},${F},${F}\\)$`),Y=RegExp(`^hsla\\(${$},${F},${F},${$}\\)$`),V={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function X(){return this.rgb().formatHex()}function J(){return this.rgb().formatRgb()}function Q(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=H.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?ee(t):3===r?new en(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?et(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?et(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=K.exec(e))?new en(t[1],t[2],t[3],1):(t=q.exec(e))?new en(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=W.exec(e))?et(t[1],t[2],t[3],t[4]):(t=Z.exec(e))?et(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=G.exec(e))?es(t[1],t[2]/100,t[3]/100,1):(t=Y.exec(e))?es(t[1],t[2]/100,t[3]/100,t[4]):V.hasOwnProperty(e)?ee(V[e]):"transparent"===e?new en(NaN,NaN,NaN,0):null}function ee(e){return new en(e>>16&255,e>>8&255,255&e,1)}function et(e,t,r,n){return n<=0&&(e=t=r=NaN),new en(e,t,r,n)}function er(e,t,r,n){var i;return 1==arguments.length?((i=e)instanceof U||(i=Q(i)),i)?new en((i=i.rgb()).r,i.g,i.b,i.opacity):new en:new en(e,t,r,null==n?1:n)}function en(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function ei(){return`#${eu(this.r)}${eu(this.g)}${eu(this.b)}`}function eo(){let e=ea(this.opacity);return`${1===e?"rgb(":"rgba("}${el(this.r)}, ${el(this.g)}, ${el(this.b)}${1===e?")":`, ${e})`}`}function ea(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function el(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function eu(e){return((e=el(e))<16?"0":"")+e.toString(16)}function es(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new ef(e,t,r,n)}function ec(e){if(e instanceof ef)return new ef(e.h,e.s,e.l,e.opacity);if(e instanceof U||(e=Q(e)),!e)return new ef;if(e instanceof ef)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),o=Math.max(t,r,n),a=NaN,l=o-i,u=(o+i)/2;return l?(a=t===o?(r-n)/l+(r<n)*6:r===o?(n-t)/l+2:(t-r)/l+4,l/=u<.5?o+i:2-o-i,a*=60):l=u>0&&u<1?0:a,new ef(a,l,u,e.opacity)}function ef(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function ed(e){return(e=(e||0)%360)<0?e+360:e}function eh(e){return Math.max(0,Math.min(1,e||0))}function ep(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function ey(e,t,r,n,i){var o=e*e,a=o*e;return((1-3*e+3*o-a)*t+(4-6*o+3*a)*r+(1+3*e+3*o-3*a)*n+a*i)/6}R(U,Q,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:X,formatHex:X,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return ec(this).formatHsl()},formatRgb:J,toString:J}),R(en,er,L(U,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new en(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new en(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new en(el(this.r),el(this.g),el(this.b),ea(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ei,formatHex:ei,formatHex8:function(){return`#${eu(this.r)}${eu(this.g)}${eu(this.b)}${eu((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:eo,toString:eo})),R(ef,function(e,t,r,n){return 1==arguments.length?ec(e):new ef(e,t,r,null==n?1:n)},L(U,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new ef(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new ef(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new en(ep(e>=240?e-240:e+120,i,n),ep(e,i,n),ep(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new ef(ed(this.h),eh(this.s),eh(this.l),ea(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=ea(this.opacity);return`${1===e?"hsl(":"hsla("}${ed(this.h)}, ${100*eh(this.s)}%, ${100*eh(this.l)}%${1===e?")":`, ${e})`}`}}));let eg=e=>()=>e;function ev(e,t){var r=t-e;return r?function(t){return e+t*r}:eg(isNaN(e)?t:e)}let em=function e(t){var r,n=1==(r=+t)?ev:function(e,t){var n,i,o;return t-e?(n=e,i=t,n=Math.pow(n,o=r),i=Math.pow(i,o)-n,o=1/o,function(e){return Math.pow(n+e*i,o)}):eg(isNaN(e)?t:e)};function i(e,t){var r=n((e=er(e)).r,(t=er(t)).r),i=n(e.g,t.g),o=n(e.b,t.b),a=ev(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=i(t),e.b=o(t),e.opacity=a(t),e+""}}return i.gamma=e,i}(1);function eb(e){return function(t){var r,n,i=t.length,o=Array(i),a=Array(i),l=Array(i);for(r=0;r<i;++r)n=er(t[r]),o[r]=n.r||0,a[r]=n.g||0,l[r]=n.b||0;return o=e(o),a=e(a),l=e(l),n.opacity=1,function(e){return n.r=o(e),n.g=a(e),n.b=l(e),n+""}}}function ew(e,t){return e*=1,t*=1,function(r){return e*(1-r)+t*r}}eb(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),i=e[n],o=e[n+1],a=n>0?e[n-1]:2*i-o,l=n<t-1?e[n+2]:2*o-i;return ey((r-n/t)*t,a,i,o,l)}}),eb(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),i=e[(n+t-1)%t],o=e[n%t],a=e[(n+1)%t],l=e[(n+2)%t];return ey((r-n/t)*t,i,o,a,l)}});var ex=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,eO=RegExp(ex.source,"g");function eM(e,t){var r,n,i=typeof t;return null==t||"boolean"===i?eg(t):("number"===i?ew:"string"===i?(n=Q(t))?(t=n,em):function(e,t){var r,n,i,o,a,l=ex.lastIndex=eO.lastIndex=0,u=-1,s=[],c=[];for(e+="",t+="";(i=ex.exec(e))&&(o=eO.exec(t));)(a=o.index)>l&&(a=t.slice(l,a),s[u]?s[u]+=a:s[++u]=a),(i=i[0])===(o=o[0])?s[u]?s[u]+=o:s[++u]=o:(s[++u]=null,c.push({i:u,x:ew(i,o)})),l=eO.lastIndex;return l<t.length&&(a=t.slice(l),s[u]?s[u]+=a:s[++u]=a),s.length<2?c[0]?(r=c[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=c.length,function(e){for(var r,n=0;n<t;++n)s[(r=c[n]).i]=r.x(e);return s.join("")})}:t instanceof Q?em:t instanceof Date?function(e,t){var r=new Date;return e*=1,t*=1,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,o=Array(i),a=Array(n);for(r=0;r<i;++r)o[r]=eM(e[r],t[r]);for(;r<n;++r)a[r]=t[r];return function(e){for(r=0;r<i;++r)a[r]=o[r](e);return a}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},i={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=eM(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}:ew:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(o){for(r=0;r<n;++r)i[r]=e[r]*(1-o)+t[r]*o;return i}})(e,t)}function eA(e,t){return e*=1,t*=1,function(r){return Math.round(e*(1-r)+t*r)}}function ej(e){return+e}var eS=[0,1];function e_(e){return e}function eP(e,t){var r;return(t-=e*=1)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function eE(e,t,r){var n=e[0],i=e[1],o=t[0],a=t[1];return i<n?(n=eP(i,n),o=r(a,o)):(n=eP(n,i),o=r(o,a)),function(e){return o(n(e))}}function ek(e,t,r){var n=Math.min(e.length,t.length)-1,i=Array(n),o=Array(n),a=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++a<n;)i[a]=eP(e[a],e[a+1]),o[a]=r(t[a],t[a+1]);return function(t){var r=I(e,t,1,n)-1;return o[r](i[r](t))}}function eT(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function eC(){var e,t,r,n,i,o,a=eS,l=eS,u=eM,s=e_;function c(){var e,t,r,u=Math.min(a.length,l.length);return s!==e_&&(e=a[0],t=a[u-1],e>t&&(r=e,e=t,t=r),s=function(r){return Math.max(e,Math.min(t,r))}),n=u>2?ek:eE,i=o=null,f}function f(t){return null==t||isNaN(t*=1)?r:(i||(i=n(a.map(e),l,u)))(e(s(t)))}return f.invert=function(r){return s(t((o||(o=n(l,a.map(e),ew)))(r)))},f.domain=function(e){return arguments.length?(a=Array.from(e,ej),c()):a.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),c()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),u=eA,c()},f.clamp=function(e){return arguments.length?(s=!!e||e_,c()):s!==e_},f.interpolate=function(e){return arguments.length?(u=e,c()):u},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,c()}}function eN(){return eC()(e_,e_)}var eD=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function ez(e){var t;if(!(t=eD.exec(e)))throw Error("invalid format: "+e);return new eI({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function eI(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function eR(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function eL(e){return(e=eR(Math.abs(e)))?e[1]:NaN}function eU(e,t){var r=eR(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}ez.prototype=eI.prototype,eI.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let eB={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>eU(100*e,t),r:eU,s:function(e,t){var r=eR(e,t);if(!r)return e+"";var i=r[0],o=r[1],a=o-(n=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,l=i.length;return a===l?i:a>l?i+Array(a-l+1).join("0"):a>0?i.slice(0,a)+"."+i.slice(a):"0."+Array(1-a).join("0")+eR(e,Math.max(0,t+a-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function e$(e){return e}var eF=Array.prototype.map,eH=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function eK(e,t,r,n){var i,l,u=E(e,t,r);switch((n=ez(null==n?",f":n)).type){case"s":var s=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(l=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(eL(s)/3)))-eL(Math.abs(u))))||(n.precision=l),a(n,s);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(l=Math.max(0,eL(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(i=Math.abs(i=u)))-eL(i))+1)||(n.precision=l-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(l=Math.max(0,-eL(Math.abs(u))))||(n.precision=l-("%"===n.type)*2)}return o(n)}function eq(e){var t=e.domain;return e.ticks=function(e){var r=t();return _(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return eK(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,o=t(),a=0,l=o.length-1,u=o[a],s=o[l],c=10;for(s<u&&(i=u,u=s,s=i,i=a,a=l,l=i);c-- >0;){if((i=P(u,s,r))===n)return o[a]=u,o[l]=s,t(o);if(i>0)u=Math.floor(u/i)*i,s=Math.ceil(s/i)*i;else if(i<0)u=Math.ceil(u*i)/i,s=Math.floor(s*i)/i;else break;n=i}return e},e}function eW(e,t){e=e.slice();var r,n=0,i=e.length-1,o=e[n],a=e[i];return a<o&&(r=n,n=i,i=r,r=o,o=a,a=r),e[n]=t.floor(o),e[i]=t.ceil(a),e}function eZ(e){return Math.log(e)}function eG(e){return Math.exp(e)}function eY(e){return-Math.log(-e)}function eV(e){return-Math.exp(-e)}function eX(e){return isFinite(e)?+("1e"+e):e<0?0:e}function eJ(e){return(t,r)=>-e(-t,r)}function eQ(e){let t,r,n=e(eZ,eG),i=n.domain,a=10;function l(){var o,l;return t=(o=a)===Math.E?Math.log:10===o&&Math.log10||2===o&&Math.log2||(o=Math.log(o),e=>Math.log(e)/o),r=10===(l=a)?eX:l===Math.E?Math.exp:e=>Math.pow(l,e),i()[0]<0?(t=eJ(t),r=eJ(r),e(eY,eV)):e(eZ,eG),n}return n.base=function(e){return arguments.length?(a=+e,l()):a},n.domain=function(e){return arguments.length?(i(e),l()):i()},n.ticks=e=>{let n,o,l=i(),u=l[0],s=l[l.length-1],c=s<u;c&&([u,s]=[s,u]);let f=t(u),d=t(s),h=null==e?10:+e,p=[];if(!(a%1)&&d-f<h){if(f=Math.floor(f),d=Math.ceil(d),u>0){for(;f<=d;++f)for(n=1;n<a;++n)if(!((o=f<0?n/r(-f):n*r(f))<u)){if(o>s)break;p.push(o)}}else for(;f<=d;++f)for(n=a-1;n>=1;--n)if(!((o=f>0?n/r(-f):n*r(f))<u)){if(o>s)break;p.push(o)}2*p.length<h&&(p=_(u,s,h))}else p=_(f,d,Math.min(d-f,h)).map(r);return c?p.reverse():p},n.tickFormat=(e,i)=>{if(null==e&&(e=10),null==i&&(i=10===a?"s":","),"function"!=typeof i&&(a%1||null!=(i=ez(i)).precision||(i.trim=!0),i=o(i)),e===1/0)return i;let l=Math.max(1,a*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*a<a-.5&&(n*=a),n<=l?i(e):""}},n.nice=()=>i(eW(i(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function e0(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function e1(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function e2(e){var t=1,r=e(e0(1),e1(t));return r.constant=function(r){return arguments.length?e(e0(t=+r),e1(t)):t},eq(r)}function e3(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function e5(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function e4(e){return e<0?-e*e:e*e}function e8(e){var t=e(e_,e_),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(e_,e_):.5===r?e(e5,e4):e(e3(r),e3(1/r)):r},eq(t)}function e6(){var e=e8(eC());return e.copy=function(){return eT(e,e6()).exponent(e.exponent())},p.apply(e,arguments),e}function e7(){return e6.apply(null,arguments).exponent(.5)}function e9(e){return Math.sign(e)*e*e}function te(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function tt(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}function tr(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:+(e>t))}function tn(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}o=(i=function(e){var t,r,i,o=void 0===e.grouping||void 0===e.thousands?e$:(t=eF.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var i=e.length,o=[],a=0,l=t[0],u=0;i>0&&l>0&&(u+l+1>n&&(l=Math.max(1,n-u)),o.push(e.substring(i-=l,i+l)),!((u+=l+1)>n));)l=t[a=(a+1)%t.length];return o.reverse().join(r)}),a=void 0===e.currency?"":e.currency[0]+"",l=void 0===e.currency?"":e.currency[1]+"",u=void 0===e.decimal?".":e.decimal+"",s=void 0===e.numerals?e$:(i=eF.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return i[+e]})}),c=void 0===e.percent?"%":e.percent+"",f=void 0===e.minus?"−":e.minus+"",d=void 0===e.nan?"NaN":e.nan+"";function h(e){var t=(e=ez(e)).fill,r=e.align,i=e.sign,h=e.symbol,p=e.zero,y=e.width,g=e.comma,v=e.precision,m=e.trim,b=e.type;"n"===b?(g=!0,b="g"):eB[b]||(void 0===v&&(v=12),m=!0,b="g"),(p||"0"===t&&"="===r)&&(p=!0,t="0",r="=");var w="$"===h?a:"#"===h&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===h?l:/[%p]/.test(b)?c:"",O=eB[b],M=/[defgprs%]/.test(b);function A(e){var a,l,c,h=w,A=x;if("c"===b)A=O(e)+A,e="";else{var j=(e*=1)<0||1/e<0;if(e=isNaN(e)?d:O(Math.abs(e),v),m&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),j&&0==+e&&"+"!==i&&(j=!1),h=(j?"("===i?i:f:"-"===i||"("===i?"":i)+h,A=("s"===b?eH[8+n/3]:"")+A+(j&&"("===i?")":""),M){for(a=-1,l=e.length;++a<l;)if(48>(c=e.charCodeAt(a))||c>57){A=(46===c?u+e.slice(a+1):e.slice(a))+A,e=e.slice(0,a);break}}}g&&!p&&(e=o(e,1/0));var S=h.length+e.length+A.length,_=S<y?Array(y-S+1).join(t):"";switch(g&&p&&(e=o(_+e,_.length?y-A.length:1/0),_=""),r){case"<":e=h+e+A+_;break;case"=":e=h+_+e+A;break;case"^":e=_.slice(0,S=_.length>>1)+h+e+A+_.slice(S);break;default:e=_+h+e+A}return s(e)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),A.toString=function(){return e+""},A}return{format:h,formatPrefix:function(e,t){var r=h(((e=ez(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(eL(t)/3))),i=Math.pow(10,-n),o=eH[8+n/3];return function(e){return r(i*e)+o}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,a=i.formatPrefix;let ti=new Date,to=new Date;function ta(e,t,r,n){function i(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{let t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,o)=>{let a,l=[];if(r=i.ceil(r),o=null==o?1:Math.floor(o),!(r<n)||!(o>0))return l;do l.push(a=new Date(+r)),t(r,o),e(r);while(a<r&&r<n);return l},i.filter=r=>ta(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}),r&&(i.count=(t,n)=>(ti.setTime(+t),to.setTime(+n),e(ti),e(to),Math.floor(r(ti,to))),i.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?i.filter(n?t=>n(t)%e==0:t=>i.count(0,t)%e==0):i:null),i}let tl=ta(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);tl.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?ta(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):tl:null,tl.range;let tu=ta(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());tu.range;let ts=ta(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());ts.range;let tc=ta(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());tc.range;let tf=ta(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());tf.range;let td=ta(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());td.range;let th=ta(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);th.range;let tp=ta(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);tp.range;let ty=ta(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function tg(e){return ta(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}ty.range;let tv=tg(0),tm=tg(1),tb=tg(2),tw=tg(3),tx=tg(4),tO=tg(5),tM=tg(6);function tA(e){return ta(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}tv.range,tm.range,tb.range,tw.range,tx.range,tO.range,tM.range;let tj=tA(0),tS=tA(1),t_=tA(2),tP=tA(3),tE=tA(4),tk=tA(5),tT=tA(6);tj.range,tS.range,t_.range,tP.range,tE.range,tk.range,tT.range;let tC=ta(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());tC.range;let tN=ta(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());tN.range;let tD=ta(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());tD.every=e=>isFinite(e=Math.floor(e))&&e>0?ta(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,tD.range;let tz=ta(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function tI(e,t,r,n,i,o){let a=[[tu,1,1e3],[tu,5,5e3],[tu,15,15e3],[tu,30,3e4],[o,1,6e4],[o,5,3e5],[o,15,9e5],[o,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function l(t,r,n){let i=Math.abs(r-t)/n,o=C(([,,e])=>e).right(a,i);if(o===a.length)return e.every(E(t/31536e6,r/31536e6,n));if(0===o)return tl.every(Math.max(E(t,r,n),1));let[l,u]=a[i/a[o-1][2]<a[o][2]/i?o-1:o];return l.every(u)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let i=r&&"function"==typeof r.range?r:l(e,t,r),o=i?i.range(e,+t+1):[];return n?o.reverse():o},l]}tz.every=e=>isFinite(e=Math.floor(e))&&e>0?ta(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,tz.range;let[tR,tL]=tI(tz,tN,tj,ty,td,tc),[tU,tB]=tI(tD,tC,tv,th,tf,ts);function t$(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function tF(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function tH(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var tK={"-":"",_:" ",0:"0"},tq=/^\s*\d+/,tW=/^%/,tZ=/[\\^$*+?|[\]().{}]/g;function tG(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",o=i.length;return n+(o<r?Array(r-o+1).join(t)+i:i)}function tY(e){return e.replace(tZ,"\\$&")}function tV(e){return RegExp("^(?:"+e.map(tY).join("|")+")","i")}function tX(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function tJ(e,t,r){var n=tq.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function tQ(e,t,r){var n=tq.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function t0(e,t,r){var n=tq.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function t1(e,t,r){var n=tq.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function t2(e,t,r){var n=tq.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function t3(e,t,r){var n=tq.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function t5(e,t,r){var n=tq.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function t4(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function t8(e,t,r){var n=tq.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function t6(e,t,r){var n=tq.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function t7(e,t,r){var n=tq.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function t9(e,t,r){var n=tq.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function re(e,t,r){var n=tq.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function rt(e,t,r){var n=tq.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function rr(e,t,r){var n=tq.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function rn(e,t,r){var n=tq.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function ri(e,t,r){var n=tq.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function ro(e,t,r){var n=tW.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function ra(e,t,r){var n=tq.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function rl(e,t,r){var n=tq.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function ru(e,t){return tG(e.getDate(),t,2)}function rs(e,t){return tG(e.getHours(),t,2)}function rc(e,t){return tG(e.getHours()%12||12,t,2)}function rf(e,t){return tG(1+th.count(tD(e),e),t,3)}function rd(e,t){return tG(e.getMilliseconds(),t,3)}function rh(e,t){return rd(e,t)+"000"}function rp(e,t){return tG(e.getMonth()+1,t,2)}function ry(e,t){return tG(e.getMinutes(),t,2)}function rg(e,t){return tG(e.getSeconds(),t,2)}function rv(e){var t=e.getDay();return 0===t?7:t}function rm(e,t){return tG(tv.count(tD(e)-1,e),t,2)}function rb(e){var t=e.getDay();return t>=4||0===t?tx(e):tx.ceil(e)}function rw(e,t){return e=rb(e),tG(tx.count(tD(e),e)+(4===tD(e).getDay()),t,2)}function rx(e){return e.getDay()}function rO(e,t){return tG(tm.count(tD(e)-1,e),t,2)}function rM(e,t){return tG(e.getFullYear()%100,t,2)}function rA(e,t){return tG((e=rb(e)).getFullYear()%100,t,2)}function rj(e,t){return tG(e.getFullYear()%1e4,t,4)}function rS(e,t){var r=e.getDay();return tG((e=r>=4||0===r?tx(e):tx.ceil(e)).getFullYear()%1e4,t,4)}function r_(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+tG(t/60|0,"0",2)+tG(t%60,"0",2)}function rP(e,t){return tG(e.getUTCDate(),t,2)}function rE(e,t){return tG(e.getUTCHours(),t,2)}function rk(e,t){return tG(e.getUTCHours()%12||12,t,2)}function rT(e,t){return tG(1+tp.count(tz(e),e),t,3)}function rC(e,t){return tG(e.getUTCMilliseconds(),t,3)}function rN(e,t){return rC(e,t)+"000"}function rD(e,t){return tG(e.getUTCMonth()+1,t,2)}function rz(e,t){return tG(e.getUTCMinutes(),t,2)}function rI(e,t){return tG(e.getUTCSeconds(),t,2)}function rR(e){var t=e.getUTCDay();return 0===t?7:t}function rL(e,t){return tG(tj.count(tz(e)-1,e),t,2)}function rU(e){var t=e.getUTCDay();return t>=4||0===t?tE(e):tE.ceil(e)}function rB(e,t){return e=rU(e),tG(tE.count(tz(e),e)+(4===tz(e).getUTCDay()),t,2)}function r$(e){return e.getUTCDay()}function rF(e,t){return tG(tS.count(tz(e)-1,e),t,2)}function rH(e,t){return tG(e.getUTCFullYear()%100,t,2)}function rK(e,t){return tG((e=rU(e)).getUTCFullYear()%100,t,2)}function rq(e,t){return tG(e.getUTCFullYear()%1e4,t,4)}function rW(e,t){var r=e.getUTCDay();return tG((e=r>=4||0===r?tE(e):tE.ceil(e)).getUTCFullYear()%1e4,t,4)}function rZ(){return"+0000"}function rG(){return"%"}function rY(e){return+e}function rV(e){return Math.floor(e/1e3)}function rX(e){return new Date(e)}function rJ(e){return e instanceof Date?+e:+new Date(+e)}function rQ(e,t,r,n,i,o,a,l,u,s){var c=eN(),f=c.invert,d=c.domain,h=s(".%L"),p=s(":%S"),y=s("%I:%M"),g=s("%I %p"),v=s("%a %d"),m=s("%b %d"),b=s("%B"),w=s("%Y");function x(e){return(u(e)<e?h:l(e)<e?p:a(e)<e?y:o(e)<e?g:n(e)<e?i(e)<e?v:m:r(e)<e?b:w)(e)}return c.invert=function(e){return new Date(f(e))},c.domain=function(e){return arguments.length?d(Array.from(e,rJ)):d().map(rX)},c.ticks=function(t){var r=d();return e(r[0],r[r.length-1],null==t?10:t)},c.tickFormat=function(e,t){return null==t?x:s(t)},c.nice=function(e){var r=d();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?d(eW(r,e)):c},c.copy=function(){return eT(c,rQ(e,t,r,n,i,o,a,l,u,s))},c}function r0(){return p.apply(rQ(tU,tB,tD,tC,tv,th,tf,ts,tu,u).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function r1(){return p.apply(rQ(tR,tL,tz,tN,tj,tp,td,tc,tu,s).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function r2(){var e,t,r,n,i,o=0,a=1,l=e_,u=!1;function s(t){return null==t||isNaN(t*=1)?i:l(0===r?.5:(t=(n(t)-e)*r,u?Math.max(0,Math.min(1,t)):t))}function c(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),s):[l(0),l(1)]}}return s.domain=function(i){return arguments.length?([o,a]=i,e=n(o*=1),t=n(a*=1),r=e===t?0:1/(t-e),s):[o,a]},s.clamp=function(e){return arguments.length?(u=!!e,s):u},s.interpolator=function(e){return arguments.length?(l=e,s):l},s.range=c(eM),s.rangeRound=c(eA),s.unknown=function(e){return arguments.length?(i=e,s):i},function(i){return n=i,e=i(o),t=i(a),r=e===t?0:1/(t-e),s}}function r3(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function r5(){var e=e8(r2());return e.copy=function(){return r3(e,r5()).exponent(e.exponent())},y.apply(e,arguments)}function r4(){return r5.apply(null,arguments).exponent(.5)}function r8(){var e,t,r,n,i,o,a,l=0,u=.5,s=1,c=1,f=e_,d=!1;function h(e){return isNaN(e*=1)?a:(e=.5+((e=+o(e))-t)*(c*e<c*t?n:i),f(d?Math.max(0,Math.min(1,e)):e))}function p(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=eM);for(var r=0,n=t.length-1,i=t[0],o=Array(n<0?0:n);r<n;)o[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return o[t](e-t)}}(e,[r,n,i]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([l,u,s]=a,e=o(l*=1),t=o(u*=1),r=o(s*=1),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),c=t<e?-1:1,h):[l,u,s]},h.clamp=function(e){return arguments.length?(d=!!e,h):d},h.interpolator=function(e){return arguments.length?(f=e,h):f},h.range=p(eM),h.rangeRound=p(eA),h.unknown=function(e){return arguments.length?(a=e,h):a},function(a){return o=a,e=a(l),t=a(u),r=a(s),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),c=t<e?-1:1,h}}function r6(){var e=e8(r8());return e.copy=function(){return r3(e,r6()).exponent(e.exponent())},y.apply(e,arguments)}function r7(){return r6.apply(null,arguments).exponent(.5)}u=(l=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,o=e.days,a=e.shortDays,l=e.months,u=e.shortMonths,s=tV(i),c=tX(i),f=tV(o),d=tX(o),h=tV(a),p=tX(a),y=tV(l),g=tX(l),v=tV(u),m=tX(u),b={a:function(e){return a[e.getDay()]},A:function(e){return o[e.getDay()]},b:function(e){return u[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:ru,e:ru,f:rh,g:rA,G:rS,H:rs,I:rc,j:rf,L:rd,m:rp,M:ry,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:rY,s:rV,S:rg,u:rv,U:rm,V:rw,w:rx,W:rO,x:null,X:null,y:rM,Y:rj,Z:r_,"%":rG},w={a:function(e){return a[e.getUTCDay()]},A:function(e){return o[e.getUTCDay()]},b:function(e){return u[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:rP,e:rP,f:rN,g:rK,G:rW,H:rE,I:rk,j:rT,L:rC,m:rD,M:rz,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:rY,s:rV,S:rI,u:rR,U:rL,V:rB,w:r$,W:rF,x:null,X:null,y:rH,Y:rq,Z:rZ,"%":rG},x={a:function(e,t,r){var n=h.exec(t.slice(r));return n?(e.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=v.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=g.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return A(e,t,r,n)},d:t7,e:t7,f:ri,g:t5,G:t3,H:re,I:re,j:t9,L:rn,m:t6,M:rt,p:function(e,t,r){var n=s.exec(t.slice(r));return n?(e.p=c.get(n[0].toLowerCase()),r+n[0].length):-1},q:t8,Q:ra,s:rl,S:rr,u:tQ,U:t0,V:t1,w:tJ,W:t2,x:function(e,t,n){return A(e,r,t,n)},X:function(e,t,r){return A(e,n,t,r)},y:t5,Y:t3,Z:t4,"%":ro};function O(e,t){return function(r){var n,i,o,a=[],l=-1,u=0,s=e.length;for(r instanceof Date||(r=new Date(+r));++l<s;)37===e.charCodeAt(l)&&(a.push(e.slice(u,l)),null!=(i=tK[n=e.charAt(++l)])?n=e.charAt(++l):i="e"===n?" ":"0",(o=t[n])&&(n=o(r,i)),a.push(n),u=l+1);return a.push(e.slice(u,l)),a.join("")}}function M(e,t){return function(r){var n,i,o=tH(1900,void 0,1);if(A(o,e,r+="",0)!=r.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(1e3*o.s+("L"in o?o.L:0));if(!t||"Z"in o||(o.Z=0),"p"in o&&(o.H=o.H%12+12*o.p),void 0===o.m&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(n=(i=(n=tF(tH(o.y,0,1))).getUTCDay())>4||0===i?tS.ceil(n):tS(n),n=tp.offset(n,(o.V-1)*7),o.y=n.getUTCFullYear(),o.m=n.getUTCMonth(),o.d=n.getUTCDate()+(o.w+6)%7):(n=(i=(n=t$(tH(o.y,0,1))).getDay())>4||0===i?tm.ceil(n):tm(n),n=th.offset(n,(o.V-1)*7),o.y=n.getFullYear(),o.m=n.getMonth(),o.d=n.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:+("W"in o)),i="Z"in o?tF(tH(o.y,0,1)).getUTCDay():t$(tH(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(i+5)%7:o.w+7*o.U-(i+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,tF(o)):t$(o)}}function A(e,t,r,n){for(var i,o,a=0,l=t.length,u=r.length;a<l;){if(n>=u)return -1;if(37===(i=t.charCodeAt(a++))){if(!(o=x[(i=t.charAt(a++))in tK?t.charAt(a++):i])||(n=o(e,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),w.x=O(r,w),w.X=O(n,w),w.c=O(t,w),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=M(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",w);return t.toString=function(){return e},t},utcParse:function(e){var t=M(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,l.parse,s=l.utcFormat,l.utcParse;var r9=r(90167),ne=r(51023),nt=r(90135),nr=r(49580),nn=r(92377);function ni(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if((0,nn.H)(t)&&(0,nn.H)(r))return!0}return!1}function no(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var na=r(29533),nl=r.n(na),nu=e=>e,ns={},nc=e=>function t(){let r;return 0==arguments.length||1==arguments.length&&(r=arguments.length<=0?void 0:arguments[0],r===ns)?t:e(...arguments)},nf=(e,t)=>1===e?t:nc(function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var o=n.filter(e=>e!==ns).length;return o>=e?t(...n):nf(e-o,nc(function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return t(...n.map(e=>e===ns?r.shift():e),...r)}))}),nd=e=>nf(e.length,e),nh=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},np=nd((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),ny=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return nu;var n=t.reverse(),i=n[0],o=n.slice(1);return function(){return o.reduce((e,t)=>t(e),i(...arguments))}},ng=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),nv=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return t&&i.every((e,r)=>{var n;return e===(null==(n=t)?void 0:n[r])})?r:(t=i,r=e(...i))}};function nm(e){return 0===e?1:Math.floor(new(nl())(e).abs().log(10).toNumber())+1}function nb(e,t,r){for(var n=new(nl())(e),i=0,o=[];n.lt(t)&&i<1e5;)o.push(n.toNumber()),n=n.add(r),i++;return o}nd((e,t,r)=>{var n=+e;return n+r*(t-n)}),nd((e,t,r)=>{var n=t-e;return(r-e)/(n=n||1/0)}),nd((e,t,r)=>{var n=t-e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});var nw=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},nx=(e,t,r)=>{if(e.lte(0))return new(nl())(0);var n=nm(e.toNumber()),i=new(nl())(10).pow(n),o=e.div(i),a=1!==n?.05:.1,l=new(nl())(Math.ceil(o.div(a).toNumber())).add(r).mul(a).mul(i);return new(nl())(t?l.toNumber():Math.ceil(l.toNumber()))},nO=function(e,t,r,n){var i,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new(nl())(0),tickMin:new(nl())(0),tickMax:new(nl())(0)};var a=nx(new(nl())(t).sub(e).div(r-1),n,o),l=Math.ceil((i=e<=0&&t>=0?new(nl())(0):(i=new(nl())(e).add(t).div(2)).sub(new(nl())(i).mod(a))).sub(e).div(a).toNumber()),u=Math.ceil(new(nl())(t).sub(i).div(a).toNumber()),s=l+u+1;return s>r?nO(e,t,r,n,o+1):(s<r&&(u=t>0?u+(r-s):u,l=t>0?l:l+(r-s)),{step:a,tickMin:i.sub(new(nl())(l).mul(a)),tickMax:i.add(new(nl())(u).mul(a))})},nM=nv(function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],o=Math.max(n,2),[a,l]=nw([t,r]);if(a===-1/0||l===1/0){var u=l===1/0?[a,...nh(0,n-1).map(()=>1/0)]:[...nh(0,n-1).map(()=>-1/0),l];return t>r?ng(u):u}if(a===l){var s=new(nl())(1),c=new(nl())(a);if(!c.isint()&&i){var f=Math.abs(a);f<1?(s=new(nl())(10).pow(nm(a)-1),c=new(nl())(Math.floor(c.div(s).toNumber())).mul(s)):f>1&&(c=new(nl())(Math.floor(a)))}else 0===a?c=new(nl())(Math.floor((n-1)/2)):i||(c=new(nl())(Math.floor(a)));var d=Math.floor((n-1)/2);return ny(np(e=>c.add(new(nl())(e-d).mul(s)).toNumber()),nh)(0,n)}var{step:h,tickMin:p,tickMax:y}=nO(a,l,o,i,0),g=nb(p,y.add(new(nl())(.1).mul(h)),h);return t>r?ng(g):g}),nA=nv(function(e,t){var[r,n]=e,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[o,a]=nw([r,n]);if(o===-1/0||a===1/0)return[r,n];if(o===a)return[o];var l=Math.max(t,2),u=nx(new(nl())(a).sub(o).div(l-1),i,0),s=[...nb(new(nl())(o),new(nl())(a),u),a];return!1===i&&(s=s.map(e=>Math.round(e))),r>n?ng(s):s}),nj=r(27588),nS=r(49507),n_=r(8291),nP=r(50257),nE=r(15195),nk=r(14821),nT=r(72259),nC=r(13802),nN=r(15145),nD=r(34264),nz=r(34565),nI=r(84811),nR=r(24021),nL=r(35923);function nU(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nB(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nU(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nU(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var n$=[0,"auto"],nF={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},nH=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?nF:r},nK={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:n$,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:nD.tQ},nq=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?nK:r},nW={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},nZ=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?nW:r},nG=(e,t,r)=>{switch(t){case"xAxis":return nH(e,r);case"yAxis":return nq(e,r);case"zAxis":return nZ(e,r);case"angleAxis":return(0,nk.Be)(e,r);case"radiusAxis":return(0,nk.Gl)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},nY=(e,t,r)=>{switch(t){case"xAxis":return nH(e,r);case"yAxis":return nq(e,r);case"angleAxis":return(0,nk.Be)(e,r);case"radiusAxis":return(0,nk.Gl)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},nV=e=>e.graphicalItems.cartesianItems.some(e=>"bar"===e.type)||e.graphicalItems.polarItems.some(e=>"radialBar"===e.type);function nX(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var nJ=e=>e.graphicalItems.cartesianItems,nQ=(0,f.Mz)([nT.N,nC.E],nX),n0=(e,t,r)=>e.filter(r).filter(e=>(null==t?void 0:t.includeHidden)===!0||!e.hide),n1=(0,f.Mz)([nJ,nG,nQ],n0),n2=(0,f.Mz)([n1],e=>e.filter(e=>"area"===e.type||"bar"===e.type).filter(nL.g)),n3=e=>e.filter(e=>!("stackId"in e)||void 0===e.stackId),n5=(0,f.Mz)([n1],n3),n4=e=>e.map(e=>e.data).filter(Boolean).flat(1),n8=(0,f.Mz)([n1],n4),n6=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},n7=(0,f.Mz)([n8,nt.HS],n6),n9=(e,t,r)=>(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,ne.kr)(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:(0,ne.kr)(e,t)}))):e.map(e=>({value:e})),ie=(0,f.Mz)([n7,nG,n1],n9);function it(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function ir(e){return e.filter(e=>(0,nr.vh)(e)||e instanceof Date).map(Number).filter(e=>!1===(0,nr.M8)(e))}var ii=(0,f.Mz)([n2,nt.HS,nI.D],nR.A),io=(e,t,r)=>Object.fromEntries(Object.entries(t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{})).map(t=>{var[n,i]=t,o=i.map(nz.x);return[n,{stackedData:(0,ne.yy)(e,o,r),graphicalItems:i}]})),ia=(0,f.Mz)([ii,n2,nE.eC],io),il=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if("zAxis"!==r){var o=(0,ne.Mk)(e,n,i);if(null==o||0!==o[0]||0!==o[1])return o}},iu=(0,f.Mz)([ia,nt.LF,nT.N],il),is=(e,t,r,n,i)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var o,a,l=null==(o=n[r.id])?void 0:o.filter(e=>it(i,e)),u=(0,ne.kr)(e,null!=(a=t.dataKey)?a:r.dataKey);return{value:u,errorDomain:function(e,t,r){return!r||"number"!=typeof t||(0,nr.M8)(t)||!r.length?[]:ir(r.flatMap(r=>{var n,i,o=(0,ne.kr)(e,r.dataKey);if(Array.isArray(o)?[n,i]=o:n=i=o,(0,nn.H)(n)&&(0,nn.H)(i))return[t-n,t+i]}))}(e,u,l)}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,ne.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),ic=e=>e.errorBars,id=(e,t,r)=>e.flatMap(e=>t[e.id]).filter(Boolean).filter(e=>it(r,e));(0,f.Mz)([n5,ic,nT.N],id);var ih=(0,f.Mz)([n7,nG,n5,ic,nT.N],is);function ip(e){var{value:t}=e;if((0,nr.vh)(t)||t instanceof Date)return t}var iy=e=>{var t=ir(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]},ig=e=>{var t;if(null==e||!("domain"in e))return n$;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=ir(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!=(t=null==e?void 0:e.domain)?t:n$},iv=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},im=e=>e.referenceElements.dots,ib=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),iw=(0,f.Mz)([im,nT.N,nC.E],ib),ix=e=>e.referenceElements.areas,iO=(0,f.Mz)([ix,nT.N,nC.E],ib),iM=e=>e.referenceElements.lines,iA=(0,f.Mz)([iM,nT.N,nC.E],ib),ij=(e,t)=>{var r=ir(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},iS=(0,f.Mz)(iw,nT.N,ij),i_=(e,t)=>{var r=ir(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},iP=(0,f.Mz)([iO,nT.N],i_),iE=(e,t)=>{var r=ir(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ik=(0,f.Mz)(iA,nT.N,iE),iT=(0,f.Mz)(iS,ik,iP,(e,t,r)=>iv(e,r,t)),iC=(0,f.Mz)([nG],ig),iN=(e,t,r,n,i,o,a)=>{var l=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[i,o]=e;if((0,nn.H)(i))r=i;else if("function"==typeof i)return;if((0,nn.H)(o))n=o;else if("function"==typeof o)return;var a=[r,n];if(ni(a))return a}}(t,e.allowDataOverflow);return null!=l?l:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(ni(n))return no(n,t,r)}catch(e){}if(Array.isArray(e)&&2===e.length){var i,o,[a,l]=e;if("auto"===a)null!=t&&(i=Math.min(...t));else if((0,nr.Et)(a))i=a;else if("function"==typeof a)try{null!=t&&(i=a(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof a&&ne.IH.test(a)){var u=ne.IH.exec(a);if(null==u||null==t)i=void 0;else{var s=+u[1];i=t[0]-s}}else i=null==t?void 0:t[0];if("auto"===l)null!=t&&(o=Math.max(...t));else if((0,nr.Et)(l))o=l;else if("function"==typeof l)try{null!=t&&(o=l(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof l&&ne.qx.test(l)){var c=ne.qx.exec(l);if(null==c||null==t)o=void 0;else{var f=+c[1];o=t[1]+f}}else o=null==t?void 0:t[1];var d=[i,o];if(ni(d))return null==t?d:no(d,t,r)}}}(t,"vertical"===o&&"xAxis"===a||"horizontal"===o&&"yAxis"===a?iv(r,i,iy(n)):iv(i,iy(n)),e.allowDataOverflow)},iD=(0,f.Mz)([nG,iC,iu,ih,iT,r9.fz,nT.N],iN),iz=[0,1],iI=(e,t,r,n,i,o,a)=>{if(null!=e&&null!=r&&0!==r.length||void 0!==a){var{dataKey:l,type:u}=e,s=(0,ne._L)(t,o);return s&&null==l?h()(0,r.length):"category"===u?((e,t,r)=>{var n=e.map(ip).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&(0,nr.CG)(n))?h()(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))})(n,e,s):"expand"===i?iz:a}},iR=(0,f.Mz)([nG,r9.fz,n7,ie,nE.eC,nT.N,iD],iI),iL=(e,t,r,n,i)=>{if(null!=e){var{scale:o,type:a}=e;if("auto"===o)return"radial"===t&&"radiusAxis"===i?"band":"radial"===t&&"angleAxis"===i?"linear":"category"===a&&n&&(n.indexOf("LineChart")>=0||n.indexOf("AreaChart")>=0||n.indexOf("ComposedChart")>=0&&!r)?"point":"category"===a?"band":"linear";if("string"==typeof o){var l="scale".concat((0,nr.Zb)(o));return l in c?l:"point"}}},iU=(0,f.Mz)([nG,r9.fz,nV,nE.iO,nT.N],iL);function iB(e,t,r,n){if(null!=r&&null!=n){if("function"==typeof e.scale)return e.scale.copy().domain(r).range(n);var i=function(e){if(null!=e){if(e in c)return c[e]();var t="scale".concat((0,nr.Zb)(e));if(t in c)return c[t]()}}(t);if(null!=i){var o=i.domain(r).range(n);return(0,ne.YB)(o),o}}}var i$=(e,t,r)=>{var n=ig(t);if("auto"===r||"linear"===r){if(null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&ni(e))return nM(e,t.tickCount,t.allowDecimals);if(null!=t&&t.tickCount&&"number"===t.type&&ni(e))return nA(e,t.tickCount,t.allowDecimals)}},iF=(0,f.Mz)([iR,nY,iU],i$),iH=(e,t,r,n)=>"angleAxis"!==n&&(null==e?void 0:e.type)==="number"&&ni(t)&&Array.isArray(r)&&r.length>0?[Math.min(t[0],r[0]),Math.max(t[1],r[r.length-1])]:t,iK=(0,f.Mz)([nG,iR,iF,nT.N],iH),iq=(0,f.Mz)(ie,nG,(e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(ir(e.map(e=>e.value))).sort((e,t)=>e-t);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var o=0;o<n.length-1;o++)r=Math.min(r,n[o+1]-n[o]);return r/i}}),iW=(0,f.Mz)(iq,r9.fz,nE.gY,n_.HZ,(e,t,r,n)=>n,(e,t,r,n,i)=>{if(!(0,nn.H)(e))return 0;var o="vertical"===t?n.height:n.width;if("gap"===i)return e*o/2;if("no-gap"===i){var a=(0,nr.F4)(r,e*o),l=e*o/2;return l-a-(l-a)/o*a}return 0}),iZ=(0,f.Mz)(nH,(e,t)=>{var r=nH(e,t);return null==r||"string"!=typeof r.padding?0:iW(e,"xAxis",t,r.padding)},(e,t)=>{if(null==e)return{left:0,right:0};var r,n,{padding:i}=e;return"string"==typeof i?{left:t,right:t}:{left:(null!=(r=i.left)?r:0)+t,right:(null!=(n=i.right)?n:0)+t}}),iG=(0,f.Mz)(nq,(e,t)=>{var r=nq(e,t);return null==r||"string"!=typeof r.padding?0:iW(e,"yAxis",t,r.padding)},(e,t)=>{if(null==e)return{top:0,bottom:0};var r,n,{padding:i}=e;return"string"==typeof i?{top:t,bottom:t}:{top:(null!=(r=i.top)?r:0)+t,bottom:(null!=(n=i.bottom)?n:0)+t}}),iY=(0,f.Mz)([n_.HZ,iZ,nP.U,nP.C,(e,t,r)=>r],(e,t,r,n,i)=>{var{padding:o}=n;return i?[o.left,r.width-o.right]:[e.left+t.left,e.left+e.width-t.right]}),iV=(0,f.Mz)([n_.HZ,r9.fz,iG,nP.U,nP.C,(e,t,r)=>r],(e,t,r,n,i,o)=>{var{padding:a}=i;return o?[n.height-a.bottom,a.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),iX=(e,t,r,n)=>{var i;switch(t){case"xAxis":return iY(e,r,n);case"yAxis":return iV(e,r,n);case"zAxis":return null==(i=nZ(e,r))?void 0:i.range;case"angleAxis":return(0,nk.Cv)(e);case"radiusAxis":return(0,nk.Dc)(e,r);default:return}},iJ=(0,f.Mz)([nG,iX],nN.I),iQ=(0,f.Mz)([nG,iU,iK,iJ],iB);function i0(e,t){return e.id<t.id?-1:+(e.id>t.id)}(0,f.Mz)([n1,ic,nT.N],id);var i1=(e,t)=>t,i2=(e,t,r)=>r,i3=(0,f.Mz)(nS.h,i1,i2,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(i0)),i5=(0,f.Mz)(nS.W,i1,i2,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(i0)),i4=(e,t)=>({width:e.width,height:t.height}),i8=(0,f.Mz)(n_.HZ,nH,i4),i6=(0,f.Mz)(nj.A$,n_.HZ,i3,i1,i2,(e,t,r,n,i)=>{var o,a={};return r.forEach(r=>{var l=i4(t,r);null==o&&(o=((e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}})(t,n,e));var u="top"===n&&!i||"bottom"===n&&i;a[r.id]=o-Number(u)*l.height,o+=(u?-1:1)*l.height}),a}),i7=(0,f.Mz)(nj.Lp,n_.HZ,i5,i1,i2,(e,t,r,n,i)=>{var o,a={};return r.forEach(r=>{var l=((e,t)=>({width:"number"==typeof t.width?t.width:nD.tQ,height:e.height}))(t,r);null==o&&(o=((e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}})(t,n,e));var u="left"===n&&!i||"right"===n&&i;a[r.id]=o-Number(u)*l.width,o+=(u?-1:1)*l.width}),a}),i9=(e,t)=>{var r=(0,n_.HZ)(e),n=nH(e,t);if(null!=n){var i=i6(e,n.orientation,n.mirror)[t];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}},oe=(e,t)=>{var r=(0,n_.HZ)(e),n=nq(e,t);if(null!=n){var i=i7(e,n.orientation,n.mirror)[t];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}},ot=(0,f.Mz)(n_.HZ,nq,(e,t)=>({width:"number"==typeof t.width?t.width:nD.tQ,height:e.height})),or=(e,t,r)=>{switch(t){case"xAxis":return i8(e,r).width;case"yAxis":return ot(e,r).height;default:return}},on=(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:o,dataKey:a}=r,l=(0,ne._L)(e,n),u=t.map(e=>e.value);if(a&&l&&"category"===o&&i&&(0,nr.CG)(u))return u}},oi=(0,f.Mz)([r9.fz,ie,nG,nT.N],on),oo=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:o}=r;if((0,ne._L)(e,n)&&("number"===i||"auto"!==o))return t.map(e=>e.value)}},oa=(0,f.Mz)([r9.fz,ie,nY,nT.N],oo),ol=(0,f.Mz)([r9.fz,(e,t,r)=>{switch(t){case"xAxis":return nH(e,r);case"yAxis":return nq(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},iU,iQ,oi,oa,iX,iF,nT.N],(e,t,r,n,i,o,a,l,u)=>{if(null==t)return null;var s=(0,ne._L)(e,u);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:u,categoricalDomain:o,duplicateDomain:i,isCategorical:s,niceTicks:l,range:a,realScaleType:r,scale:n}}),ou=(0,f.Mz)([r9.fz,nY,iU,iQ,iF,iX,oi,oa,nT.N],(e,t,r,n,i,o,a,l,u)=>{if(null!=t&&null!=n){var s=(0,ne._L)(e,u),{type:c,ticks:f,tickCount:d}=t,h="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,p="category"===c&&n.bandwidth?n.bandwidth()/h:0;p="angleAxis"===u&&null!=o&&o.length>=2?2*(0,nr.sA)(o[0]-o[1])*p:p;var y=f||i;return y?y.map((e,t)=>({index:t,coordinate:n(a?a.indexOf(e):e)+p,value:e,offset:p})).filter(e=>!(0,nr.M8)(e.coordinate)):s&&l?l.map((e,t)=>({coordinate:n(e)+p,value:e,index:t,offset:p})):n.ticks?n.ticks(d).map(e=>({coordinate:n(e)+p,value:e,offset:p})):n.domain().map((e,t)=>({coordinate:n(e)+p,value:a?a[e]:e,index:t,offset:p}))}}),os=(0,f.Mz)([r9.fz,nY,iQ,iX,oi,oa,nT.N],(e,t,r,n,i,o,a)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var l=(0,ne._L)(e,a),{tickCount:u}=t,s=0;return(s="angleAxis"===a&&(null==n?void 0:n.length)>=2?2*(0,nr.sA)(n[0]-n[1])*s:s,l&&o)?o.map((e,t)=>({coordinate:r(e)+s,value:e,index:t,offset:s})):r.ticks?r.ticks(u).map(e=>({coordinate:r(e)+s,value:e,offset:s})):r.domain().map((e,t)=>({coordinate:r(e)+s,value:i?i[e]:e,index:t,offset:s}))}}),oc=(0,f.Mz)(nG,iQ,(e,t)=>{if(null!=e&&null!=t)return nB(nB({},e),{},{scale:t})}),of=(0,f.Mz)([nG,iU,iR,iJ],iB);(0,f.Mz)((e,t,r)=>nZ(e,r),of,(e,t)=>{if(null!=e&&null!=t)return nB(nB({},e),{},{scale:t})});var od=(0,f.Mz)([r9.fz,nS.h,nS.W],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}})},2821:(e,t,r)=>{"use strict";function n(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=function e(t){var r,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var o=t.length;for(r=0;r<o;r++)t[r]&&(n=e(t[r]))&&(i&&(i+=" "),i+=n)}else for(n in t)t[n]&&(i&&(i+=" "),i+=n);return i}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{$:()=>n})},2851:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(73255),i=r(35163),o=r(96288);t.sortBy=function(e,...t){let r=t.length;return r>1&&o.isIterateeCall(e,t[0],t[1])?t=[]:r>2&&o.isIterateeCall(t[0],t[1],t[2])&&(t=[t[0]]),n.orderBy(e,i.flatten(t),["asc"])}},3838:(e,t,r)=>{"use strict";r.d(t,{p:()=>a,v:()=>l});var n=r(12115),i=r(81024),o=r(14599);function a(e){var t=(0,i.j)(),r=(0,n.useRef)(null);return(0,n.useEffect)(()=>{null===r.current?t((0,o.g5)(e)):r.current!==e&&t((0,o.ZF)({prev:r.current,next:e})),r.current=e},[t,e]),(0,n.useEffect)(()=>()=>{r.current&&(t((0,o.Vi)(r.current)),r.current=null)},[t]),null}function l(e){var t=(0,i.j)();return(0,n.useEffect)(()=>(t((0,o.As)(e)),()=>{t((0,o.TK)(e))}),[t,e]),null}},4035:(e,t,r)=>{"use strict";r.d(t,{F:()=>ew,L:()=>ep});var n=r(12115),i=r(54241),o=r.n(i),a=r(2821),l=r(76069),u=r(90135),s=r(8291),c=r(51023),f=r(1444),d=r(90167),h=r(72259),p=r(13802),y=r(15195),g=e=>e.graphicalItems.polarItems,v=(0,l.Mz)([h.N,p.E],f.eo),m=(0,l.Mz)([g,f.DP,v],f.ec),b=(0,l.Mz)([m],f.rj),w=(0,l.Mz)([b,u.z3],f.Nk),x=(0,l.Mz)([w,f.DP,m],f.fb),O=(0,l.Mz)([w,f.DP,m],(e,t,r)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var n;return{value:(0,c.kr)(e,null!=(n=t.dataKey)?n:r.dataKey),errorDomain:[]}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,c.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]}))),M=()=>void 0,A=(0,l.Mz)([f.DP,f.AV,M,O,M,d.fz,h.N],f.wL),j=(0,l.Mz)([f.DP,d.fz,w,x,y.eC,h.N,A],f.tP),S=(0,l.Mz)([j,f.DP,f.xM],f.xp);function _(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function P(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}(0,l.Mz)([f.DP,j,S,h.N],f.g1);var E=(0,l.Mz)([g,(e,t)=>t],(e,t)=>e.filter(e=>"pie"===e.type).find(e=>e.id===t)),k=[],T=(e,t,r)=>(null==r?void 0:r.length)===0?k:r,C=(0,l.Mz)([u.z3,E,T],(e,t,r)=>{var n,{chartData:i}=e;if(null!=t&&((n=(null==t?void 0:t.data)!=null&&t.data.length>0?t.data:i)&&n.length||null==r||(n=r.map(e=>P(P({},t.presentationProps),e.props))),null!=n))return n}),N=(0,l.Mz)([C,E,T],(e,t,r)=>{if(null!=e&&null!=t)return e.map((e,n)=>{var i,o,a=(0,c.kr)(e,t.nameKey,t.name);return o=null!=r&&null!=(i=r[n])&&null!=(i=i.props)&&i.fill?r[n].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:(0,c.uM)(a,t.dataKey),color:o,payload:e,type:t.legendType}})}),D=(0,l.Mz)([C,E,T,s.HZ],(e,t,r,n)=>{if(null!=t&&null!=e)return ep({offset:n,pieSettings:t,displayedData:e,cells:r})}),z=r(81024),I=r(87095),R=r(7050),L=r(39346),U=r(69386),B=r(70543),$=r(33692),F=r(34010),H=r(49580),K=r(84072),q=r(49887),W=r(92056),Z=r(87176),G=r(72481),Y=r(68997),V=r(34264),X=r(94913),J=r(85224),Q=r(48971),ee=r(3838),et=r(4264),er=r(34140),en=["onMouseEnter","onClick","onMouseLeave"],ei=["id"],eo=["id"];function ea(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function el(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eu(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?el(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):el(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function es(){return(es=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function ec(e){var t=(0,n.useMemo)(()=>(0,B.aS)(e.children,U.f),[e.children]),r=(0,z.G)(r=>N(r,e.id,t));return null==r?null:n.createElement(Y._,{legendPayload:r})}function ef(e){var{dataKey:t,nameKey:r,sectors:n,stroke:i,strokeWidth:o,fill:a,name:l,hide:u,tooltipType:s}=e;return{dataDefinedOnItem:null==n?void 0:n.map(e=>e.tooltipPayload),positions:null==n?void 0:n.map(e=>e.tooltipPosition),settings:{stroke:i,strokeWidth:o,fill:a,dataKey:t,nameKey:r,name:(0,c.uM)(l,t),hide:u,type:s,color:a,unit:""}}}function ed(e){var{sectors:t,props:r,showLabels:i}=e,{label:o,labelLine:l,dataKey:u}=r;if(!i||!o||!t)return null;var s=(0,et.u)(r),f=(0,B.J9)(o,!1),d=(0,B.J9)(l,!1),h="object"==typeof o&&"offsetRadius"in o&&o.offsetRadius||20,p=t.map((e,t)=>{var r,i,p=(e.startAngle+e.endAngle)/2,y=(0,F.IZ)(e.cx,e.cy,e.outerRadius+h,p),g=eu(eu(eu(eu({},s),e),{},{stroke:"none"},f),{},{index:t,textAnchor:(r=y.x)>(i=e.cx)?"start":r<i?"end":"middle"},y),v=eu(eu(eu(eu({},s),e),{},{fill:"none",stroke:e.fill},d),{},{index:t,points:[(0,F.IZ)(e.cx,e.cy,e.outerRadius,p),y],key:"line"});return n.createElement(I.W,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},l&&((e,t)=>{if(n.isValidElement(e))return n.cloneElement(e,t);if("function"==typeof e)return e(t);var r=(0,a.$)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return n.createElement(R.I,es({},t,{type:"linear",className:r}))})(l,v),((e,t,r)=>{if(n.isValidElement(e))return n.cloneElement(e,t);var i=r;if("function"==typeof e&&(i=e(t),n.isValidElement(i)))return i;var o=(0,a.$)("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return n.createElement(L.E,es({},t,{alignmentBaseline:"middle",className:o}),i)})(o,g,(0,c.kr)(e,u)))});return n.createElement(I.W,{className:"recharts-pie-labels"},p)}function eh(e){var{sectors:t,activeShape:r,inactiveShape:i,allOtherPieProps:o,showLabels:a}=e,l=(0,z.G)(G.A2),{onMouseEnter:u,onClick:s,onMouseLeave:c}=o,f=ea(o,en),d=(0,W.Cj)(u,o.dataKey),h=(0,W.Pg)(c),p=(0,W.Ub)(s,o.dataKey);return null==t?null:n.createElement(n.Fragment,null,t.map((e,a)=>{if((null==e?void 0:e.startAngle)===0&&(null==e?void 0:e.endAngle)===0&&1!==t.length)return null;var u=r&&String(a)===l,s=u?r:l?i:null,c=eu(eu({},e),{},{stroke:e.stroke,tabIndex:-1,[V.F0]:a,[V.um]:o.dataKey});return n.createElement(I.W,es({tabIndex:-1,className:"recharts-pie-sector"},(0,K.XC)(f,e,a),{onMouseEnter:d(e,a),onMouseLeave:h(e,a),onClick:p(e,a),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(a)}),n.createElement(q.y,es({option:s,isActive:u,shapeType:"sector"},c)))}),n.createElement(ed,{sectors:t,props:o,showLabels:a}))}function ep(e){var t,r,n,{pieSettings:i,displayedData:o,cells:a,offset:l}=e,{cornerRadius:u,startAngle:s,endAngle:f,dataKey:d,nameKey:h,tooltipType:p}=i,y=Math.abs(i.minAngle),g=(0,H.sA)(f-s)*Math.min(Math.abs(f-s),360),v=Math.abs(g),m=o.length<=1?0:null!=(t=i.paddingAngle)?t:0,b=o.filter(e=>0!==(0,c.kr)(e,d,0)).length,w=v-b*y-(v>=360?b:b-1)*m,x=o.reduce((e,t)=>{var r=(0,c.kr)(t,d,0);return e+((0,H.Et)(r)?r:0)},0);return x>0&&(r=o.map((e,t)=>{var r,o=(0,c.kr)(e,d,0),f=(0,c.kr)(e,h,t),v=((e,t,r)=>{let n,i,o;var{top:a,left:l,width:u,height:s}=t,c=(0,F.lY)(u,s),f=l+(0,H.F4)(e.cx,u,u/2),d=a+(0,H.F4)(e.cy,s,s/2),h=(0,H.F4)(e.innerRadius,c,0);return{cx:f,cy:d,innerRadius:h,outerRadius:(n=r,i=e.outerRadius,o=c,"function"==typeof i?i(n):(0,H.F4)(i,o,.8*o)),maxRadius:e.maxRadius||Math.sqrt(u*u+s*s)/2}})(i,l,e),b=((0,H.Et)(o)?o:0)/x,O=eu(eu({},e),a&&a[t]&&a[t].props),M=(r=t?n.endAngle+(0,H.sA)(g)*m*(0!==o):s)+(0,H.sA)(g)*((0!==o?y:0)+b*w),A=(r+M)/2,j=(v.innerRadius+v.outerRadius)/2,S=[{name:f,value:o,payload:O,dataKey:d,type:p}],_=(0,F.IZ)(v.cx,v.cy,j,A);return n=eu(eu(eu(eu({},i.presentationProps),{},{percent:b,cornerRadius:u,name:f,tooltipPayload:S,midAngle:A,middleRadius:j,tooltipPosition:_},O),v),{},{value:(0,c.kr)(e,d),startAngle:r,endAngle:M,payload:O,paddingAngle:(0,H.sA)(g)*m})})),r}function ey(e){var{props:t,previousSectorsRef:r}=e,{sectors:i,isAnimationActive:a,animationBegin:l,animationDuration:u,animationEasing:s,activeShape:c,inactiveShape:f,onAnimationStart:d,onAnimationEnd:h}=t,p=(0,X.n)(t,"recharts-pie-"),y=r.current,[g,v]=(0,n.useState)(!0),m=(0,n.useCallback)(()=>{"function"==typeof h&&h(),v(!1)},[h]),b=(0,n.useCallback)(()=>{"function"==typeof d&&d(),v(!0)},[d]);return n.createElement(er.J,{begin:l,duration:u,isActive:a,easing:s,onAnimationStart:b,onAnimationEnd:m,key:p},e=>{var a=[],l=(i&&i[0]).startAngle;return i.forEach((t,r)=>{var n=y&&y[r],i=r>0?o()(t,"paddingAngle",0):0;if(n){var u=(0,H.Dj)(n.endAngle-n.startAngle,t.endAngle-t.startAngle),s=eu(eu({},t),{},{startAngle:l+i,endAngle:l+u(e)+i});a.push(s),l=s.endAngle}else{var{endAngle:c,startAngle:f}=t,d=(0,H.Dj)(0,c-f)(e),h=eu(eu({},t),{},{startAngle:l+i,endAngle:l+d+i});a.push(h),l=h.endAngle}}),r.current=a,n.createElement(I.W,null,n.createElement(eh,{sectors:a,activeShape:c,inactiveShape:f,allOtherPieProps:t,showLabels:!g}))})}function eg(e){var{sectors:t,isAnimationActive:r,activeShape:i,inactiveShape:o}=e,a=(0,n.useRef)(null),l=a.current;return r&&t&&t.length&&(!l||l!==t)?n.createElement(ey,{props:e,previousSectorsRef:a}):n.createElement(eh,{sectors:t,activeShape:i,inactiveShape:o,allOtherPieProps:e,showLabels:!0})}function ev(e){var{hide:t,className:r,rootTabIndex:i}=e,o=(0,a.$)("recharts-pie",r);return t?null:n.createElement(I.W,{tabIndex:i,className:o},n.createElement(eg,e))}var em={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!$.m.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function eb(e){var{id:t}=e,r=ea(e,ei),i=(0,n.useMemo)(()=>(0,B.aS)(e.children,U.f),[e.children]),o=(0,z.G)(e=>D(e,t,i));return n.createElement(n.Fragment,null,n.createElement(Z.r,{fn:ef,args:eu(eu({},e),{},{sectors:o})}),n.createElement(ev,es({},r,{sectors:o})))}function ew(e){var t=(0,J.e)(e,em),{id:r}=t,i=ea(t,eo),o=(0,et.u)(i);return n.createElement(Q.x,{id:r,type:"pie"},e=>n.createElement(n.Fragment,null,n.createElement(ee.v,{type:"pie",id:e,data:i.data,dataKey:i.dataKey,hide:i.hide,angleAxisId:0,radiusAxisId:0,name:i.name,nameKey:i.nameKey,tooltipType:i.tooltipType,legendType:i.legendType,fill:i.fill,cx:i.cx,cy:i.cy,startAngle:i.startAngle,endAngle:i.endAngle,paddingAngle:i.paddingAngle,minAngle:i.minAngle,innerRadius:i.innerRadius,outerRadius:i.outerRadius,cornerRadius:i.cornerRadius,presentationProps:o}),n.createElement(ec,es({},i,{id:e})),n.createElement(eb,es({},i,{id:e})),i.children))}ew.displayName="Pie"},4264:(e,t,r)=>{"use strict";r.d(t,{R:()=>i,u:()=>o});var n=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"];function i(e){return"string"==typeof e&&n.includes(e)}function o(e){return Object.fromEntries(Object.entries(e).filter(e=>{var[t]=e;return i(t)}))}},4602:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.noop=function(){}},7050:(e,t,r)=>{"use strict";r.d(t,{I:()=>$});var n=r(12115);function i(){}function o(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function a(e){this._context=e}function l(e){this._context=e}function u(e){this._context=e}a.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:o(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:o(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},l.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:o(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},u.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:o(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class s{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function c(e){this._context=e}function f(e){this._context=e}function d(e){return new f(e)}c.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e*=1,t*=1,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function h(e,t,r){var n=e._x1-e._x0,i=t-e._x1,o=(e._y1-e._y0)/(n||i<0&&-0),a=(r-e._y1)/(i||n<0&&-0);return((o<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(o),Math.abs(a),.5*Math.abs((o*i+a*n)/(n+i)))||0}function p(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function y(e,t,r){var n=e._x0,i=e._y0,o=e._x1,a=e._y1,l=(o-n)/3;e._context.bezierCurveTo(n+l,i+l*t,o-l,a-l*r,o,a)}function g(e){this._context=e}function v(e){this._context=new m(e)}function m(e){this._context=e}function b(e){this._context=e}function w(e){var t,r,n=e.length-1,i=Array(n),o=Array(n),a=Array(n);for(i[0]=0,o[0]=2,a[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,o[t]=4,a[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,o[n-1]=7,a[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/o[t-1],o[t]-=r,a[t]-=r*a[t-1];for(i[n-1]=a[n-1]/o[n-1],t=n-2;t>=0;--t)i[t]=(a[t]-i[t+1])/o[t];for(t=0,o[n-1]=(e[n]+i[n-1])/2;t<n-1;++t)o[t]=2*e[t+1]-i[t+1];return[i,o]}function x(e,t){this._context=e,this._t=t}f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},g.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:y(this,this._t0,p(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t*=1,(e*=1)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,y(this,p(this,r=h(this,e,t)),r);break;default:y(this,this._t0,r=h(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(v.prototype=Object.create(g.prototype)).point=function(e,t){g.prototype.point.call(this,t,e)},m.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,o){this._context.bezierCurveTo(t,e,n,r,o,i)}},b.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=w(e),i=w(t),o=0,a=1;a<r;++o,++a)this._context.bezierCurveTo(n[0][o],i[0][o],n[1][o],i[1][o],e[a],t[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},x.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var O=r(27012),M=r(73595),A=r(89569);function j(e){return e[0]}function S(e){return e[1]}function _(e,t){var r=(0,M.A)(!0),n=null,i=d,o=null,a=(0,A.i)(l);function l(l){var u,s,c,f=(l=(0,O.A)(l)).length,d=!1;for(null==n&&(o=i(c=a())),u=0;u<=f;++u)!(u<f&&r(s=l[u],u,l))===d&&((d=!d)?o.lineStart():o.lineEnd()),d&&o.point(+e(s,u,l),+t(s,u,l));if(c)return o=null,c+""||null}return e="function"==typeof e?e:void 0===e?j:(0,M.A)(e),t="function"==typeof t?t:void 0===t?S:(0,M.A)(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,M.A)(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,M.A)(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:(0,M.A)(!!e),l):r},l.curve=function(e){return arguments.length?(i=e,null!=n&&(o=i(n)),l):i},l.context=function(e){return arguments.length?(null==e?n=o=null:o=i(n=e),l):n},l}function P(e,t,r){var n=null,i=(0,M.A)(!0),o=null,a=d,l=null,u=(0,A.i)(s);function s(s){var c,f,d,h,p,y=(s=(0,O.A)(s)).length,g=!1,v=Array(y),m=Array(y);for(null==o&&(l=a(p=u())),c=0;c<=y;++c){if(!(c<y&&i(h=s[c],c,s))===g)if(g=!g)f=c,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),d=c-1;d>=f;--d)l.point(v[d],m[d]);l.lineEnd(),l.areaEnd()}g&&(v[c]=+e(h,c,s),m[c]=+t(h,c,s),l.point(n?+n(h,c,s):v[c],r?+r(h,c,s):m[c]))}if(p)return l=null,p+""||null}function c(){return _().defined(i).curve(a).context(o)}return e="function"==typeof e?e:void 0===e?j:(0,M.A)(+e),t="function"==typeof t?t:void 0===t?(0,M.A)(0):(0,M.A)(+t),r="function"==typeof r?r:void 0===r?S:(0,M.A)(+r),s.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,M.A)(+t),n=null,s):e},s.x0=function(t){return arguments.length?(e="function"==typeof t?t:(0,M.A)(+t),s):e},s.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:(0,M.A)(+e),s):n},s.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,M.A)(+e),r=null,s):t},s.y0=function(e){return arguments.length?(t="function"==typeof e?e:(0,M.A)(+e),s):t},s.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:(0,M.A)(+e),s):r},s.lineX0=s.lineY0=function(){return c().x(e).y(t)},s.lineY1=function(){return c().x(e).y(r)},s.lineX1=function(){return c().x(n).y(t)},s.defined=function(e){return arguments.length?(i="function"==typeof e?e:(0,M.A)(!!e),s):i},s.curve=function(e){return arguments.length?(a=e,null!=o&&(l=a(o)),s):a},s.context=function(e){return arguments.length?(null==e?o=l=null:l=a(o=e),s):o},s}var E=r(2821),k=r(84072),T=r(49580),C=r(92377),N=r(4264);function D(){return(D=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function z(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function I(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?z(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):z(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var R={curveBasisClosed:function(e){return new l(e)},curveBasisOpen:function(e){return new u(e)},curveBasis:function(e){return new a(e)},curveBumpX:function(e){return new s(e,!0)},curveBumpY:function(e){return new s(e,!1)},curveLinearClosed:function(e){return new c(e)},curveLinear:d,curveMonotoneX:function(e){return new g(e)},curveMonotoneY:function(e){return new v(e)},curveNatural:function(e){return new b(e)},curveStep:function(e){return new x(e,.5)},curveStepAfter:function(e){return new x(e,1)},curveStepBefore:function(e){return new x(e,0)}},L=e=>(0,C.H)(e.x)&&(0,C.H)(e.y),U=e=>e.x,B=e=>e.y,$=e=>{var{className:t,points:r,path:i,pathRef:o}=e;if((!r||!r.length)&&!i)return null;var a=r&&r.length?(e=>{var t,{type:r="linear",points:n=[],baseLine:i,layout:o,connectNulls:a=!1}=e,l=((e,t)=>{if("function"==typeof e)return e;var r="curve".concat((0,T.Zb)(e));return("curveMonotone"===r||"curveBump"===r)&&t?R["".concat(r).concat("vertical"===t?"Y":"X")]:R[r]||d})(r,o),u=a?n.filter(L):n;if(Array.isArray(i)){var s=a?i.filter(e=>L(e)):i,c=u.map((e,t)=>I(I({},e),{},{base:s[t]}));return(t="vertical"===o?P().y(B).x1(U).x0(e=>e.base.x):P().x(U).y1(B).y0(e=>e.base.y)).defined(L).curve(l),t(c)}return(t="vertical"===o&&(0,T.Et)(i)?P().y(B).x1(U).x0(i):(0,T.Et)(i)?P().x(U).y1(B).y0(i):_().x(U).y(B)).defined(L).curve(l),t(u)})(e):i;return n.createElement("path",D({},(0,N.u)(e),(0,k._U)(e),{className:(0,E.$)("recharts-curve",t),d:null===a?void 0:a,ref:o}))}},8291:(e,t,r)=>{"use strict";r.d(t,{Ds:()=>p,HZ:()=>h,c2:()=>y});var n=r(76069),i=r(54241),o=r.n(i),a=r(91640),l=r(51023),u=r(27588),s=r(49507),c=r(34264);function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var h=(0,n.Mz)([u.Lp,u.A$,u.HK,e=>e.brush.height,s.h,s.W,a.ff,a.dc],(e,t,r,n,i,a,u,s)=>{var f=a.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:c.tQ;return d(d({},e),{},{[r]:e[r]+n})}return e},{left:r.left||0,right:r.right||0}),h=i.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:d(d({},e),{},{[r]:o()(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),p=d(d({},h),f),y=p.bottom;p.bottom+=n;var g=e-(p=(0,l.s0)(p,u,s)).left-p.right,v=t-p.top-p.bottom;return d(d({brushBottom:y},p),{},{width:Math.max(g,0),height:Math.max(v,0)})}),p=(0,n.Mz)(h,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),y=(0,n.Mz)(u.Lp,u.A$,(e,t)=>({x:0,y:0,width:e,height:t}))},8828:(e,t,r)=>{"use strict";e.exports=r(83654)},9220:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.argumentsTag="[object Arguments]",t.arrayBufferTag="[object ArrayBuffer]",t.arrayTag="[object Array]",t.bigInt64ArrayTag="[object BigInt64Array]",t.bigUint64ArrayTag="[object BigUint64Array]",t.booleanTag="[object Boolean]",t.dataViewTag="[object DataView]",t.dateTag="[object Date]",t.errorTag="[object Error]",t.float32ArrayTag="[object Float32Array]",t.float64ArrayTag="[object Float64Array]",t.functionTag="[object Function]",t.int16ArrayTag="[object Int16Array]",t.int32ArrayTag="[object Int32Array]",t.int8ArrayTag="[object Int8Array]",t.mapTag="[object Map]",t.numberTag="[object Number]",t.objectTag="[object Object]",t.regexpTag="[object RegExp]",t.setTag="[object Set]",t.stringTag="[object String]",t.symbolTag="[object Symbol]",t.uint16ArrayTag="[object Uint16Array]",t.uint32ArrayTag="[object Uint32Array]",t.uint8ArrayTag="[object Uint8Array]",t.uint8ClampedArrayTag="[object Uint8ClampedArray]"},11345:(e,t,r)=>{"use strict";r.d(t,{s:()=>T});var n=r(12115),i=r(47650),o=r(64940),a=r(2821),l=r(69905),u=r(92143),s=r(84072);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class h extends n.PureComponent{renderIcon(e,t){var{inactiveColor:r}=this.props,i=32/6,o=32/3,a=e.inactive?r:e.color,l=null!=t?t:e.type;if("none"===l)return null;if("plainline"===l)return n.createElement("line",{strokeWidth:4,fill:"none",stroke:a,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===l)return n.createElement("path",{strokeWidth:4,fill:"none",stroke:a,d:"M0,".concat(16,"h").concat(o,"\n            A").concat(i,",").concat(i,",0,1,1,").concat(2*o,",").concat(16,"\n            H").concat(32,"M").concat(2*o,",").concat(16,"\n            A").concat(i,",").concat(i,",0,1,1,").concat(o,",").concat(16),className:"recharts-legend-icon"});if("rect"===l)return n.createElement("path",{stroke:"none",fill:a,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(n.isValidElement(e.legendIcon)){var s=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return delete s.legendIcon,n.cloneElement(e.legendIcon,s)}return n.createElement(u.i,{fill:a,cx:16,cy:16,size:32,sizeType:"diameter",type:l})}renderItems(){var{payload:e,iconSize:t,layout:r,formatter:i,inactiveColor:o,iconType:u}=this.props,f={x:0,y:0,width:32,height:32},d={display:"horizontal"===r?"inline-block":"block",marginRight:10},h={display:"inline-block",verticalAlign:"middle",marginRight:4};return e.map((e,r)=>{var p=e.formatter||i,y=(0,a.$)({"recharts-legend-item":!0,["legend-item-".concat(r)]:!0,inactive:e.inactive});if("none"===e.type)return null;var g=e.inactive?o:e.color,v=p?p(e.value,e,r):e.value;return n.createElement("li",c({className:y,style:d,key:"legend-item-".concat(r)},(0,s.XC)(this.props,e,r)),n.createElement(l.u,{width:t,height:t,viewBox:f,style:h,"aria-label":"".concat(v," legend icon")},this.renderIcon(e,u)),n.createElement("span",{className:"recharts-legend-item-text",style:{color:g}},v))})}render(){var{payload:e,layout:t,align:r}=this.props;return e&&e.length?n.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===t?r:"left"}},this.renderItems()):null}}d(h,"displayName","Legend"),d(h,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",layout:"horizontal",verticalAlign:"middle"});var p=r(49580),y=r(89139),g=r.n(y),v=r(81024),m=r(91640),b=r(90167),w=r(69277),x=["contextPayload"];function O(){return(O=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function M(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function A(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?M(Object(r),!0).forEach(function(t){j(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function j(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function S(e){return e.value}function _(e){var t,{contextPayload:r}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,x),o=(t=e.payloadUniqBy,!0===t?g()(r,S):"function"==typeof t?g()(r,t):r),a=A(A({},i),{},{payload:o});return n.isValidElement(e.content)?n.cloneElement(e.content,a):"function"==typeof e.content?n.createElement(e.content,a):n.createElement(h,a)}function P(e){var t=(0,v.j)();return(0,n.useEffect)(()=>{t((0,w.h1)(e))},[t,e]),null}function E(e){var t=(0,v.j)();return(0,n.useEffect)(()=>(t((0,w.hx)(e)),()=>{t((0,w.hx)({width:0,height:0}))}),[t,e]),null}function k(e){var t=(0,v.G)(m.g0),r=(0,o.M)(),a=(0,b.Kp)(),{width:l,height:u,wrapperStyle:s,portal:c}=e,[f,d]=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=(0,n.useState)({height:0,left:0,top:0,width:0}),i=(0,n.useCallback)(e=>{if(null!=e){var n=e.getBoundingClientRect(),i={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(i.height-t.height)>1||Math.abs(i.left-t.left)>1||Math.abs(i.top-t.top)>1||Math.abs(i.width-t.width)>1)&&r({height:i.height,left:i.left,top:i.top,width:i.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,i]}([t]),h=(0,b.yi)(),p=(0,b.rY)(),y=h-(a.left||0)-(a.right||0),g=T.getWidthOrHeight(e.layout,u,l,y),w=c?s:A(A({position:"absolute",width:(null==g?void 0:g.width)||l||"auto",height:(null==g?void 0:g.height)||u||"auto"},function(e,t,r,n,i,o){var a,l,{layout:u,align:s,verticalAlign:c}=t;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(a="center"===s&&"vertical"===u?{left:((n||0)-o.width)/2}:"right"===s?{right:r&&r.right||0}:{left:r&&r.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(l="middle"===c?{top:((i||0)-o.height)/2}:"bottom"===c?{bottom:r&&r.bottom||0}:{top:r&&r.top||0}),A(A({},a),l)}(s,e,a,h,p,f)),s),x=null!=c?c:r;if(null==x)return null;var M=n.createElement("div",{className:"recharts-legend-wrapper",style:w,ref:d},n.createElement(P,{layout:e.layout,align:e.align,verticalAlign:e.verticalAlign,itemSorter:e.itemSorter}),n.createElement(E,{width:f.width,height:f.height}),n.createElement(_,O({},e,g,{margin:a,chartWidth:h,chartHeight:p,contextPayload:t})));return(0,i.createPortal)(M,x)}class T extends n.PureComponent{static getWidthOrHeight(e,t,r,n){return"vertical"===e&&(0,p.Et)(t)?{height:t}:"horizontal"===e?{width:r||n}:null}render(){return n.createElement(k,this.props)}}j(T,"displayName","Legend"),j(T,"defaultProps",{align:"center",iconSize:14,itemSorter:"value",layout:"horizontal",verticalAlign:"bottom"})},12474:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(13869);t.debounce=function(e,t=0,r={}){let i;"object"!=typeof r&&(r={});let{leading:o=!1,trailing:a=!0,maxWait:l}=r,u=[,,];o&&(u[0]="leading"),a&&(u[1]="trailing");let s=null,c=n.debounce(function(...t){i=e.apply(this,t),s=null},t,{edges:u}),f=function(...t){return null!=l&&(null===s&&(s=Date.now()),Date.now()-s>=l)?(i=e.apply(this,t),s=Date.now(),c.cancel(),c.schedule(),i):(c.apply(this,t),i)};return f.cancel=c.cancel,f.flush=()=>(c.flush(),i),f}},12723:(e,t,r)=>{"use strict";r.d(t,{r:()=>x});var n=r(12115),i=r(33308),o=r(81262),a=r(74797),l=r(38881),u=r(84020),s=r(81024),c=r(19052);function f(e){var t=(0,s.j)();return(0,n.useEffect)(()=>{t((0,c.U)(e))},[t,e]),null}var d=r(72743),h=r(85224),p=r(92377),y=["width","height","layout"];function g(){return(g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var v={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},m=(0,n.forwardRef)(function(e,t){var r,i=(0,h.e)(e.categoricalChartProps,v),{width:s,height:c,layout:m}=i,b=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,y);if(!(0,p.F)(s)||!(0,p.F)(c))return null;var{chartName:w,defaultTooltipEventType:x,validateTooltipEventTypes:O,tooltipPayloadSearcher:M}=e;return n.createElement(o.J,{preloadedState:{options:{chartName:w,defaultTooltipEventType:x,validateTooltipEventTypes:O,tooltipPayloadSearcher:M,eventEmitter:void 0}},reduxStoreName:null!=(r=i.id)?r:w},n.createElement(a.TK,{chartData:i.data}),n.createElement(l.s,{width:s,height:c,layout:m,margin:i.margin}),n.createElement(u.p,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),n.createElement(f,{cx:i.cx,cy:i.cy,startAngle:i.startAngle,endAngle:i.endAngle,innerRadius:i.innerRadius,outerRadius:i.outerRadius}),n.createElement(d.L,g({width:s,height:c},b,{ref:t})))}),b=["item"],w={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},x=(0,n.forwardRef)((e,t)=>{var r=(0,h.e)(e,w);return n.createElement(m,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:b,tooltipPayloadSearcher:i.uN,categoricalChartProps:r,ref:t})})},12974:(e,t,r)=>{"use strict";var n=r(15376).Buffer;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let i=r(16940),o=r(32333),a=r(9220),l=r(87744),u=r(73390);function s(e,t,r,i=new Map,f){let d=f?.(e,t,r,i);if(void 0!==d)return d;if(l.isPrimitive(e))return e;if(i.has(e))return i.get(e);if(Array.isArray(e)){let t=Array(e.length);i.set(e,t);for(let n=0;n<e.length;n++)t[n]=s(e[n],n,r,i,f);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){let t=new Map;for(let[n,o]of(i.set(e,t),e))t.set(n,s(o,n,r,i,f));return t}if(e instanceof Set){let t=new Set;for(let n of(i.set(e,t),e))t.add(s(n,void 0,r,i,f));return t}if(void 0!==n&&n.isBuffer(e))return e.subarray();if(u.isTypedArray(e)){let t=new(Object.getPrototypeOf(e)).constructor(e.length);i.set(e,t);for(let n=0;n<e.length;n++)t[n]=s(e[n],n,r,i,f);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return i.set(e,t),c(t,e,r,i,f),t}if("undefined"!=typeof File&&e instanceof File){let t=new File([e],e.name,{type:e.type});return i.set(e,t),c(t,e,r,i,f),t}if(e instanceof Blob){let t=new Blob([e],{type:e.type});return i.set(e,t),c(t,e,r,i,f),t}if(e instanceof Error){let t=new e.constructor;return i.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,c(t,e,r,i,f),t}if("object"==typeof e&&function(e){switch(o.getTag(e)){case a.argumentsTag:case a.arrayTag:case a.arrayBufferTag:case a.dataViewTag:case a.booleanTag:case a.dateTag:case a.float32ArrayTag:case a.float64ArrayTag:case a.int8ArrayTag:case a.int16ArrayTag:case a.int32ArrayTag:case a.mapTag:case a.numberTag:case a.objectTag:case a.regexpTag:case a.setTag:case a.stringTag:case a.symbolTag:case a.uint8ArrayTag:case a.uint8ClampedArrayTag:case a.uint16ArrayTag:case a.uint32ArrayTag:return!0;default:return!1}}(e)){let t=Object.create(Object.getPrototypeOf(e));return i.set(e,t),c(t,e,r,i,f),t}return e}function c(e,t,r=e,n,o){let a=[...Object.keys(t),...i.getSymbols(t)];for(let i=0;i<a.length;i++){let l=a[i],u=Object.getOwnPropertyDescriptor(e,l);(null==u||u.writable)&&(e[l]=s(t[l],l,r,n,o))}}t.cloneDeepWith=function(e,t){return s(e,void 0,e,new Map,t)},t.cloneDeepWithImpl=s,t.copyProperties=c},13183:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.uniqBy=function(e,t){let r=new Map;for(let n=0;n<e.length;n++){let i=e[n],o=t(i);r.has(o)||r.set(o,i)}return Array.from(r.values())}},13802:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var n=(e,t,r)=>r},13869:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.debounce=function(e,t,{signal:r,edges:n}={}){let i,o=null,a=null!=n&&n.includes("leading"),l=null==n||n.includes("trailing"),u=()=>{null!==o&&(e.apply(i,o),i=void 0,o=null)},s=null,c=()=>{null!=s&&clearTimeout(s),s=setTimeout(()=>{s=null,l&&u(),f()},t)},f=()=>{null!==s&&(clearTimeout(s),s=null),i=void 0,o=null},d=function(...e){if(r?.aborted)return;i=this,o=e;let t=null==s;c(),a&&t&&u()};return d.schedule=c,d.cancel=f,d.flush=()=>{u()},r?.addEventListener("abort",f,{once:!0}),d}},14105:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:n,blurHeight:i,blurDataURL:o,objectFit:a}=e,l=n?40*n:t,u=i?40*i:r,s=l&&u?"viewBox='0 0 "+l+" "+u+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+s+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(s?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},14599:(e,t,r)=>{"use strict";r.d(t,{As:()=>s,TK:()=>c,Vi:()=>u,ZF:()=>l,g5:()=>a,iZ:()=>f});var n=r(26286),i=r(60013),o=(0,n.Z0)({name:"graphicalItems",initialState:{cartesianItems:[],polarItems:[]},reducers:{addCartesianGraphicalItem(e,t){e.cartesianItems.push((0,i.h4)(t.payload))},replaceCartesianGraphicalItem(e,t){var{prev:r,next:n}=t.payload,o=(0,i.ss)(e).cartesianItems.indexOf((0,i.h4)(r));o>-1&&(e.cartesianItems[o]=(0,i.h4)(n))},removeCartesianGraphicalItem(e,t){var r=(0,i.ss)(e).cartesianItems.indexOf((0,i.h4)(t.payload));r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push((0,i.h4)(t.payload))},removePolarGraphicalItem(e,t){var r=(0,i.ss)(e).polarItems.indexOf((0,i.h4)(t.payload));r>-1&&e.polarItems.splice(r,1)}}}),{addCartesianGraphicalItem:a,replaceCartesianGraphicalItem:l,removeCartesianGraphicalItem:u,addPolarGraphicalItem:s,removePolarGraphicalItem:c}=o.actions,f=o.reducer},14806:(e,t,r)=>{"use strict";e.exports=r(30125)},14821:(e,t,r)=>{"use strict";r.d(t,{Be:()=>g,Cv:()=>O,D0:()=>A,Gl:()=>v,Dc:()=>M});var n=r(76069),i=r(27588),o=r(8291),a=r(34010),l=r(49580),u={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},s={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},c=r(15145),f=r(90167),d={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:u.angleAxisId,includeHidden:!1,name:void 0,reversed:u.reversed,scale:u.scale,tick:u.tick,tickCount:void 0,ticks:void 0,type:u.type,unit:void 0},h={allowDataOverflow:s.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:s.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:s.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:s.scale,tick:s.tick,tickCount:s.tickCount,ticks:void 0,type:s.type,unit:void 0},p={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:u.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:u.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:u.scale,tick:u.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},y={allowDataOverflow:s.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:s.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:s.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:s.scale,tick:s.tick,tickCount:s.tickCount,ticks:void 0,type:"category",unit:void 0},g=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?p:d,v=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?y:h,m=e=>e.polarOptions,b=(0,n.Mz)([i.Lp,i.A$,o.HZ],a.lY),w=(0,n.Mz)([m,b],(e,t)=>{if(null!=e)return(0,l.F4)(e.innerRadius,t,0)}),x=(0,n.Mz)([m,b],(e,t)=>{if(null!=e)return(0,l.F4)(e.outerRadius,t,.8*t)}),O=(0,n.Mz)([m],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]});(0,n.Mz)([g,O],c.I);var M=(0,n.Mz)([b,w,x],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]});(0,n.Mz)([v,M],c.I);var A=(0,n.Mz)([f.fz,m,w,x,i.Lp,i.A$],(e,t,r,n,i,o)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:a,cy:u,startAngle:s,endAngle:c}=t;return{cx:(0,l.F4)(a,i,i/2),cy:(0,l.F4)(u,o,o/2),innerRadius:r,outerRadius:n,startAngle:s,endAngle:c,clockWise:!1}}})},15145:(e,t,r)=>{"use strict";r.d(t,{I:()=>n});var n=(e,t)=>{if(e&&t)return null!=e&&e.reversed?[t[1],t[0]]:t}},15195:(e,t,r)=>{"use strict";r.d(t,{JN:()=>n,_5:()=>i,eC:()=>l,gY:()=>o,hX:()=>c,iO:()=>u,lZ:()=>s,pH:()=>f,x3:()=>a});var n=e=>e.rootProps.maxBarSize,i=e=>e.rootProps.barGap,o=e=>e.rootProps.barCategoryGap,a=e=>e.rootProps.barSize,l=e=>e.rootProps.stackOffset,u=e=>e.options.chartName,s=e=>e.rootProps.syncId,c=e=>e.rootProps.syncMethod,f=e=>e.options.eventEmitter},15239:(e,t,r)=>{"use strict";r.d(t,{default:()=>i.a});var n=r(54652),i=r.n(n)},15376:e=>{!function(){var t={675:function(e,t){"use strict";t.byteLength=function(e){var t=u(e),r=t[0],n=t[1];return(r+n)*3/4-n},t.toByteArray=function(e){var t,r,o=u(e),a=o[0],l=o[1],s=new i((a+l)*3/4-l),c=0,f=l>0?a-4:a;for(r=0;r<f;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],s[c++]=t>>16&255,s[c++]=t>>8&255,s[c++]=255&t;return 2===l&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,s[c++]=255&t),1===l&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,s[c++]=t>>8&255,s[c++]=255&t),s},t.fromByteArray=function(e){for(var t,n=e.length,i=n%3,o=[],a=0,l=n-i;a<l;a+=16383)o.push(function(e,t,n){for(var i,o=[],a=t;a<n;a+=3)i=(e[a]<<16&0xff0000)+(e[a+1]<<8&65280)+(255&e[a+2]),o.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return o.join("")}(e,a,a+16383>l?l:a+16383));return 1===i?o.push(r[(t=e[n-1])>>2]+r[t<<4&63]+"=="):2===i&&o.push(r[(t=(e[n-2]<<8)+e[n-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),o.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,l=o.length;a<l;++a)r[a]=o[a],n[o.charCodeAt(a)]=a;function u(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var n=r===t?0:4-r%4;return[r,n]}n[45]=62,n[95]=63},72:function(e,t,r){"use strict";var n=r(675),i=r(783),o="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function a(e){if(e>0x7fffffff)throw RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return Object.setPrototypeOf(t,l.prototype),t}function l(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return c(e)}return u(e,t,r)}function u(e,t,r){if("string"==typeof e){var n=e,i=t;if(("string"!=typeof i||""===i)&&(i="utf8"),!l.isEncoding(i))throw TypeError("Unknown encoding: "+i);var o=0|h(n,i),u=a(o),s=u.write(n,i);return s!==o&&(u=u.slice(0,s)),u}if(ArrayBuffer.isView(e))return f(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(E(e,ArrayBuffer)||e&&E(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(E(e,SharedArrayBuffer)||e&&E(e.buffer,SharedArrayBuffer)))return function(e,t,r){var n;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),l.prototype),n}(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var c=e.valueOf&&e.valueOf();if(null!=c&&c!==e)return l.from(c,t,r);var p=function(e){if(l.isBuffer(e)){var t=0|d(e.length),r=a(t);return 0===r.length||e.copy(r,0,0,t),r}return void 0!==e.length?"number"!=typeof e.length||function(e){return e!=e}(e.length)?a(0):f(e):"Buffer"===e.type&&Array.isArray(e.data)?f(e.data):void 0}(e);if(p)return p;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return l.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function s(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function c(e){return s(e),a(e<0?0:0|d(e))}function f(e){for(var t=e.length<0?0:0|d(e.length),r=a(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}t.Buffer=l,t.SlowBuffer=function(e){return+e!=e&&(e=0),l.alloc(+e)},t.INSPECT_MAX_BYTES=50,t.kMaxLength=0x7fffffff,l.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),l.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(l.prototype,"parent",{enumerable:!0,get:function(){if(l.isBuffer(this))return this.buffer}}),Object.defineProperty(l.prototype,"offset",{enumerable:!0,get:function(){if(l.isBuffer(this))return this.byteOffset}}),l.poolSize=8192,l.from=function(e,t,r){return u(e,t,r)},Object.setPrototypeOf(l.prototype,Uint8Array.prototype),Object.setPrototypeOf(l,Uint8Array),l.alloc=function(e,t,r){return(s(e),e<=0)?a(e):void 0!==t?"string"==typeof r?a(e).fill(t,r):a(e).fill(t):a(e)},l.allocUnsafe=function(e){return c(e)},l.allocUnsafeSlow=function(e){return c(e)};function d(e){if(e>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function h(e,t){if(l.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||E(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return j(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return _(e).length;default:if(i)return n?-1:j(e).length;t=(""+t).toLowerCase(),i=!0}}function p(e,t,r){var i,o,a,l=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=t;o<r;++o)i+=k[e[o]];return i}(this,t,r);case"utf8":case"utf-8":return m(this,t,r);case"ascii":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}(this,t,r);case"latin1":case"binary":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}(this,t,r);case"base64":return i=this,o=t,a=r,0===o&&a===i.length?n.fromByteArray(i):n.fromByteArray(i.slice(o,a));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var n=e.slice(t,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}(this,t,r);default:if(l)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),l=!0}}function y(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function g(e,t,r,n,i){var o;if(0===e.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(o=r*=1)!=o&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length)if(i)return -1;else r=e.length-1;else if(r<0)if(!i)return -1;else r=0;if("string"==typeof t&&(t=l.from(t,n)),l.isBuffer(t))return 0===t.length?-1:v(e,t,r,n,i);if("number"==typeof t){if(t&=255,"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(e,t,r);else return Uint8Array.prototype.lastIndexOf.call(e,t,r);return v(e,[t],r,n,i)}throw TypeError("val must be string, number or Buffer")}function v(e,t,r,n,i){var o,a=1,l=e.length,u=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return -1;a=2,l/=2,u/=2,r/=2}function s(e,t){return 1===a?e[t]:e.readUInt16BE(t*a)}if(i){var c=-1;for(o=r;o<l;o++)if(s(e,o)===s(t,-1===c?0:o-c)){if(-1===c&&(c=o),o-c+1===u)return c*a}else -1!==c&&(o-=o-c),c=-1}else for(r+u>l&&(r=l-u),o=r;o>=0;o--){for(var f=!0,d=0;d<u;d++)if(s(e,o+d)!==s(t,d)){f=!1;break}if(f)return o}return -1}l.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==l.prototype},l.compare=function(e,t){if(E(e,Uint8Array)&&(e=l.from(e,e.offset,e.byteLength)),E(t,Uint8Array)&&(t=l.from(t,t.offset,t.byteLength)),!l.isBuffer(e)||!l.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,n=t.length,i=0,o=Math.min(r,n);i<o;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:+(n<r)},l.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},l.concat=function(e,t){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return l.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,n=l.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var o=e[r];if(E(o,Uint8Array)&&(o=l.from(o)),!l.isBuffer(o))throw TypeError('"list" argument must be an Array of Buffers');o.copy(n,i),i+=o.length}return n},l.byteLength=h,l.prototype._isBuffer=!0,l.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)y(this,t,t+1);return this},l.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)y(this,t,t+3),y(this,t+1,t+2);return this},l.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)y(this,t,t+7),y(this,t+1,t+6),y(this,t+2,t+5),y(this,t+3,t+4);return this},l.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?m(this,0,e):p.apply(this,arguments)},l.prototype.toLocaleString=l.prototype.toString,l.prototype.equals=function(e){if(!l.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===l.compare(this,e)},l.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return e=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(e+=" ... "),"<Buffer "+e+">"},o&&(l.prototype[o]=l.prototype.inspect),l.prototype.compare=function(e,t,r,n,i){if(E(e,Uint8Array)&&(e=l.from(e,e.offset,e.byteLength)),!l.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,i>>>=0,this===e)return 0;for(var o=i-n,a=r-t,u=Math.min(o,a),s=this.slice(n,i),c=e.slice(t,r),f=0;f<u;++f)if(s[f]!==c[f]){o=s[f],a=c[f];break}return o<a?-1:+(a<o)},l.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},l.prototype.indexOf=function(e,t,r){return g(this,e,t,r,!0)},l.prototype.lastIndexOf=function(e,t,r){return g(this,e,t,r,!1)};function m(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var o,a,l,u,s=e[i],c=null,f=s>239?4:s>223?3:s>191?2:1;if(i+f<=r)switch(f){case 1:s<128&&(c=s);break;case 2:(192&(o=e[i+1]))==128&&(u=(31&s)<<6|63&o)>127&&(c=u);break;case 3:o=e[i+1],a=e[i+2],(192&o)==128&&(192&a)==128&&(u=(15&s)<<12|(63&o)<<6|63&a)>2047&&(u<55296||u>57343)&&(c=u);break;case 4:o=e[i+1],a=e[i+2],l=e[i+3],(192&o)==128&&(192&a)==128&&(192&l)==128&&(u=(15&s)<<18|(63&o)<<12|(63&a)<<6|63&l)>65535&&u<1114112&&(c=u)}null===c?(c=65533,f=1):c>65535&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),i+=f}var d=n,h=d.length;if(h<=4096)return String.fromCharCode.apply(String,d);for(var p="",y=0;y<h;)p+=String.fromCharCode.apply(String,d.slice(y,y+=4096));return p}function b(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function w(e,t,r,n,i,o){if(!l.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw RangeError('"value" argument is out of bounds');if(r+n>e.length)throw RangeError("Index out of range")}function x(e,t,r,n,i,o){if(r+n>e.length||r<0)throw RangeError("Index out of range")}function O(e,t,r,n,o){return t*=1,r>>>=0,o||x(e,t,r,4,34028234663852886e22,-34028234663852886e22),i.write(e,t,r,n,23,4),r+4}function M(e,t,r,n,o){return t*=1,r>>>=0,o||x(e,t,r,8,17976931348623157e292,-17976931348623157e292),i.write(e,t,r,n,52,8),r+8}l.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,o,a,l,u,s,c,f,d=this.length-t;if((void 0===r||r>d)&&(r=d),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var h=!1;;)switch(n){case"hex":return function(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=t.length;n>o/2&&(n=o/2);for(var a=0;a<n;++a){var l,u=parseInt(t.substr(2*a,2),16);if((l=u)!=l)break;e[r+a]=u}return a}(this,e,t,r);case"utf8":case"utf-8":return i=t,o=r,P(j(e,this.length-i),this,i,o);case"ascii":return a=t,l=r,P(S(e),this,a,l);case"latin1":case"binary":return function(e,t,r,n){return P(S(t),e,r,n)}(this,e,t,r);case"base64":return u=t,s=r,P(_(e),this,u,s);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return c=t,f=r,P(function(e,t){for(var r,n,i=[],o=0;o<e.length&&!((t-=2)<0);++o)n=(r=e.charCodeAt(o))>>8,i.push(r%256),i.push(n);return i}(e,this.length-c),this,c,f);default:if(h)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),h=!0}},l.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},l.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var n=this.subarray(e,t);return Object.setPrototypeOf(n,l.prototype),n},l.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n},l.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},l.prototype.readUInt8=function(e,t){return e>>>=0,t||b(e,1,this.length),this[e]},l.prototype.readUInt16LE=function(e,t){return e>>>=0,t||b(e,2,this.length),this[e]|this[e+1]<<8},l.prototype.readUInt16BE=function(e,t){return e>>>=0,t||b(e,2,this.length),this[e]<<8|this[e+1]},l.prototype.readUInt32LE=function(e,t){return e>>>=0,t||b(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},l.prototype.readUInt32BE=function(e,t){return e>>>=0,t||b(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},l.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},l.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=t,i=1,o=this[e+--n];n>0&&(i*=256);)o+=this[e+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},l.prototype.readInt8=function(e,t){return(e>>>=0,t||b(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},l.prototype.readInt16LE=function(e,t){e>>>=0,t||b(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},l.prototype.readInt16BE=function(e,t){e>>>=0,t||b(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},l.prototype.readInt32LE=function(e,t){return e>>>=0,t||b(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},l.prototype.readInt32BE=function(e,t){return e>>>=0,t||b(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},l.prototype.readFloatLE=function(e,t){return e>>>=0,t||b(e,4,this.length),i.read(this,e,!0,23,4)},l.prototype.readFloatBE=function(e,t){return e>>>=0,t||b(e,4,this.length),i.read(this,e,!1,23,4)},l.prototype.readDoubleLE=function(e,t){return e>>>=0,t||b(e,8,this.length),i.read(this,e,!0,52,8)},l.prototype.readDoubleBE=function(e,t){return e>>>=0,t||b(e,8,this.length),i.read(this,e,!1,52,8)},l.prototype.writeUIntLE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;w(this,e,t,r,i,0)}var o=1,a=0;for(this[t]=255&e;++a<r&&(o*=256);)this[t+a]=e/o&255;return t+r},l.prototype.writeUIntBE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;w(this,e,t,r,i,0)}var o=r-1,a=1;for(this[t+o]=255&e;--o>=0&&(a*=256);)this[t+o]=e/a&255;return t+r},l.prototype.writeUInt8=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,1,255,0),this[t]=255&e,t+1},l.prototype.writeUInt16LE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},l.prototype.writeUInt16BE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},l.prototype.writeUInt32LE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,4,0xffffffff,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},l.prototype.writeUInt32BE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,4,0xffffffff,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},l.prototype.writeIntLE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var i=Math.pow(2,8*r-1);w(this,e,t,r,i-1,-i)}var o=0,a=1,l=0;for(this[t]=255&e;++o<r&&(a*=256);)e<0&&0===l&&0!==this[t+o-1]&&(l=1),this[t+o]=(e/a|0)-l&255;return t+r},l.prototype.writeIntBE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var i=Math.pow(2,8*r-1);w(this,e,t,r,i-1,-i)}var o=r-1,a=1,l=0;for(this[t+o]=255&e;--o>=0&&(a*=256);)e<0&&0===l&&0!==this[t+o+1]&&(l=1),this[t+o]=(e/a|0)-l&255;return t+r},l.prototype.writeInt8=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},l.prototype.writeInt16LE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},l.prototype.writeInt16BE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},l.prototype.writeInt32LE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,4,0x7fffffff,-0x80000000),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},l.prototype.writeInt32BE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},l.prototype.writeFloatLE=function(e,t,r){return O(this,e,t,!0,r)},l.prototype.writeFloatBE=function(e,t,r){return O(this,e,t,!1,r)},l.prototype.writeDoubleLE=function(e,t,r){return M(this,e,t,!0,r)},l.prototype.writeDoubleBE=function(e,t,r){return M(this,e,t,!1,r)},l.prototype.copy=function(e,t,r,n){if(!l.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i=n-r;if(this===e&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(t,r,n);else if(this===e&&r<t&&t<n)for(var o=i-1;o>=0;--o)e[o+t]=this[o+r];else Uint8Array.prototype.set.call(e,this.subarray(r,n),t);return i},l.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!l.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===e.length){var i,o=e.charCodeAt(0);("utf8"===n&&o<128||"latin1"===n)&&(e=o)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(i=t;i<r;++i)this[i]=e;else{var a=l.isBuffer(e)?e:l.from(e,n),u=a.length;if(0===u)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(i=0;i<r-t;++i)this[i+t]=a[i%u]}return this};var A=/[^+/0-9A-Za-z-_]/g;function j(e,t){t=t||1/0;for(var r,n=e.length,i=null,o=[],a=0;a<n;++a){if((r=e.charCodeAt(a))>55295&&r<57344){if(!i){if(r>56319||a+1===n){(t-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return o}function S(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function _(e){return n.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(A,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function P(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length)&&!(i>=e.length);++i)t[i+r]=e[i];return i}function E(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}var k=function(){for(var e="0123456789abcdef",t=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)t[n+i]=e[r]+e[i];return t}()},783:function(e,t){t.read=function(e,t,r,n,i){var o,a,l=8*i-n-1,u=(1<<l)-1,s=u>>1,c=-7,f=r?i-1:0,d=r?-1:1,h=e[t+f];for(f+=d,o=h&(1<<-c)-1,h>>=-c,c+=l;c>0;o=256*o+e[t+f],f+=d,c-=8);for(a=o&(1<<-c)-1,o>>=-c,c+=n;c>0;a=256*a+e[t+f],f+=d,c-=8);if(0===o)o=1-s;else{if(o===u)return a?NaN:1/0*(h?-1:1);a+=Math.pow(2,n),o-=s}return(h?-1:1)*a*Math.pow(2,o-n)},t.write=function(e,t,r,n,i,o){var a,l,u,s=8*o-i-1,c=(1<<s)-1,f=c>>1,d=5960464477539062e-23*(23===i),h=n?0:o-1,p=n?1:-1,y=+(t<0||0===t&&1/t<0);for(isNaN(t=Math.abs(t))||t===1/0?(l=+!!isNaN(t),a=c):(a=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-a))<1&&(a--,u*=2),a+f>=1?t+=d/u:t+=d*Math.pow(2,1-f),t*u>=2&&(a++,u/=2),a+f>=c?(l=0,a=c):a+f>=1?(l=(t*u-1)*Math.pow(2,i),a+=f):(l=t*Math.pow(2,f-1)*Math.pow(2,i),a=0));i>=8;e[r+h]=255&l,h+=p,l/=256,i-=8);for(a=a<<i|l,s+=i;s>0;e[r+h]=255&a,h+=p,a/=256,s-=8);e[r+h-p]|=128*y}}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var o=r[e]={exports:{}},a=!0;try{t[e](o,o.exports,n),a=!1}finally{a&&delete r[e]}return o.exports}n.ab="//",e.exports=n(72)}()},15870:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16940:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}},17215:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("arrow-down-left",[["path",{d:"M17 7 7 17",key:"15tmo1"}],["path",{d:"M17 17H7V7",key:"1org7z"}]])},19052:(e,t,r)=>{"use strict";r.d(t,{J:()=>o,U:()=>i});var n=(0,r(26286).Z0)({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:i}=n.actions,o=n.reducer},20821:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},21838:(e,t,r)=>{"use strict";r.d(t,{P:()=>i});var n=r(92377),i=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var i=Number(r);if(!(0,n.H)(i))return r;var o=Infinity;return t.length>0&&(o=t.length-1),String(Math.max(0,Math.min(i,o)))}},23125:(e,t,r)=>{"use strict";r.d(t,{o:()=>n});var n=(e,t,r,n,i,o,a,l)=>{if(null!=o&&null!=l){var u=a[0],s=null==u?void 0:l(u.positions,o);if(null!=s)return s;var c=null==i?void 0:i[Number(o)];if(c)if("horizontal"===r)return{x:c.coordinate,y:(n.top+t)/2};else return{x:(n.left+e)/2,y:c.coordinate}}}},23134:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}},24021:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(34565),i=r(51023);function o(e,t,r){var{chartData:o=[]}=t,a=null==r?void 0:r.dataKey,l=new Map;return e.forEach(e=>{var t,r=null!=(t=e.data)?t:o;if(null!=r&&0!==r.length){var u=(0,n.x)(e);r.forEach((t,r)=>{var n,o=null==a?r:String((0,i.kr)(t,a,null)),s=(0,i.kr)(t,e.dataKey,0);Object.assign(n=l.has(o)?l.get(o):{},{[u]:s}),l.set(o,n)})}}),Array.from(l.values())}},25543:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var n=e=>e.tooltip},25723:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toPath=function(e){let t=[],r=e.length;if(0===r)return t;let n=0,i="",o="",a=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){let l=e[n];o?"\\"===l&&n+1<r?i+=e[++n]:l===o?o="":i+=l:a?'"'===l||"'"===l?o=l:"]"===l?(a=!1,t.push(i),i=""):i+=l:"["===l?(a=!0,i&&(t.push(i),i="")):"."===l?i&&(t.push(i),i=""):i+=l,n++}return i&&t.push(i),t}},26286:(e,t,r)=>{"use strict";r.d(t,{U1:()=>h,VP:()=>u,Nc:()=>Y,Z0:()=>v});var n=r(81093);function i(e){return({dispatch:t,getState:r})=>n=>i=>"function"==typeof i?i(t,r,e):n(i)}var o=i(),a=r(60013),l=(r(95704),"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?n.Zz:n.Zz.apply(null,arguments)});function u(e,t){function r(...n){if(t){let r=t(...n);if(!r)throw Error(V(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>(0,n.ve)(t)&&t.type===e,r}"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var s=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function c(e){return(0,a.a6)(e)?(0,a.jM)(e,()=>{}):e}function f(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var d=e=>t=>{setTimeout(t,e)};function h(e){let t,r,a,u=function(e){let{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:a=!0}=e??{},l=new s;return t&&("boolean"==typeof t?l.push(o):l.push(i(t.extraArgument))),l},{reducer:c,middleware:f,devTools:h=!0,duplicateMiddlewareCheck:p=!0,preloadedState:y,enhancers:g}=e||{};if("function"==typeof c)t=c;else if((0,n.Qd)(c))t=(0,n.HY)(c);else throw Error(V(1));r="function"==typeof f?f(u):u();let v=n.Zz;h&&(v=l({trace:!1,..."object"==typeof h&&h}));let m=(a=(0,n.Tw)(...r),function(e){let{autoBatch:t=!0}=e??{},r=new s(a);return t&&r.push(((e={type:"raf"})=>t=>(...r)=>{let n=t(...r),i=!0,o=!1,a=!1,l=new Set,u="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:d(10):"callback"===e.type?e.queueNotification:d(e.timeout),s=()=>{a=!1,o&&(o=!1,l.forEach(e=>e()))};return Object.assign({},n,{subscribe(e){let t=n.subscribe(()=>i&&e());return l.add(e),()=>{t(),l.delete(e)}},dispatch(e){try{return(o=!(i=!e?.meta?.RTK_autoBatch))&&!a&&(a=!0,u(s)),n.dispatch(e)}finally{i=!0}}})})("object"==typeof t?t:void 0)),r}),b=v(..."function"==typeof g?g(m):m());return(0,n.y$)(t,y,b)}function p(e){let t,r={},n=[],i={addCase(e,t){let n="string"==typeof e?e:e.type;if(!n)throw Error(V(28));if(n in r)throw Error(V(29));return r[n]=t,i},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),i),addDefaultCase:e=>(t=e,i)};return e(i),[r,n,t]}var y=Symbol.for("rtk-slice-createasyncthunk"),g=(e=>(e.reducer="reducer",e.reducerWithPrepare="reducerWithPrepare",e.asyncThunk="asyncThunk",e))(g||{}),v=function({creators:e}={}){let t=e?.asyncThunk?.[y];return function(e){let r,{name:n,reducerPath:i=n}=e;if(!n)throw Error(V(11));let o=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},l=Object.keys(o),s={},d={},h={},y=[],g={addCase(e,t){let r="string"==typeof e?e:e.type;if(!r)throw Error(V(12));if(r in d)throw Error(V(13));return d[r]=t,g},addMatcher:(e,t)=>(y.push({matcher:e,reducer:t}),g),exposeAction:(e,t)=>(h[e]=t,g),exposeCaseReducer:(e,t)=>(s[e]=t,g)};function v(){let[t={},r=[],n]="function"==typeof e.extraReducers?p(e.extraReducers):[e.extraReducers],i={...t,...d};return function(e,t){let r,[n,i,o]=p(t);if("function"==typeof e)r=()=>c(e());else{let t=c(e);r=()=>t}function l(e=r(),t){let u=[n[t.type],...i.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===u.filter(e=>!!e).length&&(u=[o]),u.reduce((e,r)=>{if(r)if((0,a.Qx)(e)){let n=r(e,t);return void 0===n?e:n}else{if((0,a.a6)(e))return(0,a.jM)(e,e=>r(e,t));let n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return e},e)}return l.getInitialState=r,l}(e.initialState,e=>{for(let t in i)e.addCase(t,i[t]);for(let t of y)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}l.forEach(r=>{let i=o[r],a={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof e.reducers};"asyncThunk"===i._reducerDefinitionType?function({type:e,reducerName:t},r,n,i){if(!i)throw Error(V(18));let{payloadCreator:o,fulfilled:a,pending:l,rejected:u,settled:s,options:c}=r,f=i(e,o,c);n.exposeAction(t,f),a&&n.addCase(f.fulfilled,a),l&&n.addCase(f.pending,l),u&&n.addCase(f.rejected,u),s&&n.addMatcher(f.settled,s),n.exposeCaseReducer(t,{fulfilled:a||m,pending:l||m,rejected:u||m,settled:s||m})}(a,i,g,t):function({type:e,reducerName:t,createNotation:r},n,i){let o,a;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(V(17));o=n.reducer,a=n.prepare}else o=n;i.addCase(e,o).exposeCaseReducer(t,o).exposeAction(t,a?u(e,a):u(e))}(a,i,g)});let b=e=>e,w=new Map,x=new WeakMap;function O(e,t){return r||(r=v()),r(e,t)}function M(){return r||(r=v()),r.getInitialState()}function A(t,r=!1){function n(e){let i=e[t];return void 0===i&&r&&(i=f(x,n,M)),i}function i(t=b){let n=f(w,r,()=>new WeakMap);return f(n,t,()=>{let n={};for(let[i,o]of Object.entries(e.selectors??{}))n[i]=function(e,t,r,n){function i(o,...a){let l=t(o);return void 0===l&&n&&(l=r()),e(l,...a)}return i.unwrapped=e,i}(o,t,()=>f(x,t,M),r);return n})}return{reducerPath:t,getSelectors:i,get selectors(){return i(n)},selectSlice:n}}let j={name:n,reducer:O,actions:h,caseReducers:s,getInitialState:M,...A(i),injectInto(e,{reducerPath:t,...r}={}){let n=t??i;return e.inject({reducerPath:n,reducer:O},r),{...j,...A(n,!0)}}};return j}}();function m(){}var b="listener",w="completed",x="cancelled",O=`task-${x}`,M=`task-${w}`,A=`${b}-${x}`,j=`${b}-${w}`,S=class{constructor(e){this.code=e,this.message=`task ${x} (reason: ${e})`}name="TaskAbortError";message},_=(e,t)=>{if("function"!=typeof e)throw TypeError(V(32))},P=()=>{},E=(e,t=P)=>(e.catch(t),e),k=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),T=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},C=e=>{if(e.aborted){let{reason:t}=e;throw new S(t)}};function N(e,t){let r=P;return new Promise((n,i)=>{let o=()=>i(new S(e.reason));if(e.aborted)return void o();r=k(e,o),t.finally(()=>r()).then(n,i)}).finally(()=>{r=P})}var D=async(e,t)=>{try{await Promise.resolve();let t=await e();return{status:"ok",value:t}}catch(e){return{status:e instanceof S?"cancelled":"rejected",error:e}}finally{t?.()}},z=e=>t=>E(N(e,t).then(t=>(C(e),t))),I=e=>{let t=z(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:R}=Object,L={},U="listenerMiddleware",B=e=>{let{type:t,actionCreator:r,matcher:n,predicate:i,effect:o}=e;if(t)i=u(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(i);else throw Error(V(21));return _(o,"options.listener"),{predicate:i,type:t,effect:o}},$=R(e=>{let{type:t,predicate:r,effect:n}=B(e);return{id:((e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t})(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(V(22))}}},{withTypes:()=>$}),F=(e,t)=>{let{type:r,effect:n,predicate:i}=B(t);return Array.from(e.values()).find(e=>("string"==typeof r?e.type===r:e.predicate===i)&&e.effect===n)},H=e=>{e.pending.forEach(e=>{T(e,A)})},K=(e,t,r)=>{try{e(t,r)}catch(e){setTimeout(()=>{throw e},0)}},q=R(u(`${U}/add`),{withTypes:()=>q}),W=u(`${U}/removeAll`),Z=R(u(`${U}/remove`),{withTypes:()=>Z}),G=(...e)=>{console.error(`${U}/error`,...e)},Y=(e={})=>{let t=new Map,{extra:r,onError:i=G}=e;_(i,"onError");let o=e=>(e=>(e.unsubscribe=()=>t.delete(e.id),t.set(e.id,e),t=>{e.unsubscribe(),t?.cancelActive&&H(e)}))(F(t,e)??$(e));R(o,{withTypes:()=>o});let a=e=>{let r=F(t,e);return r&&(r.unsubscribe(),e.cancelActive&&H(r)),!!r};R(a,{withTypes:()=>a});let l=async(e,n,a,l)=>{let u=new AbortController,s=((e,t)=>{let r=async(r,n)=>{C(t);let i=()=>{},o=[new Promise((t,n)=>{let o=e({predicate:r,effect:(e,r)=>{r.unsubscribe(),t([e,r.getState(),r.getOriginalState()])}});i=()=>{o(),n()}})];null!=n&&o.push(new Promise(e=>setTimeout(e,n,null)));try{let e=await N(t,Promise.race(o));return C(t),e}finally{i()}};return(e,t)=>E(r(e,t))})(o,u.signal),c=[];try{e.pending.add(u),await Promise.resolve(e.effect(n,R({},a,{getOriginalState:l,condition:(e,t)=>s(e,t).then(Boolean),take:s,delay:I(u.signal),pause:z(u.signal),extra:r,signal:u.signal,fork:((e,t)=>(r,n)=>{_(r,"taskExecutor");let i=new AbortController;k(e,()=>T(i,e.reason));let o=D(async()=>{C(e),C(i.signal);let t=await r({pause:z(i.signal),delay:I(i.signal),signal:i.signal});return C(i.signal),t},()=>T(i,M));return n?.autoJoin&&t.push(o.catch(P)),{result:z(e)(o),cancel(){T(i,O)}}})(u.signal,c),unsubscribe:e.unsubscribe,subscribe:()=>{t.set(e.id,e)},cancelActiveListeners:()=>{e.pending.forEach((e,t,r)=>{e!==u&&(T(e,A),r.delete(e))})},cancel:()=>{T(u,A),e.pending.delete(u)},throwIfCancelled:()=>{C(u.signal)}})))}catch(e){e instanceof S||K(i,e,{raisedBy:"effect"})}finally{await Promise.all(c),T(u,j),e.pending.delete(u)}},u=(e=>()=>{e.forEach(H),e.clear()})(t);return{middleware:e=>r=>s=>{let c;if(!(0,n.ve)(s))return r(s);if(q.match(s))return o(s.payload);if(W.match(s))return void u();if(Z.match(s))return a(s.payload);let f=e.getState(),d=()=>{if(f===L)throw Error(V(23));return f};try{if(c=r(s),t.size>0){let r=e.getState();for(let n of Array.from(t.values())){let t=!1;try{t=n.predicate(s,r,f)}catch(e){t=!1,K(i,e,{raisedBy:"predicate"})}t&&l(n,s,e,d)}}}finally{f=L}return c},startListening:o,stopListening:a,clearListeners:u}};function V(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}Symbol.for("rtk-state-proxy-original")},26991:(e,t,r)=>{"use strict";r.d(t,{u:()=>f});var n=r(2821),i=r(12115),o=r(88011),a=r.n(o),l=r(49580),u=r(58672);function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var f=(0,i.forwardRef)((e,t)=>{var{aspect:r,initialDimension:o={width:-1,height:-1},width:s="100%",height:f="100%",minWidth:d=0,minHeight:h,maxHeight:p,children:y,debounce:g=0,id:v,className:m,onResize:b,style:w={}}=e,x=(0,i.useRef)(null),O=(0,i.useRef)();O.current=b,(0,i.useImperativeHandle)(t,()=>x.current);var[M,A]=(0,i.useState)({containerWidth:o.width,containerHeight:o.height}),j=(0,i.useCallback)((e,t)=>{A(r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,i.useEffect)(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;j(r,n),null==(t=O.current)||t.call(O,r,n)};g>0&&(e=a()(e,g,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=x.current.getBoundingClientRect();return j(r,n),t.observe(x.current),()=>{t.disconnect()}},[j,g]);var S=(0,i.useMemo)(()=>{var{containerWidth:e,containerHeight:t}=M;if(e<0||t<0)return null;(0,u.R)((0,l._3)(s)||(0,l._3)(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",s,f),(0,u.R)(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=(0,l._3)(s)?e:s,o=(0,l._3)(f)?t:f;return r&&r>0&&(n?o=n/r:o&&(n=o*r),p&&o>p&&(o=p)),(0,u.R)(n>0||o>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,o,s,f,d,h,r),i.Children.map(y,e=>(0,i.cloneElement)(e,{width:n,height:o,style:c({width:n,height:o},e.props.style)}))},[r,y,f,p,h,d,M,s]);return i.createElement("div",{id:v?"".concat(v):void 0,className:(0,n.$)("recharts-responsive-container",m),style:c(c({},w),{},{width:s,height:f,minWidth:d,minHeight:h,maxHeight:p}),ref:x},i.createElement("div",{style:{width:0,height:0,overflow:"visible"}},S))})},27012:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}r.d(t,{A:()=>n}),Array.prototype.slice},27243:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(40495),i=r(35865),o=r(87744),a=r(77159);function l(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return u(e,t,r,n);if(t instanceof Map){var i=e,a=t,l=r,c=n;if(0===a.size)return!0;if(!(i instanceof Map))return!1;for(let[e,t]of a.entries())if(!1===l(i.get(e),t,e,i,a,c))return!1;return!0}if(t instanceof Set)return s(e,t,r,n);let f=Object.keys(t);if(null==e)return 0===f.length;if(0===f.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let i=0;i<f.length;i++){let a=f[i];if(!o.isPrimitive(e)&&!(a in e)||void 0===t[a]&&void 0!==e[a]||null===t[a]&&null!==e[a]||!r(e[a],t[a],a,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":if(Object.keys(t).length>0)return l(e,{...t},r,n);return a.eq(e,t);default:if(!i.isObject(e))return a.eq(e,t);if("string"==typeof t)return""===t;return!0}}function u(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;let i=new Set;for(let o=0;o<t.length;o++){let a=t[o],l=!1;for(let u=0;u<e.length;u++){if(i.has(u))continue;let s=e[u],c=!1;if(r(s,a,o,e,t,n)&&(c=!0),c){i.add(u),l=!0;break}}if(!l)return!1}return!0}function s(e,t,r,n){return 0===t.size||e instanceof Set&&u([...e],[...t],r,n)}t.isMatchWith=function(e,t,r){return"function"!=typeof r?n.isMatch(e,t):l(e,t,function e(t,n,i,o,a,u){let s=r(t,n,i,o,a,u);return void 0!==s?!!s:l(t,n,e,u)},new Map)},t.isSetMatch=s},27588:(e,t,r)=>{"use strict";r.d(t,{A$:()=>i,HK:()=>a,Lp:()=>n,et:()=>o});var n=e=>e.layout.width,i=e=>e.layout.height,o=e=>e.layout.scale,a=e=>e.layout.margin},27761:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(93276);t.property=function(e){return function(t){return n.get(t,e)}}},28086:(e,t,r)=>{e.exports=r(71669).isPlainObject},28138:(e,t,r)=>{"use strict";r(95962)},29158:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43597);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var a=(e,t,r)=>e+(t-e)*r,l=e=>{var{from:t,to:r}=e;return t!==r},u=(e,t,r)=>{var i=(0,n.s8)((t,r)=>{if(l(r)){var[n,i]=e(r.from,r.to,r.velocity);return o(o({},r),{},{from:n,velocity:i})}return r},t);return r<1?(0,n.s8)((e,t)=>l(t)?o(o({},t),{},{velocity:a(t.velocity,i[e].velocity,r),from:a(t.from,i[e].from,r)}):t,t):u(e,i,r-1)};let s=(e,t,r,i,s,c)=>{var f=(0,n.mP)(e,t);return!0===r.isStepper?function(e,t,r,i,a,s){var c,f=i.reduce((r,n)=>o(o({},r),{},{[n]:{from:e[n],velocity:0,to:t[n]}}),{}),d=null,h=i=>{c||(c=i);var p=(i-c)/r.dt;f=u(r,f,p),a(o(o(o({},e),t),(0,n.s8)((e,t)=>t.from,f))),c=i,Object.values(f).filter(l).length&&(d=s.setTimeout(h))};return()=>(d=s.setTimeout(h),()=>{d()})}(e,t,r,f,s,c):function(e,t,r,i,l,u,s){var c,f=null,d=l.reduce((r,n)=>o(o({},r),{},{[n]:[e[n],t[n]]}),{}),h=l=>{c||(c=l);var p=(l-c)/i,y=(0,n.s8)((e,t)=>a(...t,r(p)),d);if(u(o(o(o({},e),t),y)),p<1)f=s.setTimeout(h);else{var g=(0,n.s8)((e,t)=>a(...t,r(1)),d);u(o(o(o({},e),t),g))}};return()=>(f=s.setTimeout(h),()=>{f()})}(e,t,r,i,f,s,c)}},29439:(e,t,r)=>{"use strict";function n(e,t,r){return Array.isArray(e)&&e&&t+r!==0?e.slice(t,r+1):e}r.d(t,{v:()=>n})},29533:function(e,t,r){var n;!function(i){"use strict";var o,a={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},l=!0,u="[DecimalError] ",s=u+"Invalid argument: ",c=u+"Exponent out of range: ",f=Math.floor,d=Math.pow,h=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,p=f(1286742750677284.5),y={};function g(e,t){var r,n,i,o,a,u,s,c,f=e.constructor,d=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),l?S(t,d):t;if(s=e.d,c=t.d,a=e.e,i=t.e,s=s.slice(),o=a-i){for(o<0?(n=s,o=-o,u=c.length):(n=c,i=a,u=s.length),o>(u=(a=Math.ceil(d/7))>u?a+1:u+1)&&(o=u,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for((u=s.length)-(o=c.length)<0&&(o=u,n=c,c=s,s=n),r=0;o;)r=(s[--o]=s[o]+c[o]+r)/1e7|0,s[o]%=1e7;for(r&&(s.unshift(r),++i),u=s.length;0==s[--u];)s.pop();return t.d=s,t.e=i,l?S(t,d):t}function v(e,t,r){if(e!==~~e||e<t||e>r)throw Error(s+e)}function m(e){var t,r,n,i=e.length-1,o="",a=e[0];if(i>0){for(o+=a,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(o+=M(r)),o+=n;(r=7-(n=(a=e[t])+"").length)&&(o+=M(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return o+a}y.absoluteValue=y.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},y.comparedTo=y.cmp=function(e){var t,r,n,i;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(i=e.d.length)?n:i;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},y.decimalPlaces=y.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},y.dividedBy=y.div=function(e){return b(this,new this.constructor(e))},y.dividedToIntegerBy=y.idiv=function(e){var t=this.constructor;return S(b(this,new t(e),0,1),t.precision)},y.equals=y.eq=function(e){return!this.cmp(e)},y.exponent=function(){return x(this)},y.greaterThan=y.gt=function(e){return this.cmp(e)>0},y.greaterThanOrEqualTo=y.gte=function(e){return this.cmp(e)>=0},y.isInteger=y.isint=function(){return this.e>this.d.length-2},y.isNegative=y.isneg=function(){return this.s<0},y.isPositive=y.ispos=function(){return this.s>0},y.isZero=function(){return 0===this.s},y.lessThan=y.lt=function(e){return 0>this.cmp(e)},y.lessThanOrEqualTo=y.lte=function(e){return 1>this.cmp(e)},y.logarithm=y.log=function(e){var t,r=this.constructor,n=r.precision,i=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(o))throw Error(u+"NaN");if(this.s<1)throw Error(u+(this.s?"NaN":"-Infinity"));return this.eq(o)?new r(0):(l=!1,t=b(A(this,i),A(e,i),i),l=!0,S(t,n))},y.minus=y.sub=function(e){return e=new this.constructor(e),this.s==e.s?_(this,e):g(this,(e.s=-e.s,e))},y.modulo=y.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(u+"NaN");return this.s?(l=!1,t=b(this,e,0,1).times(e),l=!0,this.minus(t)):S(new r(this),n)},y.naturalExponential=y.exp=function(){return w(this)},y.naturalLogarithm=y.ln=function(){return A(this)},y.negated=y.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},y.plus=y.add=function(e){return e=new this.constructor(e),this.s==e.s?g(this,e):_(this,(e.s=-e.s,e))},y.precision=y.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(s+e);if(t=x(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},y.squareRoot=y.sqrt=function(){var e,t,r,n,i,o,a,s=this.constructor;if(this.s<1){if(!this.s)return new s(0);throw Error(u+"NaN")}for(e=x(this),l=!1,0==(i=Math.sqrt(+this))||i==1/0?(((t=m(this.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=f((e+1)/2)-(e<0||e%2),n=new s(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new s(i.toString()),i=a=(r=s.precision)+3;;)if(n=(o=n).plus(b(this,o,a+2)).times(.5),m(o.d).slice(0,a)===(t=m(n.d)).slice(0,a)){if(t=t.slice(a-3,a+1),i==a&&"4999"==t){if(S(o,r+1,0),o.times(o).eq(this)){n=o;break}}else if("9999"!=t)break;a+=4}return l=!0,S(n,r)},y.times=y.mul=function(e){var t,r,n,i,o,a,u,s,c,f=this.constructor,d=this.d,h=(e=new f(e)).d;if(!this.s||!e.s)return new f(0);for(e.s*=this.s,r=this.e+e.e,(s=d.length)<(c=h.length)&&(o=d,d=h,h=o,a=s,s=c,c=a),o=[],n=a=s+c;n--;)o.push(0);for(n=c;--n>=0;){for(t=0,i=s+n;i>n;)u=o[i]+h[n]*d[i-n-1]+t,o[i--]=u%1e7|0,t=u/1e7|0;o[i]=(o[i]+t)%1e7|0}for(;!o[--a];)o.pop();return t?++r:o.shift(),e.d=o,e.e=r,l?S(e,f.precision):e},y.toDecimalPlaces=y.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(v(e,0,1e9),void 0===t?t=n.rounding:v(t,0,8),S(r,e+x(r)+1,t))},y.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=P(n,!0):(v(e,0,1e9),void 0===t?t=i.rounding:v(t,0,8),r=P(n=S(new i(n),e+1,t),!0,e+1)),r},y.toFixed=function(e,t){var r,n,i=this.constructor;return void 0===e?P(this):(v(e,0,1e9),void 0===t?t=i.rounding:v(t,0,8),r=P((n=S(new i(this),e+x(this)+1,t)).abs(),!1,e+x(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},y.toInteger=y.toint=function(){var e=this.constructor;return S(new e(this),x(this)+1,e.rounding)},y.toNumber=function(){return+this},y.toPower=y.pow=function(e){var t,r,n,i,a,s,c=this,d=c.constructor,h=+(e=new d(e));if(!e.s)return new d(o);if(!(c=new d(c)).s){if(e.s<1)throw Error(u+"Infinity");return c}if(c.eq(o))return c;if(n=d.precision,e.eq(o))return S(c,n);if(s=(t=e.e)>=(r=e.d.length-1),a=c.s,s){if((r=h<0?-h:h)<=0x1fffffffffffff){for(i=new d(o),t=Math.ceil(n/7+4),l=!1;r%2&&E((i=i.times(c)).d,t),0!==(r=f(r/2));)E((c=c.times(c)).d,t);return l=!0,e.s<0?new d(o).div(i):S(i,n)}}else if(a<0)throw Error(u+"NaN");return a=a<0&&1&e.d[Math.max(t,r)]?-1:1,c.s=1,l=!1,i=e.times(A(c,n+12)),l=!0,(i=w(i)).s=a,i},y.toPrecision=function(e,t){var r,n,i=this,o=i.constructor;return void 0===e?(r=x(i),n=P(i,r<=o.toExpNeg||r>=o.toExpPos)):(v(e,1,1e9),void 0===t?t=o.rounding:v(t,0,8),r=x(i=S(new o(i),e,t)),n=P(i,e<=r||r<=o.toExpNeg,e)),n},y.toSignificantDigits=y.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(v(e,1,1e9),void 0===t?t=r.rounding:v(t,0,8)),S(new r(this),e,t)},y.toString=y.valueOf=y.val=y.toJSON=function(){var e=x(this),t=this.constructor;return P(this,e<=t.toExpNeg||e>=t.toExpPos)};var b=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,o;if(r!=n)o=r>n?1:-1;else for(i=o=0;i<r;i++)if(e[i]!=t[i]){o=e[i]>t[i]?1:-1;break}return o}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,o,a){var l,s,c,f,d,h,p,y,g,v,m,b,w,O,M,A,j,_,P=n.constructor,E=n.s==i.s?1:-1,k=n.d,T=i.d;if(!n.s)return new P(n);if(!i.s)throw Error(u+"Division by zero");for(c=0,s=n.e-i.e,j=T.length,M=k.length,y=(p=new P(E)).d=[];T[c]==(k[c]||0);)++c;if(T[c]>(k[c]||0)&&--s,(b=null==o?o=P.precision:a?o+(x(n)-x(i))+1:o)<0)return new P(0);if(b=b/7+2|0,c=0,1==j)for(f=0,T=T[0],b++;(c<M||f)&&b--;c++)w=1e7*f+(k[c]||0),y[c]=w/T|0,f=w%T|0;else{for((f=1e7/(T[0]+1)|0)>1&&(T=e(T,f),k=e(k,f),j=T.length,M=k.length),O=j,v=(g=k.slice(0,j)).length;v<j;)g[v++]=0;(_=T.slice()).unshift(0),A=T[0],T[1]>=1e7/2&&++A;do f=0,(l=t(T,g,j,v))<0?(m=g[0],j!=v&&(m=1e7*m+(g[1]||0)),(f=m/A|0)>1?(f>=1e7&&(f=1e7-1),h=(d=e(T,f)).length,v=g.length,1==(l=t(d,g,h,v))&&(f--,r(d,j<h?_:T,h))):(0==f&&(l=f=1),d=T.slice()),(h=d.length)<v&&d.unshift(0),r(g,d,v),-1==l&&(v=g.length,(l=t(T,g,j,v))<1&&(f++,r(g,j<v?_:T,v))),v=g.length):0===l&&(f++,g=[0]),y[c++]=f,l&&g[0]?g[v++]=k[O]||0:(g=[k[O]],v=1);while((O++<M||void 0!==g[0])&&b--)}return y[0]||y.shift(),p.e=s,S(p,a?o+x(p)+1:o)}}();function w(e,t){var r,n,i,a,u,s=0,f=0,h=e.constructor,p=h.precision;if(x(e)>16)throw Error(c+x(e));if(!e.s)return new h(o);for(null==t?(l=!1,u=p):u=t,a=new h(.03125);e.abs().gte(.1);)e=e.times(a),f+=5;for(u+=Math.log(d(2,f))/Math.LN10*2+5|0,r=n=i=new h(o),h.precision=u;;){if(n=S(n.times(e),u),r=r.times(++s),m((a=i.plus(b(n,r,u))).d).slice(0,u)===m(i.d).slice(0,u)){for(;f--;)i=S(i.times(i),u);return h.precision=p,null==t?(l=!0,S(i,p)):i}i=a}}function x(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function O(e,t,r){if(t>e.LN10.sd())throw l=!0,r&&(e.precision=r),Error(u+"LN10 precision limit exceeded");return S(new e(e.LN10),t)}function M(e){for(var t="";e--;)t+="0";return t}function A(e,t){var r,n,i,a,s,c,f,d,h,p=1,y=e,g=y.d,v=y.constructor,w=v.precision;if(y.s<1)throw Error(u+(y.s?"NaN":"-Infinity"));if(y.eq(o))return new v(0);if(null==t?(l=!1,d=w):d=t,y.eq(10))return null==t&&(l=!0),O(v,d);if(v.precision=d+=10,n=(r=m(g)).charAt(0),!(15e14>Math.abs(a=x(y))))return f=O(v,d+2,w).times(a+""),y=A(new v(n+"."+r.slice(1)),d-10).plus(f),v.precision=w,null==t?(l=!0,S(y,w)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=m((y=y.times(e)).d)).charAt(0),p++;for(a=x(y),n>1?(y=new v("0."+r),a++):y=new v(n+"."+r.slice(1)),c=s=y=b(y.minus(o),y.plus(o),d),h=S(y.times(y),d),i=3;;){if(s=S(s.times(h),d),m((f=c.plus(b(s,new v(i),d))).d).slice(0,d)===m(c.d).slice(0,d))return c=c.times(2),0!==a&&(c=c.plus(O(v,d+2,w).times(a+""))),c=b(c,new v(p),d),v.precision=w,null==t?(l=!0,S(c,w)):c;c=f,i+=2}}function j(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,e.e=f((r=r-n-1)/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),l&&(e.e>p||e.e<-p))throw Error(c+r)}else e.s=0,e.e=0,e.d=[0];return e}function S(e,t,r){var n,i,o,a,u,s,h,y,g=e.d;for(a=1,o=g[0];o>=10;o/=10)a++;if((n=t-a)<0)n+=7,i=t,h=g[y=0];else{if((y=Math.ceil((n+1)/7))>=(o=g.length))return e;for(a=1,h=o=g[y];o>=10;o/=10)a++;n%=7,i=n-7+a}if(void 0!==r&&(u=h/(o=d(10,a-i-1))%10|0,s=t<0||void 0!==g[y+1]||h%o,s=r<4?(u||s)&&(0==r||r==(e.s<0?3:2)):u>5||5==u&&(4==r||s||6==r&&(n>0?i>0?h/d(10,a-i):0:g[y-1])%10&1||r==(e.s<0?8:7))),t<1||!g[0])return s?(o=x(e),g.length=1,t=t-o-1,g[0]=d(10,(7-t%7)%7),e.e=f(-t/7)||0):(g.length=1,g[0]=e.e=e.s=0),e;if(0==n?(g.length=y,o=1,y--):(g.length=y+1,o=d(10,7-n),g[y]=i>0?(h/d(10,a-i)%d(10,i)|0)*o:0),s)for(;;)if(0==y){1e7==(g[0]+=o)&&(g[0]=1,++e.e);break}else{if(g[y]+=o,1e7!=g[y])break;g[y--]=0,o=1}for(n=g.length;0===g[--n];)g.pop();if(l&&(e.e>p||e.e<-p))throw Error(c+x(e));return e}function _(e,t){var r,n,i,o,a,u,s,c,f,d,h=e.constructor,p=h.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new h(e),l?S(t,p):t;if(s=e.d,d=t.d,n=t.e,c=e.e,s=s.slice(),a=c-n){for((f=a<0)?(r=s,a=-a,u=d.length):(r=d,n=c,u=s.length),a>(i=Math.max(Math.ceil(p/7),u)+2)&&(a=i,r.length=1),r.reverse(),i=a;i--;)r.push(0);r.reverse()}else{for((f=(i=s.length)<(u=d.length))&&(u=i),i=0;i<u;i++)if(s[i]!=d[i]){f=s[i]<d[i];break}a=0}for(f&&(r=s,s=d,d=r,t.s=-t.s),u=s.length,i=d.length-u;i>0;--i)s[u++]=0;for(i=d.length;i>a;){if(s[--i]<d[i]){for(o=i;o&&0===s[--o];)s[o]=1e7-1;--s[o],s[i]+=1e7}s[i]-=d[i]}for(;0===s[--u];)s.pop();for(;0===s[0];s.shift())--n;return s[0]?(t.d=s,t.e=n,l?S(t,p):t):new h(0)}function P(e,t,r){var n,i=x(e),o=m(e.d),a=o.length;return t?(r&&(n=r-a)>0?o=o.charAt(0)+"."+o.slice(1)+M(n):a>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+M(-i-1)+o,r&&(n=r-a)>0&&(o+=M(n))):i>=a?(o+=M(i+1-a),r&&(n=r-i-1)>0&&(o=o+"."+M(n))):((n=i+1)<a&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-a)>0&&(i+1===a&&(o+="."),o+=M(n))),e.s<0?"-"+o:o}function E(e,t){if(e.length>t)return e.length=t,!0}function k(e){if(!e||"object"!=typeof e)throw Error(u+"Object expected");var t,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]]))if(f(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(s+r+": "+n);if(void 0!==(n=e[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(s+r+": "+n);return this}(a=function e(t){var r,n,i;function o(e){if(!(this instanceof o))return new o(e);if(this.constructor=o,e instanceof o){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(s+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return j(this,e.toString())}if("string"!=typeof e)throw Error(s+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,h.test(e))j(this,e);else throw Error(s+e)}if(o.prototype=y,o.ROUND_UP=0,o.ROUND_DOWN=1,o.ROUND_CEIL=2,o.ROUND_FLOOR=3,o.ROUND_HALF_UP=4,o.ROUND_HALF_DOWN=5,o.ROUND_HALF_EVEN=6,o.ROUND_HALF_CEIL=7,o.ROUND_HALF_FLOOR=8,o.clone=e,o.config=o.set=k,void 0===t&&(t={}),t)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return o.config(t),o}(a)).default=a.Decimal=a,o=new a(1),void 0===(n=(function(){return a}).call(t,r,t,e))||(e.exports=n)}(0)},30125:(e,t,r)=>{"use strict";var n=r(12115),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=n.useState,a=n.useEffect,l=n.useLayoutEffect,u=n.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=o({inst:{value:r,getSnapshot:t}}),i=n[0].inst,c=n[1];return l(function(){i.value=r,i.getSnapshot=t,s(i)&&c({inst:i})},[e,r,t]),a(function(){return s(i)&&c({inst:i}),e(function(){s(i)&&c({inst:i})})},[e]),u(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:c},30808:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(94202),i=r(52451),o=r(44078),a=r(25723);t.has=function(e,t){let r;if(0===(r=Array.isArray(t)?t:"string"==typeof t&&n.isDeepKey(t)&&e?.[t]==null?a.toPath(t):[t]).length)return!1;let l=e;for(let e=0;e<r.length;e++){let t=r[e];if((null==l||!Object.hasOwn(l,t))&&!((Array.isArray(l)||o.isArguments(l))&&i.isIndex(t)&&t<l.length))return!1;l=l[t]}return!0}},31474:(e,t,r)=>{"use strict";r.d(t,{N:()=>u});var n=r(49580),i=r(51023),o=r(29439);function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var u=(e,t,r,a,u,s,c)=>{if(null!=t&&null!=s){var{chartData:f,computedData:d,dataStartIndex:h,dataEndIndex:p}=r;return e.reduce((e,r)=>{var y,g,v,m,b,{dataDefinedOnItem:w,settings:x}=r,O=(y=w,g=f,null!=y?y:g),M=Array.isArray(O)?(0,o.v)(O,h,p):O,A=null!=(v=null==x?void 0:x.dataKey)?v:null==a?void 0:a.dataKey,j=null==x?void 0:x.nameKey;return Array.isArray(m=null!=a&&a.dataKey&&Array.isArray(M)&&!Array.isArray(M[0])&&"axis"===c?(0,n.eP)(M,a.dataKey,u):s(M,t,d,j))?m.forEach(t=>{var r=l(l({},x),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push((0,i.GF)({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:(0,i.kr)(t.payload,t.dataKey),name:t.name}))}):e.push((0,i.GF)({tooltipEntrySettings:x,dataKey:A,payload:m,value:(0,i.kr)(m,A),name:null!=(b=(0,i.kr)(m,j))?b:null==x?void 0:x.name})),e},[])}}},31730:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(13183),i=r(36704),o=r(79835),a=r(57048);t.uniqBy=function(e,t=i.identity){return o.isArrayLikeObject(e)?n.uniqBy(Array.from(e),a.iteratee(t)):[]}},32333:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},32403:(e,t,r)=>{"use strict";r.d(t,{x:()=>n});var n=e=>e.options.tooltipPayloadSearcher},33308:(e,t,r)=>{"use strict";r.d(t,{dl:()=>u,lJ:()=>l,uN:()=>o});var n=r(26286),i=r(49580);function o(e,t){if(t){var r=Number.parseInt(t,10);if(!(0,i.M8)(r))return null==e?void 0:e[r]}}var a=(0,n.Z0)({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),l=a.reducer,{createEventEmitter:u}=a.actions},33325:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(74453),i=r(4602);t.isEqual=function(e,t){return n.isEqualWith(e,t,i.noop)}},33383:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(35555);t.cloneDeep=function(e){return n.cloneDeepWith(e)}},33455:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return o}});let n=r(28140)._(r(12115)),i=r(20821),o=n.default.createContext(i.imageConfigDefault)},33692:(e,t,r)=>{"use strict";r.d(t,{m:()=>n});var n={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout)}},34010:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}r.d(t,{IZ:()=>a,Kg:()=>o,lY:()=>l,yy:()=>u}),r(12115);var o=Math.PI/180,a=(e,t,r,n)=>({x:e+Math.cos(-o*n)*r,y:t+Math.sin(-o*n)*r}),l=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},u=(e,t)=>{var r,{x:n,y:o}=e,{radius:a,angle:l}=((e,t)=>{var{x:r,y:n}=e,{cx:i,cy:o}=t,a=((e,t)=>{var{x:r,y:n}=e,{x:i,y:o}=t;return Math.sqrt((r-i)**2+(n-o)**2)})({x:r,y:n},{x:i,y:o});if(a<=0)return{radius:a,angle:0};var l=Math.acos((r-i)/a);return n>o&&(l=2*Math.PI-l),{radius:a,angle:180*l/Math.PI,angleInRadian:l}})({x:n,y:o},t),{innerRadius:u,outerRadius:s}=t;if(a<u||a>s||0===a)return null;var{startAngle:c,endAngle:f}=(e=>{var{startAngle:t,endAngle:r}=e,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}})(t),d=l;if(c<=f){for(;d>f;)d-=360;for(;d<c;)d+=360;r=d>=c&&d<=f}else{for(;d>c;)d-=360;for(;d<f;)d+=360;r=d>=f&&d<=c}return r?i(i({},t),{},{radius:a,angle:((e,t)=>{var{startAngle:r,endAngle:n}=t;return e+360*Math.min(Math.floor(r/360),Math.floor(n/360))})(d,t)}):null}},34140:(e,t,r)=>{"use strict";r.d(t,{J:()=>d});var n=r(12115);function i(){}var o=r(85224),a=r(29158),l=r(99180),u=r(47139),s={begin:0,duration:1e3,easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}},c={t:0},f={t:1};function d(e){var t=(0,o.e)(e,s),{isActive:r,canBegin:d,duration:h,easing:p,begin:y,onAnimationEnd:g,onAnimationStart:v,children:m}=t,b=(0,u.L)("JavascriptAnimate",t.animationManager),[w,x]=(0,n.useState)(r?c:f),O=(0,n.useRef)(null);return(0,n.useEffect)(()=>{r||x(f)},[r]),(0,n.useEffect)(()=>{if(!r||!d)return i;var e=(0,a.A)(c,f,(0,l.yl)(p),h,x,b.getTimeoutController());return b.start([v,y,()=>{O.current=e()},h,g]),()=>{b.stop(),O.current&&O.current(),g()}},[r,d,h,p,y,v,g,b]),m(w.t)}},34264:(e,t,r)=>{"use strict";r.d(t,{F0:()=>n,tQ:()=>o,um:()=>i});var n="data-recharts-item-index",i="data-recharts-item-data-key",o=60},34565:(e,t,r)=>{"use strict";function n(e){return null==e?void 0:e.id}r.d(t,{x:()=>n})},35053:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(23134);t.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},35067:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toKey=function(e){return"string"==typeof e||"symbol"==typeof e?e:Object.is(e?.valueOf?.(),-0)?"-0":String(e)}},35163:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.flatten=function(e,t=1){let r=[],n=Math.floor(t),i=(e,t)=>{for(let o=0;o<e.length;o++){let a=e[o];Array.isArray(a)&&t<n?i(a,t+1):r.push(a)}};return i(e,0),r}},35555:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(12974),i=r(9220);t.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,(r,o,a,l)=>{let u=t?.(r,o,a,l);if(void 0!==u)return u;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case i.numberTag:case i.stringTag:case i.booleanTag:{let t=new e.constructor(e?.valueOf());return n.copyProperties(t,e),t}case i.argumentsTag:{let t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}})}},35704:(e,t,r)=>{"use strict";r.d(t,{r:()=>o});var n=r(12115),i=(0,n.createContext)(null),o=()=>null!=(0,n.useContext)(i)},35865:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}},35923:(e,t,r)=>{"use strict";function n(e){return null!=e.stackId&&null!=e.dataKey}r.d(t,{g:()=>n})},36704:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.identity=function(e){return e}},37047:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},37205:(e,t,r)=>{"use strict";r.d(t,{e:()=>p,k:()=>y});var n=r(26286),i=r(83507),o=r(98496),a=r(94868),l=r(73406),u=r(34264),s=r(76069),c=r(32403),f=r(25543),d=(0,s.Mz)([f.J],e=>e.tooltipItemPayloads),h=(0,s.Mz)([d,c.x,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var i=e.find(e=>e.settings.dataKey===n);if(null!=i){var{positions:o}=i;if(null!=o)return t(o,r)}}),p=(0,n.VP)("touchMove"),y=(0,n.Nc)();y.startListening({actionCreator:p,effect:(e,t)=>{var r=e.payload,n=t.getState(),s=(0,l.au)(n,n.tooltip.settings.shared);if("axis"===s){var c=(0,o.g)(n,(0,a.w)({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==c?void 0:c.activeIndex)!=null&&t.dispatch((0,i.Nt)({activeIndex:c.activeIndex,activeDataKey:void 0,activeCoordinate:c.activeCoordinate}))}else if("item"===s){var f,d=r.touches[0],p=document.elementFromPoint(d.clientX,d.clientY);if(!p||!p.getAttribute)return;var y=p.getAttribute(u.F0),g=null!=(f=p.getAttribute(u.um))?f:void 0,v=h(t.getState(),y,g);t.dispatch((0,i.RD)({activeDataKey:g,activeIndex:y,activeCoordinate:v}))}}})},37849:(e,t,r)=>{"use strict";e.exports=r(68381)},38116:(e,t,r)=>{"use strict";r.d(t,{B_:()=>i,JK:()=>o,Vp:()=>u,gX:()=>a,hF:()=>l});var n=(0,r(26286).Z0)({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:i,setLayout:o,setChartSize:a,setScale:l}=n.actions,u=n.reducer},38522:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},38528:(e,t,r)=>{e.exports=r(60512).range},38881:(e,t,r)=>{"use strict";r.d(t,{s:()=>l});var n=r(12115),i=r(35704),o=r(38116),a=r(81024);function l(e){var{layout:t,width:r,height:l,margin:u}=e,s=(0,a.j)(),c=(0,i.r)();return(0,n.useEffect)(()=>{c||(s((0,o.JK)(t)),s((0,o.gX)({width:r,height:l})),s((0,o.B_)(u)))},[s,c,t,r,l,u]),null}},39346:(e,t,r)=>{"use strict";r.d(t,{E:()=>_});var n=r(12115),i=r(2821),o=r(49580),a=r(33692),l=r(70543),u=r(64680),s=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,c=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,f=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,d=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,h={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},p=Object.keys(h);class y{static parse(e){var t,[,r,n]=null!=(t=d.exec(e))?t:[];return new y(parseFloat(r),null!=n?n:"")}add(e){return this.unit!==e.unit?new y(NaN,""):new y(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new y(NaN,""):new y(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new y(NaN,""):new y(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new y(NaN,""):new y(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return(0,o.M8)(this.num)}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,(0,o.M8)(e)&&(this.unit=""),""===t||f.test(t)||(this.num=NaN,this.unit=""),p.includes(t)&&(this.num=e*h[t],this.unit="px")}}function g(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,o]=null!=(r=s.exec(t))?r:[],a=y.parse(null!=n?n:""),l=y.parse(null!=o?o:""),u="*"===i?a.multiply(l):a.divide(l);if(u.isNaN())return"NaN";t=t.replace(s,u.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var f,[,d,h,p]=null!=(f=c.exec(t))?f:[],g=y.parse(null!=d?d:""),v=y.parse(null!=p?p:""),m="+"===h?g.add(v):g.subtract(v);if(m.isNaN())return"NaN";t=t.replace(c,m.toString())}return t}var v=/\(([^()]*)\)/;function m(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t,r=e;null!=(t=v.exec(r));){var[,n]=t;r=r.replace(v,g(n))}return r}(t),t=g(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var b=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],w=["dx","dy","angle","className","breakAll"];function x(){return(x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var M=/[ \f\n\r\t\v\u2028\u2029]+/,A=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];(0,o.uy)(t)||(i=r?t.toString().split(""):t.toString().split(M));var a=i.map(e=>({word:e,width:(0,u.Pu)(e,n).width})),l=r?0:(0,u.Pu)("\xa0",n).width;return{wordsWithComputedWidth:a,spaceWidth:l}}catch(e){return null}},j=e=>[{words:(0,o.uy)(e)?[]:e.toString().split(M)}],S="#808080",_=(0,n.forwardRef)((e,t)=>{var r,{x:u=0,y:s=0,lineHeight:c="1em",capHeight:f="0.71em",scaleToFit:d=!1,textAnchor:h="start",verticalAnchor:p="end",fill:y=S}=e,g=O(e,b),v=(0,n.useMemo)(()=>(e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:l,maxLines:u}=e;if((t||r)&&!a.m.isSsr){var s=A({breakAll:l,children:n,style:i});if(!s)return j(n);var{wordsWithComputedWidth:c,spaceWidth:f}=s;return((e,t,r,n,i)=>{var a,{maxLines:l,children:u,style:s,breakAll:c}=e,f=(0,o.Et)(l),d=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce((e,t)=>{var{word:o,width:a}=t,l=e[e.length-1];return l&&(null==n||i||l.width+a+r<Number(n))?(l.words.push(o),l.width+=a+r):e.push({words:[o],width:a}),e},[])},h=d(t),p=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!f||i||!(h.length>l||p(h).width>Number(n)))return h;for(var y=e=>{var t=d(A({breakAll:c,style:s,children:u.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>l||p(t).width>Number(n),t]},g=0,v=u.length-1,m=0;g<=v&&m<=u.length-1;){var b=Math.floor((g+v)/2),[w,x]=y(b-1),[O]=y(b);if(w||O||(g=b+1),w&&O&&(v=b-1),!w&&O){a=x;break}m++}return a||h})({breakAll:l,children:n,maxLines:u,style:i},c,f,t,r)}return j(n)})({breakAll:g.breakAll,children:g.children,maxLines:g.maxLines,scaleToFit:d,style:g.style,width:g.width}),[g.breakAll,g.children,g.maxLines,d,g.style,g.width]),{dx:M,dy:_,angle:P,className:E,breakAll:k}=g,T=O(g,w);if(!(0,o.vh)(u)||!(0,o.vh)(s))return null;var C=u+((0,o.Et)(M)?M:0),N=s+((0,o.Et)(_)?_:0);switch(p){case"start":r=m("calc(".concat(f,")"));break;case"middle":r=m("calc(".concat((v.length-1)/2," * -").concat(c," + (").concat(f," / 2))"));break;default:r=m("calc(".concat(v.length-1," * -").concat(c,")"))}var D=[];if(d){var z=v[0].width,{width:I}=g;D.push("scale(".concat((0,o.Et)(I)?I/z:1,")"))}return P&&D.push("rotate(".concat(P,", ").concat(C,", ").concat(N,")")),D.length&&(T.transform=D.join(" ")),n.createElement("text",x({},(0,l.J9)(T,!0),{ref:t,x:C,y:N,className:(0,i.$)("recharts-text",E),textAnchor:h,fill:y.includes("url")?S:y}),v.map((e,t)=>{var i=e.words.join(k?"":" ");return n.createElement("tspan",{x:C,dy:0===t?r:c,key:"".concat(i,"-").concat(t)},i)}))});_.displayName="Text"},40495:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(27243);t.isMatch=function(e,t){return n.isMatchWith(e,t,()=>void 0)}},42915:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(40495),i=r(75190);t.matches=function(e){return e=i.cloneDeep(e),t=>n.isMatch(t,e)}},43597:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}r.d(t,{dl:()=>o,mP:()=>a,s8:()=>l});var o=(e,t,r)=>e.map(e=>"".concat(e.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase()))," ").concat(t,"ms ").concat(r)).join(","),a=(e,t)=>[Object.keys(e),Object.keys(t)].reduce((e,t)=>e.filter(e=>t.includes(e))),l=(e,t)=>Object.keys(t).reduce((r,n)=>i(i({},r),{},{[n]:e(n,t[n])}),{})},44078:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(32333);t.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===n.getTag(e)}},47139:(e,t,r)=>{"use strict";r.d(t,{L:()=>a});var n=r(12115);class i{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=o=>{o-r>=t?e(o):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var o=(0,n.createContext)(function(){var e,t,r,n,o;return e=new i,t=()=>null,r=!1,n=null,o=i=>{if(!r){if(Array.isArray(i)){if(!i.length)return;var[a,...l]=i;if("number"==typeof a){n=e.setTimeout(o.bind(null,l),a);return}o(a),n=e.setTimeout(o.bind(null,l));return}"string"==typeof i&&t(i),"object"==typeof i&&t(i),"function"==typeof i&&i()}},{stop:()=>{r=!0},start:e=>{r=!1,n&&(n(),n=null),o(e)},subscribe:e=>(t=e,()=>{t=()=>null}),getTimeoutController:()=>e}});function a(e,t){var r=(0,n.useContext)(o);return(0,n.useMemo)(()=>null!=t?t:r(e),[e,t,r])}},47226:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(40495),i=r(35067),o=r(33383),a=r(93276),l=r(30808);t.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(e?.valueOf(),-0)&&(e="-0");break;case"number":e=i.toKey(e)}return t=o.cloneDeep(t),function(r){let i=a.get(r,e);return void 0===i?l.has(r,e):void 0===t?void 0===i:n.isMatch(i,t)}}},48971:(e,t,r)=>{"use strict";r.d(t,{x:()=>s});var n,i=r(12115),o=r.t(i,2),a=r(49580),l=null!=(n=o["useId".toString()])?n:()=>{var[e]=i.useState(()=>(0,a.NF)("uid-"));return e},u=(0,i.createContext)(void 0),s=e=>{var{id:t,type:r,children:n}=e,o=function(e,t){var r=l();return t||(e?"".concat(e,"-").concat(r):r)}("recharts-".concat(r),t);return i.createElement(u.Provider,{value:o},n(o))}},49074:(e,t,r)=>{"use strict";r.d(t,{x:()=>a,y:()=>o});var n=r(26286),i=r(72481),o=(0,n.VP)("externalEvent"),a=(0,n.Nc)();a.startListening({actionCreator:o,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:(0,i.eE)(r),activeDataKey:(0,i.Xb)(r),activeIndex:(0,i.A2)(r),activeLabel:(0,i.BZ)(r),activeTooltipIndex:(0,i.A2)(r),isTooltipActive:(0,i.yn)(r)};e.payload.handler(n,e.payload.reactEvent)}}})},49484:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(23134),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,o=/^\w*$/;t.isKey=function(e,t){return!Array.isArray(e)&&(!!("number"==typeof e||"boolean"==typeof e||null==e||n.isSymbol(e))||"string"==typeof e&&(o.test(e)||!i.test(e))||null!=t&&Object.hasOwn(t,e))}},49507:(e,t,r)=>{"use strict";r.d(t,{W:()=>o,h:()=>i});var n=r(76069),i=(0,n.Mz)(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),o=(0,n.Mz)(e=>e.cartesianAxis.yAxis,e=>Object.values(e))},49580:(e,t,r)=>{"use strict";r.d(t,{CG:()=>h,Dj:()=>p,Et:()=>u,F4:()=>d,GW:()=>y,M8:()=>a,NF:()=>f,Zb:()=>m,_3:()=>l,eP:()=>g,sA:()=>o,uy:()=>v,vh:()=>s});var n=r(54241),i=r.n(n),o=e=>0===e?0:e>0?1:-1,a=e=>"number"==typeof e&&e!=+e,l=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,u=e=>("number"==typeof e||e instanceof Number)&&!a(e),s=e=>u(e)||"string"==typeof e,c=0,f=e=>{var t=++c;return"".concat(e||"").concat(t)},d=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!u(e)&&"string"!=typeof e)return n;if(l(e)){if(null==t)return n;var o=e.indexOf("%");r=t*parseFloat(e.slice(0,o))/100}else r=+e;return a(r)&&(r=n),i&&null!=t&&r>t&&(r=t),r},h=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++)if(r[e[n]])return!0;else r[e[n]]=!0;return!1},p=(e,t)=>u(e)&&u(t)?r=>e+r*(t-e):()=>t;function y(e,t,r){return u(e)&&u(t)?e+r*(t-e):t}function g(e,t,r){if(e&&e.length)return e.find(e=>e&&("function"==typeof t?t(e):i()(e,t))===r)}var v=e=>null==e,m=e=>v(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1))},49629:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var n=(0,r(12115).createContext)(null)},49887:(e,t,r)=>{"use strict";r.d(t,{y:()=>K});var n=r(12115),i=r(28086),o=r.n(i),a=r(2821),l=r(70543),u=r(85224),s=r(68281),c=r.n(s),f=r(99180),d=r(29158),h=r(43597),p=r(47139),y=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function g(){return(g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach(function(t){b(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function b(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class w extends n.PureComponent{componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:o,from:a}=this.props,{style:l}=this.state;if(r){if(!t){this.state&&l&&(n&&l[n]!==o||!n&&l!==o)&&this.setState({style:n?{[n]:o}:o});return}if(!c()(e.to,o)||!e.canBegin||!e.isActive){var u=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=u||i?a:e.to;this.state&&l&&(n&&l[n]!==s||!n&&l!==s)&&this.setState({style:n?{[n]:s}:s}),this.runAnimation(m(m({},this.props),{},{from:s,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var{from:t,to:r,duration:n,easing:i,begin:o,onAnimationEnd:a,onAnimationStart:l}=e,u=(0,d.A)(t,r,(0,f.yl)(i),n,this.changeStyle,this.manager.getTimeoutController()),s=()=>{this.stopJSAnimation=u()};this.manager.start([l,o,s,n,a])}runAnimation(e){var{begin:t,duration:r,attributeName:n,to:i,easing:o,onAnimationStart:a,onAnimationEnd:l,children:u}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof o||"function"==typeof u||"spring"===o)return void this.runJSAnimation(e);var s=n?{[n]:i}:i,c=(0,h.dl)(Object.keys(s),r,o);this.manager.start([a,t,m(m({},s),{},{transition:c}),r,l])}render(){var e=this.props,{children:t,begin:r,duration:i,attributeName:o,easing:a,isActive:l,from:u,to:s,canBegin:c,onAnimationEnd:f,shouldReAnimate:d,onAnimationReStart:h,animationManager:p}=e,g=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,y),v=n.Children.count(t),b=this.state.style;if("function"==typeof t)return t(b);if(!l||0===v||i<=0)return t;var w=e=>{var{style:t={},className:r}=e.props;return(0,n.cloneElement)(e,m(m({},g),{},{style:m(m({},t),b),className:r}))};return 1===v?w(n.Children.only(t)):n.createElement("div",null,n.Children.map(t,e=>w(e)))}constructor(e,t){super(e,t),b(this,"mounted",!1),b(this,"manager",void 0),b(this,"stopJSAnimation",null),b(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:o,children:a,duration:l,animationManager:u}=this.props;if(this.manager=u,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof a&&(this.state={style:o});return}if(i){if("function"==typeof a){this.state={style:i};return}this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}}function x(e){var t,r=(0,p.L)(null!=(t=e.attributeName)?t:Object.keys(e.to).join(","),e.animationManager);return n.createElement(w,g({},e,{animationManager:r}))}function O(){return(O=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}b(w,"displayName","Animate"),b(w,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var M=(e,t,r,n,i)=>{var o,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,u=r>=0?1:-1,s=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&i instanceof Array){for(var c=[0,0,0,0],f=0;f<4;f++)c[f]=i[f]>a?a:i[f];o="M".concat(e,",").concat(t+l*c[0]),c[0]>0&&(o+="A ".concat(c[0],",").concat(c[0],",0,0,").concat(s,",").concat(e+u*c[0],",").concat(t)),o+="L ".concat(e+r-u*c[1],",").concat(t),c[1]>0&&(o+="A ".concat(c[1],",").concat(c[1],",0,0,").concat(s,",\n        ").concat(e+r,",").concat(t+l*c[1])),o+="L ".concat(e+r,",").concat(t+n-l*c[2]),c[2]>0&&(o+="A ".concat(c[2],",").concat(c[2],",0,0,").concat(s,",\n        ").concat(e+r-u*c[2],",").concat(t+n)),o+="L ".concat(e+u*c[3],",").concat(t+n),c[3]>0&&(o+="A ".concat(c[3],",").concat(c[3],",0,0,").concat(s,",\n        ").concat(e,",").concat(t+n-l*c[3])),o+="Z"}else if(a>0&&i===+i&&i>0){var d=Math.min(a,i);o="M ".concat(e,",").concat(t+l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+u*d,",").concat(t,"\n            L ").concat(e+r-u*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+r,",").concat(t+l*d,"\n            L ").concat(e+r,",").concat(t+n-l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+r-u*d,",").concat(t+n,"\n            L ").concat(e+u*d,",").concat(t+n,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e,",").concat(t+n-l*d," Z")}else o="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return o},A={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},j=e=>{var t=(0,u.e)(e,A),r=(0,n.useRef)(null),[i,o]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&o(e)}catch(e){}},[]);var{x:s,y:c,width:f,height:d,radius:h,className:p}=t,{animationEasing:y,animationDuration:g,animationBegin:v,isAnimationActive:m,isUpdateAnimationActive:b}=t;if(s!==+s||c!==+c||f!==+f||d!==+d||0===f||0===d)return null;var w=(0,a.$)("recharts-rectangle",p);return b?n.createElement(x,{canBegin:i>0,from:{width:f,height:d,x:s,y:c},to:{width:f,height:d,x:s,y:c},duration:g,animationEasing:y,isActive:b},e=>{var{width:o,height:a,x:u,y:s}=e;return n.createElement(x,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:g,isActive:m,easing:y},n.createElement("path",O({},(0,l.J9)(t,!0),{className:w,d:M(u,s,o,a,h),ref:r})))}):n.createElement("path",O({},(0,l.J9)(t,!0),{className:w,d:M(s,c,f,d,h)}))};function S(){return(S=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var _=(e,t,r,n,i)=>{var o=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-o/2,",").concat(t+i)+"L ".concat(e+r-o/2-n,",").concat(t+i)+"L ".concat(e,",").concat(t," Z")},P={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},E=e=>{var t=(0,u.e)(e,P),r=(0,n.useRef)(),[i,o]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&o(e)}catch(e){}},[]);var{x:s,y:c,upperWidth:f,lowerWidth:d,height:h,className:p}=t,{animationEasing:y,animationDuration:g,animationBegin:v,isUpdateAnimationActive:m}=t;if(s!==+s||c!==+c||f!==+f||d!==+d||h!==+h||0===f&&0===d||0===h)return null;var b=(0,a.$)("recharts-trapezoid",p);return m?n.createElement(x,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:h,x:s,y:c},to:{upperWidth:f,lowerWidth:d,height:h,x:s,y:c},duration:g,animationEasing:y,isActive:m},e=>{var{upperWidth:o,lowerWidth:a,height:u,x:s,y:c}=e;return n.createElement(x,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:g,easing:y},n.createElement("path",S({},(0,l.J9)(t,!0),{className:b,d:_(s,c,o,a,u),ref:r})))}):n.createElement("g",null,n.createElement("path",S({},(0,l.J9)(t,!0),{className:b,d:_(s,c,f,d,h)})))},k=r(34010),T=r(49580);function C(){return(C=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var N=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:o,isExternal:a,cornerRadius:l,cornerIsExternal:u}=e,s=l*(a?1:-1)+n,c=Math.asin(l/s)/k.Kg,f=u?i:i+o*c,d=(0,k.IZ)(t,r,s,f);return{center:d,circleTangency:(0,k.IZ)(t,r,n,f),lineTangency:(0,k.IZ)(t,r,s*Math.cos(c*k.Kg),u?i-o*c:i),theta:c}},D=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:o,endAngle:a}=e,l=((e,t)=>(0,T.sA)(t-e)*Math.min(Math.abs(t-e),359.999))(o,a),u=o+l,s=(0,k.IZ)(t,r,i,o),c=(0,k.IZ)(t,r,i,u),f="M ".concat(s.x,",").concat(s.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(o>u),",\n    ").concat(c.x,",").concat(c.y,"\n  ");if(n>0){var d=(0,k.IZ)(t,r,n,o),h=(0,k.IZ)(t,r,n,u);f+="L ".concat(h.x,",").concat(h.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(o<=u),",\n            ").concat(d.x,",").concat(d.y," Z")}else f+="L ".concat(t,",").concat(r," Z");return f},z={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},I=e=>{var t,r=(0,u.e)(e,z),{cx:i,cy:o,innerRadius:s,outerRadius:c,cornerRadius:f,forceCornerRadius:d,cornerIsExternal:h,startAngle:p,endAngle:y,className:g}=r;if(c<s||p===y)return null;var v=(0,a.$)("recharts-sector",g),m=c-s,b=(0,T.F4)(f,m,0,!0);return t=b>0&&360>Math.abs(p-y)?(e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:o,forceCornerRadius:a,cornerIsExternal:l,startAngle:u,endAngle:s}=e,c=(0,T.sA)(s-u),{circleTangency:f,lineTangency:d,theta:h}=N({cx:t,cy:r,radius:i,angle:u,sign:c,cornerRadius:o,cornerIsExternal:l}),{circleTangency:p,lineTangency:y,theta:g}=N({cx:t,cy:r,radius:i,angle:s,sign:-c,cornerRadius:o,cornerIsExternal:l}),v=l?Math.abs(u-s):Math.abs(u-s)-h-g;if(v<0)return a?"M ".concat(d.x,",").concat(d.y,"\n        a").concat(o,",").concat(o,",0,0,1,").concat(2*o,",0\n        a").concat(o,",").concat(o,",0,0,1,").concat(-(2*o),",0\n      "):D({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:u,endAngle:s});var m="M ".concat(d.x,",").concat(d.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(c<0),",").concat(f.x,",").concat(f.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(v>180),",").concat(+(c<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(c<0),",").concat(y.x,",").concat(y.y,"\n  ");if(n>0){var{circleTangency:b,lineTangency:w,theta:x}=N({cx:t,cy:r,radius:n,angle:u,sign:c,isExternal:!0,cornerRadius:o,cornerIsExternal:l}),{circleTangency:O,lineTangency:M,theta:A}=N({cx:t,cy:r,radius:n,angle:s,sign:-c,isExternal:!0,cornerRadius:o,cornerIsExternal:l}),j=l?Math.abs(u-s):Math.abs(u-s)-x-A;if(j<0&&0===o)return"".concat(m,"L").concat(t,",").concat(r,"Z");m+="L".concat(M.x,",").concat(M.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(c<0),",").concat(O.x,",").concat(O.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(j>180),",").concat(+(c>0),",").concat(b.x,",").concat(b.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(c<0),",").concat(w.x,",").concat(w.y,"Z")}else m+="L".concat(t,",").concat(r,"Z");return m})({cx:i,cy:o,innerRadius:s,outerRadius:c,cornerRadius:Math.min(b,m/2),forceCornerRadius:d,cornerIsExternal:h,startAngle:p,endAngle:y}):D({cx:i,cy:o,innerRadius:s,outerRadius:c,startAngle:p,endAngle:y}),n.createElement("path",C({},(0,l.J9)(r,!0),{className:v,d:t}))},R=r(87095),L=r(92143),U=["option","shapeType","propTransformer","activeClassName","isActive"];function B(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function $(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?B(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):B(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function F(e,t){return $($({},t),e)}function H(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return n.createElement(j,r);case"trapezoid":return n.createElement(E,r);case"sector":return n.createElement(I,r);case"symbols":if("symbols"===t)return n.createElement(L.i,r);break;default:return null}}function K(e){var t,{option:r,shapeType:i,propTransformer:a=F,activeClassName:l="recharts-active-shape",isActive:u}=e,s=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,U);if((0,n.isValidElement)(r))t=(0,n.cloneElement)(r,$($({},s),(0,n.isValidElement)(r)?r.props:r));else if("function"==typeof r)t=r(s);else if(o()(r)&&"boolean"!=typeof r){var c=a(r,s);t=n.createElement(H,{shapeType:i,elementProps:c})}else t=n.createElement(H,{shapeType:i,elementProps:s});return u?n.createElement(R.W,{className:l},t):t}},50257:(e,t,r)=>{"use strict";r.d(t,{C:()=>l,U:()=>u});var n=r(76069),i=r(8291),o=r(27588),a=r(49580),l=e=>e.brush,u=(0,n.Mz)([l,i.HZ,o.HK],(e,t,r)=>({height:e.height,x:(0,a.Et)(e.x)?e.x:t.left,y:(0,a.Et)(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:(0,a.Et)(e.width)?e.width:t.width}))},51023:(e,t,r)=>{"use strict";r.d(t,{qx:()=>D,IH:()=>N,s0:()=>w,gH:()=>b,SW:()=>B,YB:()=>A,bk:()=>U,Hj:()=>z,DW:()=>T,y2:()=>k,nb:()=>E,PW:()=>O,Mk:()=>C,$8:()=>P,yy:()=>_,Rh:()=>M,GF:()=>I,uM:()=>R,kr:()=>m,r4:()=>L,_L:()=>x,_f:()=>j});var n=r(97354),i=r.n(n),o=r(54241),a=r.n(o);function l(e,t){if((i=e.length)>1)for(var r,n,i,o=1,a=e[t[0]],l=a.length;o<i;++o)for(n=a,a=e[t[o]],r=0;r<l;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}var u=r(27012),s=r(73595);function c(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function f(e,t){return e[t]}function d(e){let t=[];return t.key=e,t}var h=r(49580),p=r(34010),y=r(29439);function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function m(e,t,r){return(0,h.uy)(e)||(0,h.uy)(t)?r:(0,h.vh)(t)?a()(e,t,r):"function"==typeof t?t(e):r}var b=(e,t,r,n,i)=>{var o,a=-1,l=null!=(o=null==t?void 0:t.length)?o:0;if(l<=1||null==e)return 0;if("angleAxis"===n&&null!=i&&1e-6>=Math.abs(Math.abs(i[1]-i[0])-360))for(var u=0;u<l;u++){var s=u>0?r[u-1].coordinate:r[l-1].coordinate,c=r[u].coordinate,f=u>=l-1?r[0].coordinate:r[u+1].coordinate,d=void 0;if((0,h.sA)(c-s)!==(0,h.sA)(f-c)){var p=[];if((0,h.sA)(f-c)===(0,h.sA)(i[1]-i[0])){d=f;var y=c+i[1]-i[0];p[0]=Math.min(y,(y+s)/2),p[1]=Math.max(y,(y+s)/2)}else{d=s;var g=f+i[1]-i[0];p[0]=Math.min(c,(g+c)/2),p[1]=Math.max(c,(g+c)/2)}var v=[Math.min(c,(d+c)/2),Math.max(c,(d+c)/2)];if(e>v[0]&&e<=v[1]||e>=p[0]&&e<=p[1]){({index:a}=r[u]);break}}else{var m=Math.min(s,f),b=Math.max(s,f);if(e>(m+c)/2&&e<=(b+c)/2){({index:a}=r[u]);break}}}else if(t){for(var w=0;w<l;w++)if(0===w&&e<=(t[w].coordinate+t[w+1].coordinate)/2||w>0&&w<l-1&&e>(t[w].coordinate+t[w-1].coordinate)/2&&e<=(t[w].coordinate+t[w+1].coordinate)/2||w===l-1&&e>(t[w].coordinate+t[w-1].coordinate)/2){({index:a}=t[w]);break}}return a},w=(e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:o,verticalAlign:a,layout:l}=t;if(("vertical"===l||"horizontal"===l&&"middle"===a)&&"center"!==o&&(0,h.Et)(e[o]))return v(v({},e),{},{[o]:e[o]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===o)&&"middle"!==a&&(0,h.Et)(e[a]))return v(v({},e),{},{[a]:e[a]+(i||0)})}return e},x=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,O=(e,t,r,n)=>{if(n)return e.map(e=>e.coordinate);var i,o,a=e.map(e=>(e.coordinate===t&&(i=!0),e.coordinate===r&&(o=!0),e.coordinate));return i||a.push(t),o||a.push(r),a},M=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:i,range:o,scale:a,realScaleType:l,isCategorical:u,categoricalDomain:s,tickCount:c,ticks:f,niceTicks:d,axisType:p}=e;if(!a)return null;var y="scaleBand"===l&&a.bandwidth?a.bandwidth()/2:2,g=(t||r)&&"category"===i&&a.bandwidth?a.bandwidth()/y:0;return(g="angleAxis"===p&&o&&o.length>=2?2*(0,h.sA)(o[0]-o[1])*g:g,t&&(f||d))?(f||d||[]).map((e,t)=>({coordinate:a(n?n.indexOf(e):e)+g,value:e,offset:g,index:t})).filter(e=>!(0,h.M8)(e.coordinate)):u&&s?s.map((e,t)=>({coordinate:a(e)+g,value:e,index:t,offset:g})):a.ticks&&!r&&null!=c?a.ticks(c).map((e,t)=>({coordinate:a(e)+g,value:e,offset:g,index:t})):a.domain().map((e,t)=>({coordinate:a(e)+g,value:n?n[e]:e,index:t,offset:g}))},A=e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-1e-4,o=Math.max(n[0],n[1])+1e-4,a=e(t[0]),l=e(t[r-1]);(a<i||a>o||l<i||l>o)&&e.domain([t[0],t[r-1]])}},j=(e,t)=>{if(!t||2!==t.length||!(0,h.Et)(t[0])||!(0,h.Et)(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),i=[e[0],e[1]];return(!(0,h.Et)(e[0])||e[0]<r)&&(i[0]=r),(!(0,h.Et)(e[1])||e[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},S={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,o=0,a=0;a<t;++a){var l=(0,h.M8)(e[a][r][1])?e[a][r][0]:e[a][r][1];l>=0?(e[a][r][0]=i,e[a][r][1]=i+l,i=e[a][r][1]):(e[a][r][0]=o,e[a][r][1]=o+l,o=e[a][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,o=0,a=e[0].length;o<a;++o){for(i=r=0;r<n;++r)i+=e[r][o][1]||0;if(i)for(r=0;r<n;++r)e[r][o][1]/=i}l(e,t)}},none:l,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],o=i.length;n<o;++n){for(var a=0,u=0;a<r;++a)u+=e[a][n][1]||0;i[n][1]+=i[n][0]=-u/2}l(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,o=0,a=1;a<n;++a){for(var u=0,s=0,c=0;u<i;++u){for(var f=e[t[u]],d=f[a][1]||0,h=(d-(f[a-1][1]||0))/2,p=0;p<u;++p){var y=e[t[p]];h+=(y[a][1]||0)-(y[a-1][1]||0)}s+=d,c+=h*d}r[a-1][1]+=r[a-1][0]=o,s&&(o-=c/s)}r[a-1][1]+=r[a-1][0]=o,l(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,o=0;o<t;++o){var a=(0,h.M8)(e[o][r][1])?e[o][r][0]:e[o][r][1];a>=0?(e[o][r][0]=i,e[o][r][1]=i+a,i=e[o][r][1]):(e[o][r][0]=0,e[o][r][1]=0)}}},_=(e,t,r)=>{var n=S[r];return(function(){var e=(0,s.A)([]),t=c,r=l,n=f;function i(i){var o,a,l=Array.from(e.apply(this,arguments),d),s=l.length,c=-1;for(let e of i)for(o=0,++c;o<s;++o)(l[o][c]=[0,+n(e,l[o].key,c,i)]).data=e;for(o=0,a=(0,u.A)(t(l));o<s;++o)l[a[o]].index=o;return r(l,a),l}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:(0,s.A)(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"==typeof e?e:(0,s.A)(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?c:"function"==typeof e?e:(0,s.A)(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?l:e,i):r},i})().keys(t).value((e,t)=>+m(e,t,0)).order(c).offset(n)(e)};function P(e){return null==e?void 0:String(e)}function E(e){var{axis:t,ticks:r,bandSize:n,entry:i,index:o,dataKey:a}=e;if("category"===t.type){if(!t.allowDuplicatedCategory&&t.dataKey&&!(0,h.uy)(i[t.dataKey])){var l=(0,h.eP)(r,"value",i[t.dataKey]);if(l)return l.coordinate+n/2}return r[o]?r[o].coordinate+n/2:null}var u=m(i,(0,h.uy)(a)?t.dataKey:a);return(0,h.uy)(u)?null:t.scale(u)}var k=e=>{var{axis:t,ticks:r,offset:n,bandSize:i,entry:o,index:a}=e;if("category"===t.type)return r[a]?r[a].coordinate+n:null;var l=m(o,t.dataKey,t.scale.domain()[a]);return(0,h.uy)(l)?null:t.scale(l)-i/2+n},T=e=>{var{numericAxis:t}=e,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},C=(e,t,r)=>{if(null!=e)return(e=>[e[0]===1/0?0:e[0],e[1]===-1/0?0:e[1]])(Object.keys(e).reduce((n,i)=>{var{stackedData:o}=e[i],a=o.reduce((e,n)=>{var i=(e=>{var t=e.flat(2).filter(h.Et);return[Math.min(...t),Math.max(...t)]})((0,y.v)(n,t,r));return[Math.min(e[0],i[0]),Math.max(e[1],i[1])]},[1/0,-1/0]);return[Math.min(a[0],n[0]),Math.max(a[1],n[1])]},[1/0,-1/0]))},N=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,D=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,z=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var o=i()(t,e=>e.coordinate),a=1/0,l=1,u=o.length;l<u;l++){var s=o[l],c=o[l-1];a=Math.min((s.coordinate||0)-(c.coordinate||0),a)}return a===1/0?0:a}return r?void 0:0};function I(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:o}=e;return v(v({},t),{},{dataKey:r,payload:n,value:i,name:o})}function R(e,t){return e?String(e):"string"==typeof t?t:void 0}function L(e,t,r,n,i){return"horizontal"===r||"vertical"===r?e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height?{x:e,y:t}:null:n?(0,p.yy)({x:e,y:t},n):null}var U=(e,t,r,n)=>{var i=t.find(e=>e&&e.index===r);if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var o=i.coordinate,{radius:a}=n;return v(v(v({},n),(0,p.IZ)(n.cx,n.cy,a,o)),{},{angle:o,radius:a})}var l=i.coordinate,{angle:u}=n;return v(v(v({},n),(0,p.IZ)(n.cx,n.cy,l,u)),{},{angle:u,radius:l})}return{x:0,y:0}},B=(e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius},52089:(e,t,r)=>{"use strict";r.d(t,{q:()=>i});var n=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"];function i(e){return"string"==typeof e&&n.includes(e)}},52451:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let r=/^(?:0|[1-9]\d*)$/;t.isIndex=function(e,t=Number.MAX_SAFE_INTEGER){switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return r.test(e)}}},54241:(e,t,r)=>{e.exports=r(93276).get},54652:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return u},getImageProps:function(){return l}});let n=r(28140),i=r(75040),o=r(81356),a=n._(r(71124));function l(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let u=o.Image},57048:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(36704),i=r(27761),o=r(42915),a=r(47226);t.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":if(Array.isArray(e)&&2===e.length)return a.matchesProperty(e[0],e[1]);return o.matches(e);case"string":case"symbol":case"number":return i.property(e)}}},57333:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(12474);t.throttle=function(e,t=0,r={}){let{leading:i=!0,trailing:o=!0}=r;return n.debounce(e,t,{leading:i,maxWait:t,trailing:o})}},57828:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},58672:(e,t,r)=>{"use strict";r.d(t,{R:()=>n});var n=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]}},59799:(e,t,r)=>{"use strict";r.d(t,{aX:()=>k,pg:()=>P,r1:()=>A});var n=r(76069),i=r(97354),o=r.n(i),a=r(51023),l=r(90135),u=r(72481),s=r(90167),c=r(8291),f=r(27588),d=r(85339),h=r(82601),p=r(21838),y=r(23125),g=r(74525),v=r(32403),m=r(25543),b=r(31474),w=r(84811),x=(e,t)=>t,O=(e,t,r)=>r,M=(e,t,r,n)=>n,A=(0,n.Mz)(u.R4,e=>o()(e,e=>e.coordinate)),j=(0,n.Mz)([m.J,x,O,M],h.i),S=(0,n.Mz)([j,u.n4],p.P),_=(0,n.Mz)([m.J,x,O,M],g.q),P=(0,n.Mz)([f.Lp,f.A$,s.fz,c.HZ,u.R4,M,_,v.x],y.o);(0,n.Mz)([j,P],(e,t)=>{var r;return null!=(r=e.coordinate)?r:t});var E=(0,n.Mz)(u.R4,S,d.E);(0,n.Mz)([_,S,l.LF,w.D,E,v.x,x],b.N),(0,n.Mz)([j],e=>({isActive:e.active,activeIndex:e.index}));var k=(e,t,r,n,i,o,l,u)=>{if(e&&t&&n&&i&&o){var s=(0,a.r4)(e.chartX,e.chartY,t,r,u);if(s){var c=(0,a.SW)(s,t),f=(0,a.gH)(c,l,o,n,i),d=(0,a.bk)(t,o,f,s);return{activeIndex:String(f),activeCoordinate:d}}}}},60013:(e,t,r)=>{"use strict";r.d(t,{Qx:()=>s,a6:()=>c,h4:()=>q,jM:()=>K,ss:()=>F});var n,i=Symbol.for("immer-nothing"),o=Symbol.for("immer-draftable"),a=Symbol.for("immer-state");function l(e){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var u=Object.getPrototypeOf;function s(e){return!!e&&!!e[a]}function c(e){return!!e&&(d(e)||Array.isArray(e)||!!e[o]||!!e.constructor?.[o]||v(e)||m(e))}var f=Object.prototype.constructor.toString();function d(e){if(!e||"object"!=typeof e)return!1;let t=u(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===f}function h(e,t){0===p(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function p(e){let t=e[a];return t?t.type_:Array.isArray(e)?1:v(e)?2:3*!!m(e)}function y(e,t){return 2===p(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function g(e,t,r){let n=p(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function v(e){return e instanceof Map}function m(e){return e instanceof Set}function b(e){return e.copy_||e.base_}function w(e,t){if(v(e))return new Map(e);if(m(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=d(e);if(!0!==t&&("class_only"!==t||r)){let t=u(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[a];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){let i=r[n],o=t[i];!1===o.writable&&(o.writable=!0,o.configurable=!0),(o.get||o.set)&&(t[i]={configurable:!0,writable:!0,enumerable:o.enumerable,value:e[i]})}return Object.create(u(e),t)}}function x(e,t=!1){return M(e)||s(e)||!c(e)||(p(e)>1&&(e.set=e.add=e.clear=e.delete=O),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>x(t,!0))),e}function O(){l(2)}function M(e){return Object.isFrozen(e)}var A={};function j(e){let t=A[e];return t||l(0,e),t}function S(e,t){t&&(j("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function _(e){P(e),e.drafts_.forEach(k),e.drafts_=null}function P(e){e===n&&(n=e.parent_)}function E(e){return n={drafts_:[],parent_:n,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function k(e){let t=e[a];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function T(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[a].modified_&&(_(t),l(4)),c(e)&&(e=C(t,e),t.parent_||D(t,e)),t.patches_&&j("Patches").generateReplacementPatches_(r[a].base_,e,t.patches_,t.inversePatches_)):e=C(t,r,[]),_(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==i?e:void 0}function C(e,t,r){if(M(t))return t;let n=t[a];if(!n)return h(t,(i,o)=>N(e,n,t,i,o,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return D(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let t=n.copy_,i=t,o=!1;3===n.type_&&(i=new Set(t),t.clear(),o=!0),h(i,(i,a)=>N(e,n,t,i,a,r,o)),D(e,t,!1),r&&e.patches_&&j("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function N(e,t,r,n,i,o,a){if(s(i)){let a=C(e,i,o&&t&&3!==t.type_&&!y(t.assigned_,n)?o.concat(n):void 0);if(g(r,n,a),!s(a))return;e.canAutoFreeze_=!1}else a&&r.add(i);if(c(i)&&!M(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;C(e,i),(!t||!t.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&D(e,i)}}function D(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&x(t,r)}var z={get(e,t){if(t===a)return e;let r=b(e);if(!y(r,t)){var n=e,i=r,o=t;let a=L(i,o);return a?"value"in a?a.value:a.get?.call(n.draft_):void 0}let l=r[t];return e.finalized_||!c(l)?l:l===R(e.base_,t)?(B(e),e.copy_[t]=$(l,e)):l},has:(e,t)=>t in b(e),ownKeys:e=>Reflect.ownKeys(b(e)),set(e,t,r){let n=L(b(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){let n=R(b(e),t),i=n?.[a];if(i&&i.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||y(e.base_,t)))return!0;B(e),U(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==R(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,B(e),U(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=b(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){l(11)},getPrototypeOf:e=>u(e.base_),setPrototypeOf(){l(12)}},I={};function R(e,t){let r=e[a];return(r?b(r):e)[t]}function L(e,t){if(!(t in e))return;let r=u(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=u(r)}}function U(e){!e.modified_&&(e.modified_=!0,e.parent_&&U(e.parent_))}function B(e){e.copy_||(e.copy_=w(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function $(e,t){let r=v(e)?j("MapSet").proxyMap_(e,t):m(e)?j("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),i={type_:+!!r,scope_:t?t.scope_:n,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},o=i,a=z;r&&(o=[i],a=I);let{revoke:l,proxy:u}=Proxy.revocable(o,a);return i.draft_=u,i.revoke_=l,u}(e,t);return(t?t.scope_:n).drafts_.push(r),r}function F(e){return s(e)||l(10,e),function e(t){let r;if(!c(t)||M(t))return t;let n=t[a];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=w(t,n.scope_.immer_.useStrictShallowCopy_)}else r=w(t,!0);return h(r,(t,n)=>{g(r,t,e(n))}),n&&(n.finalized_=!1),r}(e)}h(z,(e,t)=>{I[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),I.deleteProperty=function(e,t){return I.set.call(this,e,t,void 0)},I.set=function(e,t,r){return z.set.call(this,e[0],t,r,e[0])};var H=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let n;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let n=this;return function(e=r,...i){return n.produce(e,e=>t.call(this,e,...i))}}if("function"!=typeof t&&l(6),void 0!==r&&"function"!=typeof r&&l(7),c(e)){let i=E(this),o=$(e,void 0),a=!0;try{n=t(o),a=!1}finally{a?_(i):P(i)}return S(i,r),T(n,i)}if(e&&"object"==typeof e)l(1,e);else{if(void 0===(n=t(e))&&(n=e),n===i&&(n=void 0),this.autoFreeze_&&x(n,!0),r){let t=[],i=[];j("Patches").generateReplacementPatches_(e,n,t,i),r(t,i)}return n}},this.produceWithPatches=(e,t)=>{let r,n;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){c(e)||l(8),s(e)&&(e=F(e));let t=E(this),r=$(e,void 0);return r[a].isManual_=!0,P(t),r}finishDraft(e,t){let r=e&&e[a];r&&r.isManual_||l(9);let{scope_:n}=r;return S(n,t),T(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));let n=j("Patches").applyPatches_;return s(e)?n(e,t):this.produce(e,e=>n(e,t))}},K=H.produce;function q(e){return e}H.produceWithPatches.bind(H),H.setAutoFreeze.bind(H),H.setUseStrictShallowCopy.bind(H),H.applyPatches.bind(H),H.createDraft.bind(H),H.finishDraft.bind(H)},60512:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(96288),i=r(68795);t.range=function(e,t,r){r&&"number"!=typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=i.toFinite(e),void 0===t?(t=e,e=0):t=i.toFinite(t),r=void 0===r?e<t?1:-1:i.toFinite(r);let o=Math.max(Math.ceil((t-e)/(r||1)),0),a=Array(o);for(let t=0;t<o;t++)a[t]=e,e+=r;return a}},60861:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},64680:(e,t,r)=>{"use strict";r.d(t,{Pu:()=>f});var n=r(33692);class i{get(e){var t=this.cache.get(e);return void 0!==t&&(this.cache.delete(e),this.cache.set(e,t)),t}set(e,t){if(this.cache.has(e))this.cache.delete(e);else if(this.cache.size>=this.maxSize){var r=this.cache.keys().next().value;this.cache.delete(r)}this.cache.set(e,t)}clear(){this.cache.clear()}size(){return this.cache.size}constructor(e){!function(e,t,r){var n;(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(this,"cache",new Map),this.maxSize=e}}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var a=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},{cacheSize:2e3,enableCache:!0}),l=new i(a.cacheSize),u={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},s="recharts_measurement_span",c=(e,t)=>{try{var r=document.getElementById(s);r||((r=document.createElement("span")).setAttribute("id",s),r.setAttribute("aria-hidden","true"),document.body.appendChild(r)),Object.assign(r.style,u,t),r.textContent="".concat(e);var n=r.getBoundingClientRect();return{width:n.width,height:n.height}}catch(e){return{width:0,height:0}}},f=function(e){var t,r,i,o,u,s,f=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||n.m.isSsr)return{width:0,height:0};if(!a.enableCache)return c(e,f);var d=(t=f.fontSize||"",r=f.fontFamily||"",i=f.fontWeight||"",o=f.fontStyle||"",u=f.letterSpacing||"",s=f.textTransform||"","".concat(e,"|").concat(t,"|").concat(r,"|").concat(i,"|").concat(o,"|").concat(u,"|").concat(s)),h=l.get(d);if(h)return h;var p=c(e,f);return l.set(d,p),p}},64940:(e,t,r)=>{"use strict";r.d(t,{M:()=>o,t:()=>i});var n=r(12115),i=(0,n.createContext)(null),o=()=>(0,n.useContext)(i)},68281:(e,t,r)=>{e.exports=r(33325).isEqual},68381:(e,t)=>{"use strict";var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,i=r?Symbol.for("react.portal"):60106,o=r?Symbol.for("react.fragment"):60107,a=r?Symbol.for("react.strict_mode"):60108,l=r?Symbol.for("react.profiler"):60114,u=r?Symbol.for("react.provider"):60109,s=r?Symbol.for("react.context"):60110,c=r?Symbol.for("react.async_mode"):60111,f=r?Symbol.for("react.concurrent_mode"):60111,d=r?Symbol.for("react.forward_ref"):60112,h=r?Symbol.for("react.suspense"):60113,p=(r&&Symbol.for("react.suspense_list"),r?Symbol.for("react.memo"):60115),y=r?Symbol.for("react.lazy"):60116;function g(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case c:case f:case o:case l:case a:case h:return e;default:switch(e=e&&e.$$typeof){case s:case d:case y:case p:case u:return e;default:return t}}case i:return t}}}r&&Symbol.for("react.block"),r&&Symbol.for("react.fundamental"),r&&Symbol.for("react.responder"),r&&Symbol.for("react.scope");t.isFragment=function(e){return g(e)===o}},68570:(e,t,r)=>{"use strict";r.d(t,{M:()=>n});var n=e=>e.tooltip.settings.axisId},68795:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(35053);t.toFinite=function(e){return e?(e=n.toNumber(e))===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e==e?e:0:0===e?e:0}},68997:(e,t,r)=>{"use strict";r.d(t,{A:()=>s,_:()=>c});var n=r(12115),i=r(35704),o=r(90167),a=r(81024),l=r(69277),u=()=>{};function s(e){var{legendPayload:t}=e,r=(0,a.j)(),o=(0,i.r)();return(0,n.useEffect)(()=>o?u:(r((0,l.Lx)(t)),()=>{r((0,l.u3)(t))}),[r,o,t]),null}function c(e){var{legendPayload:t}=e,r=(0,a.j)(),i=(0,a.G)(o.fz);return(0,n.useEffect)(()=>"centric"!==i&&"radial"!==i?u:(r((0,l.Lx)(t)),()=>{r((0,l.u3)(t))}),[r,i,t]),null}},69277:(e,t,r)=>{"use strict";r.d(t,{CU:()=>c,Lx:()=>u,h1:()=>l,hx:()=>a,u3:()=>s});var n=r(26286),i=r(60013),o=(0,n.Z0)({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push((0,i.h4)(t.payload))},removeLegendPayload(e,t){var r=(0,i.ss)(e).payload.indexOf((0,i.h4)(t.payload));r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:a,setLegendSettings:l,addLegendPayload:u,removeLegendPayload:s}=o.actions,c=o.reducer},69386:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});var n=e=>null;n.displayName="Cell"},69905:(e,t,r)=>{"use strict";r.d(t,{u:()=>u});var n=r(12115),i=r(2821),o=r(70543),a=["children","width","height","viewBox","className","style","title","desc"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var u=(0,n.forwardRef)((e,t)=>{var{children:r,width:u,height:s,viewBox:c,className:f,style:d,title:h,desc:p}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,a),g=c||{width:u,height:s,x:0,y:0},v=(0,i.$)("recharts-surface",f);return n.createElement("svg",l({},(0,o.J9)(y,!0,"svg"),{className:v,width:u,height:s,style:d,viewBox:"".concat(g.x," ").concat(g.y," ").concat(g.width," ").concat(g.height),ref:t}),n.createElement("title",null,h),n.createElement("desc",null,p),r)})},70543:(e,t,r)=>{"use strict";r.d(t,{J9:()=>v,aS:()=>y,y$:()=>g});var n=r(54241),i=r.n(n),o=r(12115),a=r(37849),l=r(49580),u=r(84072),s=r(52089),c=r(4264),f=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",d=null,h=null,p=e=>{if(e===d&&Array.isArray(h))return h;var t=[];return o.Children.forEach(e,e=>{(0,l.uy)(e)||((0,a.isFragment)(e)?t=t.concat(p(e.props.children)):t.push(e))}),h=t,d=e,t};function y(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(e=>f(e)):[f(t)],p(e).forEach(e=>{var t=i()(e,"type.displayName")||i()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}var g=e=>!e||"object"!=typeof e||!("clipDot"in e)||!!e.clipDot,v=(e,t,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,o.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach(e=>{var o;((e,t,r,n)=>{if("symbol"==typeof t||"number"==typeof t)return!0;var i,o=null!=(i=n&&(null===u.VU||void 0===u.VU?void 0:u.VU[n]))?i:[],a=t.startsWith("data-"),l="function"!=typeof e&&(!!n&&o.includes(t)||(0,c.R)(t)),f=!!r&&(0,s.q)(t);return a||l||f})(null==(o=n)?void 0:o[e],e,t,r)&&(i[e]=n[e])}),i}},70806:(e,t,r)=>{"use strict";r.d(t,{EI:()=>f,oM:()=>c});var n=r(81024),i=r(72481),o=r(76069),a=r(8291),l=(0,o.Mz)([a.HZ],e=>{if(e)return{top:e.top,bottom:e.bottom,left:e.left,right:e.right}}),u=r(27588),s=(0,o.Mz)([l,u.Lp,u.A$],(e,t,r)=>{if(e&&null!=t&&null!=r)return{x:e.left,y:e.top,width:Math.max(0,t-e.left-e.right),height:Math.max(0,r-e.top-e.bottom)}}),c=()=>(0,n.G)(s),f=()=>(0,n.G)(i.JG)},70846:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObjectLike=function(e){return"object"==typeof e&&null!==e}},71124:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:n,width:i,quality:o}=e,a=o||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(n)+"&w="+i+"&q="+a+(n.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),r.__next_img_default=!0;let n=r},71669:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if("object"!=typeof e||null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){let t=e[Symbol.toStringTag];return null!=t&&!!Object.getOwnPropertyDescriptor(e,Symbol.toStringTag)?.writable&&e.toString()===`[object ${t}]`}let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},71847:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(12115);let i=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:i=24,strokeWidth:l=2,absoluteStrokeWidth:u,className:s="",children:c,iconNode:f,...d}=e;return(0,n.createElement)("svg",{ref:t,...a,width:i,height:i,stroke:r,strokeWidth:u?24*Number(l)/Number(i):l,className:o("lucide",s),...!c&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(d)&&{"aria-hidden":"true"},...d},[...f.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),u=(e,t)=>{let r=(0,n.forwardRef)((r,a)=>{let{className:u,...s}=r;return(0,n.createElement)(l,{ref:a,iconNode:t,className:o("lucide-".concat(i(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),u),...s})});return r.displayName=i(e),r}},72259:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var n=(e,t)=>t},72481:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>ec,eE:()=>ep,Xb:()=>ef,JG:()=>ev,A2:()=>es,yn:()=>ey,gL:()=>ee,R4:()=>ei,n4:()=>N});var n=r(76069),i=r(1444),o=r(90167),a=r(51023),l=r(90135),u=r(15195),s=r(49580),c=r(15145),f=r(73406),d=r(85339),h=r(82601),p=r(21838),y=r(23125),g=r(27588),v=r(8291),m=r(74525),b=r(32403),w=r(25543),x=r(31474),O=r(68570),M=r(95603),A=r(84811),j=r(24021),S=r(35923),_=(0,n.Mz)([A.D,o.fz,i.um,u.iO,M.R],i.sr),P=(0,n.Mz)([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),E=(0,n.Mz)([M.R,O.M],i.eo),k=(0,n.Mz)([P,A.D,E],i.ec),T=(0,n.Mz)([k],e=>e.filter(S.g)),C=(0,n.Mz)([k],i.rj),N=(0,n.Mz)([C,l.LF],i.Nk),D=(0,n.Mz)([T,l.LF,A.D],j.A),z=(0,n.Mz)([N,A.D,k],i.fb),I=(0,n.Mz)([A.D],i.S5),R=(0,n.Mz)([k],e=>e.filter(S.g)),L=(0,n.Mz)([D,R,u.eC],i.MK),U=(0,n.Mz)([L,l.LF,M.R],i.pM),B=(0,n.Mz)([k],i.IO),$=(0,n.Mz)([N,A.D,B,i.CH,M.R],i.kz),F=(0,n.Mz)([i.Kr,M.R,O.M],i.P9),H=(0,n.Mz)([F,M.R],i.Oz),K=(0,n.Mz)([i.gT,M.R,O.M],i.P9),q=(0,n.Mz)([K,M.R],i.q),W=(0,n.Mz)([i.$X,M.R,O.M],i.P9),Z=(0,n.Mz)([W,M.R],i.bb),G=(0,n.Mz)([H,Z,q],i.yi),Y=(0,n.Mz)([A.D,I,U,$,G,o.fz,M.R],i.wL),V=(0,n.Mz)([A.D,o.fz,N,z,u.eC,M.R,Y],i.tP),X=(0,n.Mz)([V,A.D,_],i.xp),J=(0,n.Mz)([A.D,V,X,M.R],i.g1),Q=e=>{var t=(0,M.R)(e),r=(0,O.M)(e);return(0,i.D5)(e,t,r,!1)},ee=(0,n.Mz)([A.D,Q],c.I),et=(0,n.Mz)([A.D,_,J,ee],i.Qn),er=(0,n.Mz)([o.fz,z,A.D,M.R],i.tF),en=(0,n.Mz)([o.fz,z,A.D,M.R],i.iv),ei=(0,n.Mz)([o.fz,A.D,_,et,Q,er,en,M.R],(e,t,r,n,i,o,l,u)=>{if(t){var{type:c}=t,f=(0,a._L)(e,u);if(n){var d="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,h="category"===c&&n.bandwidth?n.bandwidth()/d:0;return(h="angleAxis"===u&&null!=i&&(null==i?void 0:i.length)>=2?2*(0,s.sA)(i[0]-i[1])*h:h,f&&l)?l.map((e,t)=>({coordinate:n(e)+h,value:e,index:t,offset:h})):n.domain().map((e,t)=>({coordinate:n(e)+h,value:o?o[e]:e,index:t,offset:h}))}}}),eo=(0,n.Mz)([f.xH,f.Hw,e=>e.tooltip.settings],(e,t,r)=>(0,f.$g)(r.shared,e,t)),ea=e=>e.tooltip.settings.trigger,el=e=>e.tooltip.settings.defaultIndex,eu=(0,n.Mz)([w.J,eo,ea,el],h.i),es=(0,n.Mz)([eu,N],p.P),ec=(0,n.Mz)([ei,es],d.E),ef=(0,n.Mz)([eu],e=>{if(e)return e.dataKey}),ed=(0,n.Mz)([w.J,eo,ea,el],m.q),eh=(0,n.Mz)([g.Lp,g.A$,o.fz,v.HZ,ei,el,ed,b.x],y.o),ep=(0,n.Mz)([eu,eh],(e,t)=>null!=e&&e.coordinate?e.coordinate:t),ey=(0,n.Mz)([eu],e=>e.active),eg=(0,n.Mz)([ed,es,l.LF,A.D,ec,b.x,eo],x.N),ev=(0,n.Mz)([eg],e=>{if(null!=e)return Array.from(new Set(e.map(e=>e.payload).filter(e=>null!=e)))})},72743:(e,t,r)=>{"use strict";r.d(t,{L:()=>F});var n=r(12115),i=r(90167),o=r(81024),a=r(35704),l=r(69905),u=r(50257),s=r(92377),c=["children"];function f(){return(f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var d={width:"100%",height:"100%"},h=(0,n.forwardRef)((e,t)=>{var r,a,u=(0,i.yi)(),c=(0,i.rY)(),h=(0,o.G)(e=>e.rootProps.accessibilityLayer);if(!(0,s.F)(u)||!(0,s.F)(c))return null;var{children:p,otherAttributes:y,title:g,desc:v}=e;return r="number"==typeof y.tabIndex?y.tabIndex:h?0:void 0,a="string"==typeof y.role?y.role:h?"application":void 0,n.createElement(l.u,f({},y,{title:g,desc:v,role:a,tabIndex:r,width:u,height:c,style:d,ref:t}),p)}),p=e=>{var{children:t}=e,r=(0,o.G)(u.U);if(!r)return null;var{width:i,height:a,y:s,x:c}=r;return n.createElement(l.u,{width:i,height:a,x:c,y:s},t)},y=(0,n.forwardRef)((e,t)=>{var{children:r}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,c);return(0,a.r)()?n.createElement(p,null,r):n.createElement(h,f({ref:t},i),r)}),g=r(2821),v=r(83507),m=r(75754),b=r(15195),w=new(r(94820)),x="recharts.syncEvent.tooltip",O="recharts.syncEvent.brush",M=r(33308),A=r(72481),j=r(94078),S=()=>{},_=r(85080),P=r(27588),E=r(38116),k=r(49074),T=r(37205),C=(0,n.createContext)(null),N=r(64940);function D(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var z=(0,n.forwardRef)((e,t)=>{var{children:r,className:a,height:l,onClick:u,onContextMenu:c,onDoubleClick:f,onMouseDown:d,onMouseEnter:h,onMouseLeave:p,onMouseMove:y,onMouseUp:z,onTouchEnd:I,onTouchMove:R,onTouchStart:L,style:U,width:B}=e,$=(0,o.j)(),[F,H]=(0,n.useState)(null),[K,q]=(0,n.useState)(null);!function(){var e,t,r,a,l,u,s,c,f,d,h,p=(0,o.j)();(0,n.useEffect)(()=>{p((0,M.dl)())},[p]),e=(0,o.G)(b.lZ),t=(0,o.G)(b.pH),r=(0,o.j)(),a=(0,o.G)(b.hX),l=(0,o.G)(A.R4),u=(0,i.WX)(),s=(0,i.sk)(),c=(0,o.G)(e=>e.rootProps.className),(0,n.useEffect)(()=>{if(null==e)return S;var n=(n,i,o)=>{if(t!==o&&e===n){if("index"===a)return void r(i);if(null!=l){if("function"==typeof a){var c,f=a(l,{activeTooltipIndex:null==i.payload.index?void 0:Number(i.payload.index),isTooltipActive:i.payload.active,activeIndex:null==i.payload.index?void 0:Number(i.payload.index),activeLabel:i.payload.label,activeDataKey:i.payload.dataKey,activeCoordinate:i.payload.coordinate});c=l[f]}else"value"===a&&(c=l.find(e=>String(e.value)===i.payload.label));var{coordinate:d}=i.payload;if(null==c||!1===i.payload.active||null==d||null==s)return void r((0,v.E1)({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}));var{x:h,y:p}=d,y=Math.min(h,s.x+s.width),g=Math.min(p,s.y+s.height),m={x:"horizontal"===u?c.coordinate:y,y:"horizontal"===u?g:c.coordinate};r((0,v.E1)({active:i.payload.active,coordinate:m,dataKey:i.payload.dataKey,index:String(c.index),label:i.payload.label}))}}};return w.on(x,n),()=>{w.off(x,n)}},[c,r,t,e,a,l,u,s]),f=(0,o.G)(b.lZ),d=(0,o.G)(b.pH),h=(0,o.j)(),(0,n.useEffect)(()=>{if(null==f)return S;var e=(e,t,r)=>{d!==r&&f===e&&h((0,j.M)(t))};return w.on(O,e),()=>{w.off(O,e)}},[h,d,f])}();var W=function(){var e=(0,o.j)(),[t,r]=(0,n.useState)(null),i=(0,o.G)(P.et);return(0,n.useEffect)(()=>{if(null!=t){var r=t.getBoundingClientRect().width/t.offsetWidth;(0,s.H)(r)&&r!==i&&e((0,E.hF)(r))}},[t,e,i]),r}(),Z=(0,n.useCallback)(e=>{W(e),"function"==typeof t&&t(e),H(e),q(e)},[W,t,H,q]),G=(0,n.useCallback)(e=>{$((0,m.ky)(e)),$((0,k.y)({handler:u,reactEvent:e}))},[$,u]),Y=(0,n.useCallback)(e=>{$((0,m.dj)(e)),$((0,k.y)({handler:h,reactEvent:e}))},[$,h]),V=(0,n.useCallback)(e=>{$((0,v.xS)()),$((0,k.y)({handler:p,reactEvent:e}))},[$,p]),X=(0,n.useCallback)(e=>{$((0,m.dj)(e)),$((0,k.y)({handler:y,reactEvent:e}))},[$,y]),J=(0,n.useCallback)(()=>{$((0,_.Ru)())},[$]),Q=(0,n.useCallback)(e=>{$((0,_.uZ)(e.key))},[$]),ee=(0,n.useCallback)(e=>{$((0,k.y)({handler:c,reactEvent:e}))},[$,c]),et=(0,n.useCallback)(e=>{$((0,k.y)({handler:f,reactEvent:e}))},[$,f]),er=(0,n.useCallback)(e=>{$((0,k.y)({handler:d,reactEvent:e}))},[$,d]),en=(0,n.useCallback)(e=>{$((0,k.y)({handler:z,reactEvent:e}))},[$,z]),ei=(0,n.useCallback)(e=>{$((0,k.y)({handler:L,reactEvent:e}))},[$,L]),eo=(0,n.useCallback)(e=>{$((0,T.e)(e)),$((0,k.y)({handler:R,reactEvent:e}))},[$,R]),ea=(0,n.useCallback)(e=>{$((0,k.y)({handler:I,reactEvent:e}))},[$,I]);return n.createElement(C.Provider,{value:F},n.createElement(N.t.Provider,{value:K},n.createElement("div",{className:(0,g.$)("recharts-wrapper",a),style:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?D(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):D(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({position:"relative",cursor:"default",width:B,height:l},U),onClick:G,onContextMenu:ee,onDoubleClick:et,onFocus:J,onKeyDown:Q,onMouseDown:er,onMouseEnter:Y,onMouseLeave:V,onMouseMove:X,onMouseUp:en,onTouchEnd:ea,onTouchMove:eo,onTouchStart:ei,ref:Z},r)))}),I=r(49580),R=r(70806),L=(0,n.createContext)(void 0),U=e=>{var{children:t}=e,[r]=(0,n.useState)("".concat((0,I.NF)("recharts"),"-clip")),i=(0,R.oM)();if(null==i)return null;var{x:o,y:a,width:l,height:u}=i;return n.createElement(L.Provider,{value:r},n.createElement("defs",null,n.createElement("clipPath",{id:r},n.createElement("rect",{x:o,y:a,height:u,width:l}))),t)},B=r(4264),$=["children","className","width","height","style","compact","title","desc"],F=(0,n.forwardRef)((e,t)=>{var{children:r,className:i,width:o,height:a,style:l,compact:u,title:s,desc:c}=e,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,$),d=(0,B.u)(f);return u?n.createElement(y,{otherAttributes:d,title:s,desc:c},r):n.createElement(z,{className:i,style:l,width:o,height:a,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},n.createElement(y,{otherAttributes:d,title:s,desc:c,ref:t},n.createElement(U,null,r)))})},73255:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(1322),i=r(49484),o=r(25723);t.orderBy=function(e,t,r,a){if(null==e)return[];r=a?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(e=>String(e));let l=(e,t)=>{let r=e;for(let e=0;e<t.length&&null!=r;++e)r=r[t[e]];return r},u=t.map(e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||i.isKey(e))?e:{key:e,path:o.toPath(e)});return e.map(e=>({original:e,criteria:u.map(t=>{var r,n;return r=t,null==(n=e)||null==r?n:"object"==typeof r&&"key"in r?Object.hasOwn(n,r.key)?n[r.key]:l(n,r.path):"function"==typeof r?r(n):Array.isArray(r)?l(n,r):"object"==typeof n?n[r]:n})})).slice().sort((e,t)=>{for(let i=0;i<u.length;i++){let o=n.compareValues(e.criteria[i],t.criteria[i],r[i]);if(0!==o)return o}return 0}).map(e=>e.original)}},73390:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},73406:(e,t,r)=>{"use strict";r.d(t,{$g:()=>o,Hw:()=>i,au:()=>a,xH:()=>n});var n=e=>e.options.defaultTooltipEventType,i=e=>e.options.validateTooltipEventTypes;function o(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function a(e,t){return o(t,n(e),i(e))}},73595:(e,t,r)=>{"use strict";function n(e){return function(){return e}}r.d(t,{A:()=>n})},74453:(e,t,r)=>{"use strict";var n=r(15376).Buffer;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let i=r(38522),o=r(16940),a=r(32333),l=r(9220),u=r(77159);t.isEqualWith=function(e,t,r){return function e(t,r,s,c,f,d,h){let p=h(t,r,s,c,f,d);if(void 0!==p)return p;if(typeof t==typeof r)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return t===r;case"number":return t===r||Object.is(t,r)}return function t(r,s,c,f){if(Object.is(r,s))return!0;let d=a.getTag(r),h=a.getTag(s);if(d===l.argumentsTag&&(d=l.objectTag),h===l.argumentsTag&&(h=l.objectTag),d!==h)return!1;switch(d){case l.stringTag:return r.toString()===s.toString();case l.numberTag:{let e=r.valueOf(),t=s.valueOf();return u.eq(e,t)}case l.booleanTag:case l.dateTag:case l.symbolTag:return Object.is(r.valueOf(),s.valueOf());case l.regexpTag:return r.source===s.source&&r.flags===s.flags;case l.functionTag:return r===s}let p=(c=c??new Map).get(r),y=c.get(s);if(null!=p&&null!=y)return p===s;c.set(r,s),c.set(s,r);try{switch(d){case l.mapTag:if(r.size!==s.size)return!1;for(let[t,n]of r.entries())if(!s.has(t)||!e(n,s.get(t),t,r,s,c,f))return!1;return!0;case l.setTag:{if(r.size!==s.size)return!1;let t=Array.from(r.values()),n=Array.from(s.values());for(let i=0;i<t.length;i++){let o=t[i],a=n.findIndex(t=>e(o,t,void 0,r,s,c,f));if(-1===a)return!1;n.splice(a,1)}return!0}case l.arrayTag:case l.uint8ArrayTag:case l.uint8ClampedArrayTag:case l.uint16ArrayTag:case l.uint32ArrayTag:case l.bigUint64ArrayTag:case l.int8ArrayTag:case l.int16ArrayTag:case l.int32ArrayTag:case l.bigInt64ArrayTag:case l.float32ArrayTag:case l.float64ArrayTag:if(void 0!==n&&n.isBuffer(r)!==n.isBuffer(s)||r.length!==s.length)return!1;for(let t=0;t<r.length;t++)if(!e(r[t],s[t],t,r,s,c,f))return!1;return!0;case l.arrayBufferTag:if(r.byteLength!==s.byteLength)return!1;return t(new Uint8Array(r),new Uint8Array(s),c,f);case l.dataViewTag:if(r.byteLength!==s.byteLength||r.byteOffset!==s.byteOffset)return!1;return t(new Uint8Array(r),new Uint8Array(s),c,f);case l.errorTag:return r.name===s.name&&r.message===s.message;case l.objectTag:{if(!(t(r.constructor,s.constructor,c,f)||i.isPlainObject(r)&&i.isPlainObject(s)))return!1;let n=[...Object.keys(r),...o.getSymbols(r)],a=[...Object.keys(s),...o.getSymbols(s)];if(n.length!==a.length)return!1;for(let t=0;t<n.length;t++){let i=n[t],o=r[i];if(!Object.hasOwn(s,i))return!1;let a=s[i];if(!e(o,a,i,r,s,c,f))return!1}return!0}default:return!1}}finally{c.delete(r),c.delete(s)}}(t,r,d,h)}(e,t,void 0,void 0,void 0,void 0,r)}},74525:(e,t,r)=>{"use strict";r.d(t,{q:()=>n});var n=(e,t,r,n)=>{var i;return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null==(t=e.settings)?void 0:t.dataKey)===i})}},74797:(e,t,r)=>{"use strict";r.d(t,{TK:()=>l});var n=r(12115),i=r(94078),o=r(81024),a=r(35704),l=e=>{var{chartData:t}=e,r=(0,o.j)(),l=(0,a.r)();return(0,n.useEffect)(()=>l?()=>{}:(r((0,i.hq)(t)),()=>{r((0,i.hq)(void 0))}),[t,r,l]),null}},74841:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return y},defaultHead:function(){return f}});let n=r(28140),i=r(49417),o=r(95155),a=i._(r(12115)),l=n._(r(1262)),u=r(90737),s=r(82073),c=r(60861);function f(e){void 0===e&&(e=!1);let t=[(0,o.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,o.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function d(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(94781);let h=["name","httpEquiv","charSet","itemProp"];function p(e,t){let{inAmpMode:r}=t;return e.reduce(d,[]).reverse().concat(f(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return i=>{let o=!0,a=!1;if(i.key&&"number"!=typeof i.key&&i.key.indexOf("$")>0){a=!0;let t=i.key.slice(i.key.indexOf("$")+1);e.has(t)?o=!1:e.add(t)}switch(i.type){case"title":case"base":t.has(i.type)?o=!1:t.add(i.type);break;case"meta":for(let e=0,t=h.length;e<t;e++){let t=h[e];if(i.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?o=!1:r.add(t);else{let e=i.props[t],r=n[t]||new Set;("name"!==t||!a)&&r.has(e)?o=!1:(r.add(e),n[t]=r)}}}return o}}()).reverse().map((e,t)=>{let r=e.key||t;return a.default.cloneElement(e,{key:r})})}let y=function(e){let{children:t}=e,r=(0,a.useContext)(u.AmpStateContext),n=(0,a.useContext)(s.HeadManagerContext);return(0,o.jsx)(l.default,{reduceComponentsToState:p,headManager:n,inAmpMode:(0,c.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},75040:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return u}}),r(94781);let n=r(14105),i=r(20821),o=["-moz-initial","fill","none","scale-down",void 0];function a(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function u(e,t){var r,u;let s,c,f,{src:d,sizes:h,unoptimized:p=!1,priority:y=!1,loading:g,className:v,quality:m,width:b,height:w,fill:x=!1,style:O,overrideSrc:M,onLoad:A,onLoadingComplete:j,placeholder:S="empty",blurDataURL:_,fetchPriority:P,decoding:E="async",layout:k,objectFit:T,objectPosition:C,lazyBoundary:N,lazyRoot:D,...z}=e,{imgConf:I,showAltText:R,blurComplete:L,defaultLoader:U}=t,B=I||i.imageConfigDefault;if("allSizes"in B)s=B;else{let e=[...B.deviceSizes,...B.imageSizes].sort((e,t)=>e-t),t=B.deviceSizes.sort((e,t)=>e-t),n=null==(r=B.qualities)?void 0:r.sort((e,t)=>e-t);s={...B,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===U)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let $=z.loader||U;delete z.loader,delete z.srcSet;let F="__next_img_default"in $;if(F){if("custom"===s.loader)throw Object.defineProperty(Error('Image with src "'+d+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=$;$=t=>{let{config:r,...n}=t;return e(n)}}if(k){"fill"===k&&(x=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[k];e&&(O={...O,...e});let t={responsive:"100vw",fill:"100vw"}[k];t&&!h&&(h=t)}let H="",K=l(b),q=l(w);if((u=d)&&"object"==typeof u&&(a(u)||void 0!==u.src)){let e=a(d)?d.default:d;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,f=e.blurHeight,_=_||e.blurDataURL,H=e.src,!x)if(K||q){if(K&&!q){let t=K/e.width;q=Math.round(e.height*t)}else if(!K&&q){let t=q/e.height;K=Math.round(e.width*t)}}else K=e.width,q=e.height}let W=!y&&("lazy"===g||void 0===g);(!(d="string"==typeof d?d:H)||d.startsWith("data:")||d.startsWith("blob:"))&&(p=!0,W=!1),s.unoptimized&&(p=!0),F&&!s.dangerouslyAllowSVG&&d.split("?",1)[0].endsWith(".svg")&&(p=!0);let Z=l(m),G=Object.assign(x?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:T,objectPosition:C}:{},R?{}:{color:"transparent"},O),Y=L||"empty"===S?null:"blur"===S?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:K,heightInt:q,blurWidth:c,blurHeight:f,blurDataURL:_||"",objectFit:G.objectFit})+'")':'url("'+S+'")',V=o.includes(G.objectFit)?"fill"===G.objectFit?"100% 100%":"cover":G.objectFit,X=Y?{backgroundSize:V,backgroundPosition:G.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:Y}:{},J=function(e){let{config:t,src:r,unoptimized:n,width:i,quality:o,sizes:a,loader:l}=e;if(n)return{src:r,srcSet:void 0,sizes:void 0};let{widths:u,kind:s}=function(e,t,r){let{deviceSizes:n,allSizes:i}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(r);)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,a),c=u.length-1;return{sizes:a||"w"!==s?a:"100vw",srcSet:u.map((e,n)=>l({config:t,src:r,quality:o,width:e})+" "+("w"===s?e:n+1)+s).join(", "),src:l({config:t,src:r,quality:o,width:u[c]})}}({config:s,src:d,unoptimized:p,width:K,quality:Z,sizes:h,loader:$});return{props:{...z,loading:W?"lazy":g,fetchPriority:P,width:K,height:q,decoding:E,className:v,style:{...G,...X},sizes:J.sizes,srcSet:J.srcSet,src:M||J.src},meta:{unoptimized:p,priority:y,placeholder:S,fill:x}}}},75190:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(12974);t.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},75754:(e,t,r)=>{"use strict";r.d(t,{YF:()=>s,dj:()=>c,fP:()=>f,ky:()=>u});var n=r(26286),i=r(83507),o=r(98496),a=r(73406),l=r(94868),u=(0,n.VP)("mouseClick"),s=(0,n.Nc)();s.startListening({actionCreator:u,effect:(e,t)=>{var r=e.payload,n=(0,o.g)(t.getState(),(0,l.w)(r));(null==n?void 0:n.activeIndex)!=null&&t.dispatch((0,i.jF)({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var c=(0,n.VP)("mouseMove"),f=(0,n.Nc)();f.startListening({actionCreator:c,effect:(e,t)=>{var r=e.payload,n=t.getState(),u=(0,a.au)(n,n.tooltip.settings.shared),s=(0,o.g)(n,(0,l.w)(r));"axis"===u&&((null==s?void 0:s.activeIndex)!=null?t.dispatch((0,i.Nt)({activeIndex:s.activeIndex,activeDataKey:void 0,activeCoordinate:s.activeCoordinate})):t.dispatch((0,i.xS)()))}})},75889:(e,t,r)=>{"use strict";r.d(t,{QP:()=>ee});let n=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],i=t.nextPart.get(r),o=i?n(e.slice(1),i):void 0;if(o)return o;if(0===t.validators.length)return;let a=e.join("-");return t.validators.find(({validator:e})=>e(a))?.classGroupId},i=/^\[(.+)\]$/,o=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:a(t,e)).classGroupId=r;return}if("function"==typeof e)return l(e)?void o(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,i])=>{o(i,a(t,e),r,n)})})},a=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},l=e=>e.isThemeGetter,u=/\s+/;function s(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=c(e))&&(n&&(n+=" "),n+=t);return n}let c=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=c(e[n]))&&(r&&(r+=" "),r+=t);return r},f=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},d=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,h=/^\((?:(\w[\w-]*):)?(.+)\)$/i,p=/^\d+\/\d+$/,y=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,g=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,v=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,m=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,b=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,w=e=>p.test(e),x=e=>!!e&&!Number.isNaN(Number(e)),O=e=>!!e&&Number.isInteger(Number(e)),M=e=>e.endsWith("%")&&x(e.slice(0,-1)),A=e=>y.test(e),j=()=>!0,S=e=>g.test(e)&&!v.test(e),_=()=>!1,P=e=>m.test(e),E=e=>b.test(e),k=e=>!C(e)&&!L(e),T=e=>q(e,Y,_),C=e=>d.test(e),N=e=>q(e,V,S),D=e=>q(e,X,x),z=e=>q(e,Z,_),I=e=>q(e,G,E),R=e=>q(e,Q,P),L=e=>h.test(e),U=e=>W(e,V),B=e=>W(e,J),$=e=>W(e,Z),F=e=>W(e,Y),H=e=>W(e,G),K=e=>W(e,Q,!0),q=(e,t,r)=>{let n=d.exec(e);return!!n&&(n[1]?t(n[1]):r(n[2]))},W=(e,t,r=!1)=>{let n=h.exec(e);return!!n&&(n[1]?t(n[1]):r)},Z=e=>"position"===e||"percentage"===e,G=e=>"image"===e||"url"===e,Y=e=>"length"===e||"size"===e||"bg-size"===e,V=e=>"length"===e,X=e=>"number"===e,J=e=>"family-name"===e,Q=e=>"shadow"===e;Symbol.toStringTag;let ee=function(e,...t){let r,a,l,c=function(u){let s;return a=(r={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,i=(i,o)=>{r.set(i,o),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(i(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):i(e,t)}}})((s=t.reduce((e,t)=>t(e),e())).cacheSize),parseClassName:(e=>{let{prefix:t,experimentalParseClassName:r}=e,n=e=>{let t,r,n=[],i=0,o=0,a=0;for(let r=0;r<e.length;r++){let l=e[r];if(0===i&&0===o){if(":"===l){n.push(e.slice(a,r)),a=r+1;continue}if("/"===l){t=r;continue}}"["===l?i++:"]"===l?i--:"("===l?o++:")"===l&&o--}let l=0===n.length?e:e.substring(a),u=(r=l).endsWith("!")?r.substring(0,r.length-1):r.startsWith("!")?r.substring(1):r;return{modifiers:n,hasImportantModifier:u!==l,baseClassName:u,maybePostfixModifierPosition:t&&t>a?t-a:void 0}};if(t){let e=t+":",r=n;n=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=n;n=t=>r({className:t,parseClassName:e})}return n})(s),sortModifiers:(e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],n=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...n.sort(),e),n=[]):n.push(e)}),r.push(...n.sort()),r}})(s),...(e=>{let t=(e=>{let{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(let e in r)o(r[e],n,e,t);return n})(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:a}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),n(r,t)||(e=>{if(i.test(e)){let t=i.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}})(e)},getConflictingClassGroupIds:(e,t)=>{let n=r[e]||[];return t&&a[e]?[...n,...a[e]]:n}}})(s)}).cache.get,l=r.cache.set,c=f,f(u)};function f(e){let t=a(e);if(t)return t;let n=((e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:i,sortModifiers:o}=t,a=[],l=e.trim().split(u),s="";for(let e=l.length-1;e>=0;e-=1){let t=l[e],{isExternal:u,modifiers:c,hasImportantModifier:f,baseClassName:d,maybePostfixModifierPosition:h}=r(t);if(u){s=t+(s.length>0?" "+s:s);continue}let p=!!h,y=n(p?d.substring(0,h):d);if(!y){if(!p||!(y=n(d))){s=t+(s.length>0?" "+s:s);continue}p=!1}let g=o(c).join(":"),v=f?g+"!":g,m=v+y;if(a.includes(m))continue;a.push(m);let b=i(y,p);for(let e=0;e<b.length;++e){let t=b[e];a.push(v+t)}s=t+(s.length>0?" "+s:s)}return s})(e,r);return l(e,n),n}return function(){return c(s.apply(null,arguments))}}(()=>{let e=f("color"),t=f("font"),r=f("text"),n=f("font-weight"),i=f("tracking"),o=f("leading"),a=f("breakpoint"),l=f("container"),u=f("spacing"),s=f("radius"),c=f("shadow"),d=f("inset-shadow"),h=f("text-shadow"),p=f("drop-shadow"),y=f("blur"),g=f("perspective"),v=f("aspect"),m=f("ease"),b=f("animate"),S=()=>["auto","avoid","all","avoid-page","page","left","right","column"],_=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],P=()=>[..._(),L,C],E=()=>["auto","hidden","clip","visible","scroll"],q=()=>["auto","contain","none"],W=()=>[L,C,u],Z=()=>[w,"full","auto",...W()],G=()=>[O,"none","subgrid",L,C],Y=()=>["auto",{span:["full",O,L,C]},O,L,C],V=()=>[O,"auto",L,C],X=()=>["auto","min","max","fr",L,C],J=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Q=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...W()],et=()=>[w,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...W()],er=()=>[e,L,C],en=()=>[..._(),$,z,{position:[L,C]}],ei=()=>["no-repeat",{repeat:["","x","y","space","round"]}],eo=()=>["auto","cover","contain",F,T,{size:[L,C]}],ea=()=>[M,U,N],el=()=>["","none","full",s,L,C],eu=()=>["",x,U,N],es=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ef=()=>[x,M,$,z],ed=()=>["","none",y,L,C],eh=()=>["none",x,L,C],ep=()=>["none",x,L,C],ey=()=>[x,L,C],eg=()=>[w,"full",...W()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[A],breakpoint:[A],color:[j],container:[A],"drop-shadow":[A],ease:["in","out","in-out"],font:[k],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[A],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[A],shadow:[A],spacing:["px",x],text:[A],"text-shadow":[A],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",w,C,L,v]}],container:["container"],columns:[{columns:[x,C,L,l]}],"break-after":[{"break-after":S()}],"break-before":[{"break-before":S()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:P()}],overflow:[{overflow:E()}],"overflow-x":[{"overflow-x":E()}],"overflow-y":[{"overflow-y":E()}],overscroll:[{overscroll:q()}],"overscroll-x":[{"overscroll-x":q()}],"overscroll-y":[{"overscroll-y":q()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:Z()}],"inset-x":[{"inset-x":Z()}],"inset-y":[{"inset-y":Z()}],start:[{start:Z()}],end:[{end:Z()}],top:[{top:Z()}],right:[{right:Z()}],bottom:[{bottom:Z()}],left:[{left:Z()}],visibility:["visible","invisible","collapse"],z:[{z:[O,"auto",L,C]}],basis:[{basis:[w,"full","auto",l,...W()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[x,w,"auto","initial","none",C]}],grow:[{grow:["",x,L,C]}],shrink:[{shrink:["",x,L,C]}],order:[{order:[O,"first","last","none",L,C]}],"grid-cols":[{"grid-cols":G()}],"col-start-end":[{col:Y()}],"col-start":[{"col-start":V()}],"col-end":[{"col-end":V()}],"grid-rows":[{"grid-rows":G()}],"row-start-end":[{row:Y()}],"row-start":[{"row-start":V()}],"row-end":[{"row-end":V()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":X()}],"auto-rows":[{"auto-rows":X()}],gap:[{gap:W()}],"gap-x":[{"gap-x":W()}],"gap-y":[{"gap-y":W()}],"justify-content":[{justify:[...J(),"normal"]}],"justify-items":[{"justify-items":[...Q(),"normal"]}],"justify-self":[{"justify-self":["auto",...Q()]}],"align-content":[{content:["normal",...J()]}],"align-items":[{items:[...Q(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Q(),{baseline:["","last"]}]}],"place-content":[{"place-content":J()}],"place-items":[{"place-items":[...Q(),"baseline"]}],"place-self":[{"place-self":["auto",...Q()]}],p:[{p:W()}],px:[{px:W()}],py:[{py:W()}],ps:[{ps:W()}],pe:[{pe:W()}],pt:[{pt:W()}],pr:[{pr:W()}],pb:[{pb:W()}],pl:[{pl:W()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":W()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":W()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[l,"screen",...et()]}],"min-w":[{"min-w":[l,"screen","none",...et()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[a]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",r,U,N]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,L,D]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",M,C]}],"font-family":[{font:[B,C,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,L,C]}],"line-clamp":[{"line-clamp":[x,"none",L,D]}],leading:[{leading:[o,...W()]}],"list-image":[{"list-image":["none",L,C]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",L,C]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...es(),"wavy"]}],"text-decoration-thickness":[{decoration:[x,"from-font","auto",L,N]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[x,"auto",L,C]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:W()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",L,C]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",L,C]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:en()}],"bg-repeat":[{bg:ei()}],"bg-size":[{bg:eo()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},O,L,C],radial:["",L,C],conic:[O,L,C]},H,I]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:ea()}],"gradient-via-pos":[{via:ea()}],"gradient-to-pos":[{to:ea()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:el()}],"rounded-s":[{"rounded-s":el()}],"rounded-e":[{"rounded-e":el()}],"rounded-t":[{"rounded-t":el()}],"rounded-r":[{"rounded-r":el()}],"rounded-b":[{"rounded-b":el()}],"rounded-l":[{"rounded-l":el()}],"rounded-ss":[{"rounded-ss":el()}],"rounded-se":[{"rounded-se":el()}],"rounded-ee":[{"rounded-ee":el()}],"rounded-es":[{"rounded-es":el()}],"rounded-tl":[{"rounded-tl":el()}],"rounded-tr":[{"rounded-tr":el()}],"rounded-br":[{"rounded-br":el()}],"rounded-bl":[{"rounded-bl":el()}],"border-w":[{border:eu()}],"border-w-x":[{"border-x":eu()}],"border-w-y":[{"border-y":eu()}],"border-w-s":[{"border-s":eu()}],"border-w-e":[{"border-e":eu()}],"border-w-t":[{"border-t":eu()}],"border-w-r":[{"border-r":eu()}],"border-w-b":[{"border-b":eu()}],"border-w-l":[{"border-l":eu()}],"divide-x":[{"divide-x":eu()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":eu()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...es(),"hidden","none"]}],"divide-style":[{divide:[...es(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...es(),"none","hidden"]}],"outline-offset":[{"outline-offset":[x,L,C]}],"outline-w":[{outline:["",x,U,N]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",c,K,R]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",d,K,R]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:eu()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[x,N]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":eu()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",h,K,R]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[x,L,C]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[x]}],"mask-image-linear-from-pos":[{"mask-linear-from":ef()}],"mask-image-linear-to-pos":[{"mask-linear-to":ef()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":ef()}],"mask-image-t-to-pos":[{"mask-t-to":ef()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":ef()}],"mask-image-r-to-pos":[{"mask-r-to":ef()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":ef()}],"mask-image-b-to-pos":[{"mask-b-to":ef()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":ef()}],"mask-image-l-to-pos":[{"mask-l-to":ef()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":ef()}],"mask-image-x-to-pos":[{"mask-x-to":ef()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":ef()}],"mask-image-y-to-pos":[{"mask-y-to":ef()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[L,C]}],"mask-image-radial-from-pos":[{"mask-radial-from":ef()}],"mask-image-radial-to-pos":[{"mask-radial-to":ef()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":_()}],"mask-image-conic-pos":[{"mask-conic":[x]}],"mask-image-conic-from-pos":[{"mask-conic-from":ef()}],"mask-image-conic-to-pos":[{"mask-conic-to":ef()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:en()}],"mask-repeat":[{mask:ei()}],"mask-size":[{mask:eo()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",L,C]}],filter:[{filter:["","none",L,C]}],blur:[{blur:ed()}],brightness:[{brightness:[x,L,C]}],contrast:[{contrast:[x,L,C]}],"drop-shadow":[{"drop-shadow":["","none",p,K,R]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",x,L,C]}],"hue-rotate":[{"hue-rotate":[x,L,C]}],invert:[{invert:["",x,L,C]}],saturate:[{saturate:[x,L,C]}],sepia:[{sepia:["",x,L,C]}],"backdrop-filter":[{"backdrop-filter":["","none",L,C]}],"backdrop-blur":[{"backdrop-blur":ed()}],"backdrop-brightness":[{"backdrop-brightness":[x,L,C]}],"backdrop-contrast":[{"backdrop-contrast":[x,L,C]}],"backdrop-grayscale":[{"backdrop-grayscale":["",x,L,C]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[x,L,C]}],"backdrop-invert":[{"backdrop-invert":["",x,L,C]}],"backdrop-opacity":[{"backdrop-opacity":[x,L,C]}],"backdrop-saturate":[{"backdrop-saturate":[x,L,C]}],"backdrop-sepia":[{"backdrop-sepia":["",x,L,C]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":W()}],"border-spacing-x":[{"border-spacing-x":W()}],"border-spacing-y":[{"border-spacing-y":W()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",L,C]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[x,"initial",L,C]}],ease:[{ease:["linear","initial",m,L,C]}],delay:[{delay:[x,L,C]}],animate:[{animate:["none",b,L,C]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,L,C]}],"perspective-origin":[{"perspective-origin":P()}],rotate:[{rotate:eh()}],"rotate-x":[{"rotate-x":eh()}],"rotate-y":[{"rotate-y":eh()}],"rotate-z":[{"rotate-z":eh()}],scale:[{scale:ep()}],"scale-x":[{"scale-x":ep()}],"scale-y":[{"scale-y":ep()}],"scale-z":[{"scale-z":ep()}],"scale-3d":["scale-3d"],skew:[{skew:ey()}],"skew-x":[{"skew-x":ey()}],"skew-y":[{"skew-y":ey()}],transform:[{transform:[L,C,"","none","gpu","cpu"]}],"transform-origin":[{origin:P()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",L,C]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":W()}],"scroll-mx":[{"scroll-mx":W()}],"scroll-my":[{"scroll-my":W()}],"scroll-ms":[{"scroll-ms":W()}],"scroll-me":[{"scroll-me":W()}],"scroll-mt":[{"scroll-mt":W()}],"scroll-mr":[{"scroll-mr":W()}],"scroll-mb":[{"scroll-mb":W()}],"scroll-ml":[{"scroll-ml":W()}],"scroll-p":[{"scroll-p":W()}],"scroll-px":[{"scroll-px":W()}],"scroll-py":[{"scroll-py":W()}],"scroll-ps":[{"scroll-ps":W()}],"scroll-pe":[{"scroll-pe":W()}],"scroll-pt":[{"scroll-pt":W()}],"scroll-pr":[{"scroll-pr":W()}],"scroll-pb":[{"scroll-pb":W()}],"scroll-pl":[{"scroll-pl":W()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",L,C]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[x,U,N,D]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},76069:(e,t,r)=>{"use strict";r.d(t,{Mz:()=>w});var n=e=>Array.isArray(e)?e:[e],i=0,o=class{revision=i;_value;_lastValue;_isEqual=a;constructor(e,t=a){this._value=this._lastValue=e,this._isEqual=t}get value(){return this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++i)}};function a(e,t){return e===t}function l(e){return e instanceof o||console.warn("Not a valid cell! ",e),e.value}var u=(e,t)=>!1;function s(){return function(e,t=a){return new o(null,t)}(0,u)}var c=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=s()),l(t)};Symbol();var f=0,d=Object.getPrototypeOf({}),h=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,p);tag=s();tags={};children={};collectionTag=null;id=f++},p={get:(e,t)=>(function(){let{value:r}=e,n=Reflect.get(r,t);if("symbol"==typeof t||t in d)return n;if("object"==typeof n&&null!==n){var i;let r=e.children[t];return void 0===r&&(r=e.children[t]=Array.isArray(i=n)?new y(i):new h(i)),r.tag&&l(r.tag),r.proxy}{let r=e.tags[t];return void 0===r&&((r=e.tags[t]=s()).value=n),l(r),n}})(),ownKeys:e=>(c(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},y=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],g);tag=s();tags={};children={};collectionTag=null;id=f++},g={get:([e],t)=>("length"===t&&c(e),p.get(e,t)),ownKeys:([e])=>p.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>p.getOwnPropertyDescriptor(e,t),has:([e],t)=>p.has(e,t)},v="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function m(){return{s:0,v:void 0,o:null,p:null}}function b(e,t={}){let r,n=m(),{resultEqualityCheck:i}=t,o=0;function a(){let t,a=n,{length:l}=arguments;for(let e=0;e<l;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=a.o;null===e&&(a.o=e=new WeakMap);let r=e.get(t);void 0===r?(a=m(),e.set(t,a)):a=r}else{let e=a.p;null===e&&(a.p=e=new Map);let r=e.get(t);void 0===r?(a=m(),e.set(t,a)):a=r}}let u=a;if(1===a.s)t=a.v;else if(t=e.apply(null,arguments),o++,i){let e=r?.deref?.()??r;null!=e&&i(e,t)&&(t=e,0!==o&&o--),r="object"==typeof t&&null!==t||"function"==typeof t?new v(t):t}return u.s=1,u.v=t,t}return a.clearCache=()=>{n=m(),a.resetResultsCount()},a.resultsCount=()=>o,a.resetResultsCount=()=>{o=0},a}var w=function(e,...t){let r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,i=(...e)=>{let t,i=0,o=0,a={},l=e.pop();"object"==typeof l&&(a=l,l=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(l,`createSelector expects an output function after the inputs, but received: [${typeof l}]`);let{memoize:u,memoizeOptions:s=[],argsMemoize:c=b,argsMemoizeOptions:f=[],devModeChecks:d={}}={...r,...a},h=n(s),p=n(f),y=function(e){let t=Array.isArray(e[0])?e[0]:e;return!function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),g=u(function(){return i++,l.apply(null,arguments)},...h);return Object.assign(c(function(){o++;let e=function(e,t){let r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(y,arguments);return t=g.apply(null,e)},...p),{resultFunc:l,memoizedResultFunc:g,dependencies:y,dependencyRecomputations:()=>o,resetDependencyRecomputations:()=>{o=0},lastResult:()=>t,recomputations:()=>i,resetRecomputations:()=>{i=0},memoize:u,argsMemoize:c})};return Object.assign(i,{withTypes:()=>i}),i}(b),x=Object.assign((e,t=w)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>x})},77159:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}},78519:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},79835:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(89644),i=r(70846);t.isArrayLikeObject=function(e){return i.isObjectLike(e)&&n.isArrayLike(e)}},79862:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return n}});let n=r(28140)._(r(12115)).default.createContext(null)},81024:(e,t,r)=>{"use strict";r.d(t,{G:()=>f,j:()=>l});var n=r(8828),i=r(12115),o=r(49629),a=e=>e,l=()=>{var e=(0,i.useContext)(o.E);return e?e.store.dispatch:a},u=()=>{},s=()=>u,c=(e,t)=>e===t;function f(e){var t=(0,i.useContext)(o.E);return(0,n.useSyncExternalStoreWithSelector)(t?t.subscription.addNestedSub:s,t?t.store.getState:u,t?t.store.getState:u,t?e:u,c)}},81093:(e,t,r)=>{"use strict";function n(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}r.d(t,{HY:()=>s,Qd:()=>l,Tw:()=>f,Zz:()=>c,ve:()=>d,y$:()=>u});var i="function"==typeof Symbol&&Symbol.observable||"@@observable",o=()=>Math.random().toString(36).substring(7).split("").join("."),a={INIT:`@@redux/INIT${o()}`,REPLACE:`@@redux/REPLACE${o()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${o()}`};function l(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function u(e,t,r){if("function"!=typeof e)throw Error(n(2));if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw Error(n(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw Error(n(1));return r(u)(e,t)}let o=e,s=t,c=new Map,f=c,d=0,h=!1;function p(){f===c&&(f=new Map,c.forEach((e,t)=>{f.set(t,e)}))}function y(){if(h)throw Error(n(3));return s}function g(e){if("function"!=typeof e)throw Error(n(4));if(h)throw Error(n(5));let t=!0;p();let r=d++;return f.set(r,e),function(){if(t){if(h)throw Error(n(6));t=!1,p(),f.delete(r),c=null}}}function v(e){if(!l(e))throw Error(n(7));if(void 0===e.type)throw Error(n(8));if("string"!=typeof e.type)throw Error(n(17));if(h)throw Error(n(9));try{h=!0,s=o(s,e)}finally{h=!1}return(c=f).forEach(e=>{e()}),e}return v({type:a.INIT}),{dispatch:v,subscribe:g,getState:y,replaceReducer:function(e){if("function"!=typeof e)throw Error(n(10));o=e,v({type:a.REPLACE})},[i]:function(){return{subscribe(e){if("object"!=typeof e||null===e)throw Error(n(11));function t(){e.next&&e.next(y())}return t(),{unsubscribe:g(t)}},[i](){return this}}}}}function s(e){let t,r=Object.keys(e),i={};for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof e[n]&&(i[n]=e[n])}let o=Object.keys(i);try{Object.keys(i).forEach(e=>{let t=i[e];if(void 0===t(void 0,{type:a.INIT}))throw Error(n(12));if(void 0===t(void 0,{type:a.PROBE_UNKNOWN_ACTION()}))throw Error(n(13))})}catch(e){t=e}return function(e={},r){if(t)throw t;let a=!1,l={};for(let t=0;t<o.length;t++){let u=o[t],s=i[u],c=e[u],f=s(c,r);if(void 0===f)throw r&&r.type,Error(n(14));l[u]=f,a=a||f!==c}return(a=a||o.length!==Object.keys(e).length)?l:e}}function c(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...r)=>e(t(...r)))}function f(...e){return t=>(r,i)=>{let o=t(r,i),a=()=>{throw Error(n(15))},l={getState:o.getState,dispatch:(e,...t)=>a(e,...t)};return a=c(...e.map(e=>e(l)))(o.dispatch),{...o,dispatch:a}}}function d(e){return l(e)&&"type"in e&&"string"==typeof e.type}},81262:(e,t,r)=>{"use strict";r.d(t,{J:()=>et});var n=r(12115);r(28138);var i={notify(){},get:()=>[]},o="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,a="undefined"!=typeof navigator&&"ReactNative"===navigator.product,l=o||a?n.useLayoutEffect:n.useEffect;Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var u=Symbol.for("react-redux-context"),s="undefined"!=typeof globalThis?globalThis:{},c=function(){if(!n.createContext)return{};let e=s[u]??=new Map,t=e.get(n.createContext);return t||(t=n.createContext(null),e.set(n.createContext,t)),t}(),f=function(e){let{children:t,context:r,serverState:o,store:a}=e,u=n.useMemo(()=>{let e=function(e,t){let r,n=i,o=0,a=!1;function l(){c.onStateChange&&c.onStateChange()}function u(){if(o++,!r){let t,i;r=e.subscribe(l),t=null,i=null,n={clear(){t=null,i=null},notify(){let e=t;for(;e;)e.callback(),e=e.next},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let r=!0,n=i={callback:e,next:null,prev:i};return n.prev?n.prev.next=n:t=n,function(){r&&null!==t&&(r=!1,n.next?n.next.prev=n.prev:i=n.prev,n.prev?n.prev.next=n.next:t=n.next)}}}}}function s(){o--,r&&0===o&&(r(),r=void 0,n.clear(),n=i)}let c={addNestedSub:function(e){u();let t=n.subscribe(e),r=!1;return()=>{r||(r=!0,t(),s())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:l,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,u())},tryUnsubscribe:function(){a&&(a=!1,s())},getListeners:()=>n};return c}(a);return{store:a,subscription:e,getServerState:o?()=>o:void 0}},[a,o]),s=n.useMemo(()=>a.getState(),[a]);return l(()=>{let{subscription:e}=u;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),s!==a.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[u,s]),n.createElement((r||c).Provider,{value:u},t)},d=r(81093),h=r(26286),p=r(33308),y=r(83507),g=r(94078),v=r(38116),m=r(75754);function b(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}var w=r(92487),x=r(14599),O=r(60013),M=(0,h.Z0)({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=(0,O.ss)(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=(0,O.ss)(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=(0,O.ss)(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:A,removeDot:j,addArea:S,removeArea:_,addLine:P,removeLine:E}=M.actions,k=M.reducer,T={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},C=(0,h.Z0)({name:"brush",initialState:T,reducers:{setBrushSettings:(e,t)=>null==t.payload?T:t.payload}}),{setBrushSettings:N}=C.actions,D=C.reducer,z=r(69277),I=r(93100),R=(0,h.Z0)({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=(0,O.h4)(t.payload)},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=(0,O.h4)(t.payload)},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:L,removeRadiusAxis:U,addAngleAxis:B,removeAngleAxis:$}=R.actions,F=R.reducer,H=r(19052),K=r(85080),q=r(49074),W=r(37205),Z=(0,h.Z0)({name:"errorBars",initialState:{},reducers:{addErrorBar:(e,t)=>{var{itemId:r,errorBar:n}=t.payload;e[r]||(e[r]=[]),e[r].push(n)},removeErrorBar:(e,t)=>{var{itemId:r,errorBar:n}=t.payload;e[r]&&(e[r]=e[r].filter(e=>e.dataKey!==n.dataKey||e.direction!==n.direction))}}}),{addErrorBar:G,removeErrorBar:Y}=Z.actions,V=Z.reducer,X=(0,d.HY)({brush:D,cartesianAxis:w.CA,chartData:g.LV,errorBars:V,graphicalItems:x.iZ,layout:v.Vp,legend:z.CU,options:p.lJ,polarAxis:F,polarOptions:H.J,referenceElements:k,rootProps:I.vE,tooltip:y.En}),J=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return(0,h.U1)({reducer:X,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([m.YF.middleware,m.fP.middleware,K.$7.middleware,q.x.middleware,W.k.middleware]),devTools:{serialize:{replacer:b},name:"recharts-".concat(t)}})},Q=r(35704),ee=r(49629);function et(e){var{preloadedState:t,children:r,reduxStoreName:i}=e,o=(0,Q.r)(),a=(0,n.useRef)(null);if(o)return r;null==a.current&&(a.current=J(t,i));var l=ee.E;return n.createElement(f,{context:l,store:a.current},r)}},81356:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return w}});let n=r(28140),i=r(49417),o=r(95155),a=i._(r(12115)),l=n._(r(47650)),u=n._(r(74841)),s=r(75040),c=r(20821),f=r(33455);r(94781);let d=r(79862),h=n._(r(71124)),p=r(83011),y={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function g(e,t,r,n,i,o,a){let l=null==e?void 0:e.src;e&&e["data-loaded-src"]!==l&&(e["data-loaded-src"]=l,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&i(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,i=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>i,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{i=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function v(e){return a.use?{fetchPriority:e}:{fetchpriority:e}}let m=(0,a.forwardRef)((e,t)=>{let{src:r,srcSet:n,sizes:i,height:l,width:u,decoding:s,className:c,style:f,fetchPriority:d,placeholder:h,loading:y,unoptimized:m,fill:b,onLoadRef:w,onLoadingCompleteRef:x,setBlurComplete:O,setShowAltText:M,sizesInput:A,onLoad:j,onError:S,..._}=e,P=(0,a.useCallback)(e=>{e&&(S&&(e.src=e.src),e.complete&&g(e,h,w,x,O,m,A))},[r,h,w,x,O,S,m,A]),E=(0,p.useMergedRef)(t,P);return(0,o.jsx)("img",{..._,...v(d),loading:y,width:u,height:l,decoding:s,"data-nimg":b?"fill":"1",className:c,style:f,sizes:i,srcSet:n,src:r,ref:E,onLoad:e=>{g(e.currentTarget,h,w,x,O,m,A)},onError:e=>{M(!0),"empty"!==h&&O(!0),S&&S(e)}})});function b(e){let{isAppRouter:t,imgAttributes:r}=e,n={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...v(r.fetchPriority)};return t&&l.default.preload?(l.default.preload(r.src,n),null):(0,o.jsx)(u.default,{children:(0,o.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...n},"__nimg-"+r.src+r.srcSet+r.sizes)})}let w=(0,a.forwardRef)((e,t)=>{let r=(0,a.useContext)(d.RouterContext),n=(0,a.useContext)(f.ImageConfigContext),i=(0,a.useMemo)(()=>{var e;let t=y||n||c.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),i=t.deviceSizes.sort((e,t)=>e-t),o=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:i,qualities:o}},[n]),{onLoad:l,onLoadingComplete:u}=e,p=(0,a.useRef)(l);(0,a.useEffect)(()=>{p.current=l},[l]);let g=(0,a.useRef)(u);(0,a.useEffect)(()=>{g.current=u},[u]);let[v,w]=(0,a.useState)(!1),[x,O]=(0,a.useState)(!1),{props:M,meta:A}=(0,s.getImgProps)(e,{defaultLoader:h.default,imgConf:i,blurComplete:v,showAltText:x});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(m,{...M,unoptimized:A.unoptimized,placeholder:A.placeholder,fill:A.fill,onLoadRef:p,onLoadingCompleteRef:g,setBlurComplete:w,setShowAltText:O,sizesInput:e.sizes,ref:t}),A.priority?(0,o.jsx)(b,{isAppRouter:!r,imgAttributes:M}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},81943:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isUnsafeProperty=function(e){return"__proto__"===e}},82601:(e,t,r)=>{"use strict";r.d(t,{i:()=>a});var n=r(83507);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var a=(e,t,r,i)=>{if(null==t)return n.k_;var a=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==a)return n.k_;if(a.active)return a;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var l=!0===e.settings.active;if(null!=a.index){if(l)return o(o({},a),{},{active:!0})}else if(null!=i)return{active:!0,coordinate:void 0,dataKey:void 0,index:i};return o(o({},n.k_),{},{coordinate:a.coordinate})}},83011:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return i}});let n=r(12115);function i(e,t){let r=(0,n.useRef)(null),i=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=i.current;t&&(i.current=null,t())}else e&&(r.current=o(e,n)),t&&(i.current=o(t,n))},[e,t])}function o(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83101:(e,t,r)=>{"use strict";r.d(t,{F:()=>a});var n=r(2821);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=n.$,a=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return o(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:l}=t,u=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],n=null==l?void 0:l[e];if(null===t)return null;let o=i(t)||i(n);return a[e][o]}),s=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return o(e,u,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...s}[t]):({...l,...s})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},83507:(e,t,r)=>{"use strict";r.d(t,{E1:()=>g,En:()=>m,Ix:()=>l,ML:()=>h,Nt:()=>p,RD:()=>c,XB:()=>u,jF:()=>y,k_:()=>o,o4:()=>v,oP:()=>f,xS:()=>d});var n=r(26286),i=r(60013),o={active:!1,index:null,dataKey:void 0,coordinate:void 0},a=(0,n.Z0)({name:"tooltip",initialState:{itemInteraction:{click:o,hover:o},axisInteraction:{click:o,hover:o},keyboardInteraction:o,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push((0,i.h4)(t.payload))},removeTooltipEntrySettings(e,t){var r=(0,i.ss)(e).tooltipItemPayloads.indexOf((0,i.h4)(t.payload));r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:l,removeTooltipEntrySettings:u,setTooltipSettingsState:s,setActiveMouseOverItemIndex:c,mouseLeaveItem:f,mouseLeaveChart:d,setActiveClickItemIndex:h,setMouseOverAxisIndex:p,setMouseClickAxisIndex:y,setSyncInteraction:g,setKeyboardInteraction:v}=a.actions,m=a.reducer},83654:(e,t,r)=>{"use strict";var n=r(12115),i=r(14806),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=i.useSyncExternalStore,l=n.useRef,u=n.useEffect,s=n.useMemo,c=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var f=l(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var h=a(e,(f=s(function(){function e(e){if(!u){if(u=!0,a=e,e=n(e),void 0!==i&&d.hasValue){var t=d.value;if(i(t,e))return l=t}return l=e}if(t=l,o(a,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(a=e,t):(a=e,l=r)}var a,l,u=!1,s=void 0===r?null:r;return[function(){return e(t())},null===s?void 0:function(){return e(s())}]},[t,r,n,i]))[0],f[1]);return u(function(){d.hasValue=!0,d.value=h},[h]),c(h),h}},84020:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var n=r(12115),i=r(93100),o=r(81024);function a(e){var t=(0,o.j)();return(0,n.useEffect)(()=>{t((0,i.mZ)(e))},[t,e]),null}},84072:(e,t,r)=>{"use strict";r.d(t,{VU:()=>a,XC:()=>u,_U:()=>l});var n=r(12115),i=r(52089),o=["points","pathLength"],a={svg:["viewBox","children"],polygon:o,polyline:o},l=(e,t)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,n.isValidElement)(e)&&(r=e.props),"object"!=typeof r&&"function"!=typeof r)return null;var o={};return Object.keys(r).forEach(e=>{(0,i.q)(e)&&(o[e]=t||(t=>r[e](r,t)))}),o},u=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach(o=>{var a=e[o];(0,i.q)(o)&&"function"==typeof a&&(n||(n={}),n[o]=e=>(a(t,r,e),null))}),n}},84811:(e,t,r)=>{"use strict";r.d(t,{D:()=>a});var n=r(1444),i=r(95603),o=r(68570),a=e=>{var t=(0,i.R)(e),r=(0,o.M)(e);return(0,n.Hd)(e,t,r)}},85080:(e,t,r)=>{"use strict";r.d(t,{$7:()=>f,Ru:()=>c,uZ:()=>s});var n=r(26286),i=r(83507),o=r(72481),a=r(59799),l=r(1444),u=r(21838),s=(0,n.VP)("keyDown"),c=(0,n.VP)("focus"),f=(0,n.Nc)();f.startListening({actionCreator:s,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,s=e.payload;if("ArrowRight"===s||"ArrowLeft"===s||"Enter"===s){var c=Number((0,u.P)(n,(0,o.n4)(r))),f=(0,o.R4)(r);if("Enter"===s){var d=(0,a.pg)(r,"axis","hover",String(n.index));t.dispatch((0,i.o4)({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:d}));return}var h=c+("ArrowRight"===s?1:-1)*("left-to-right"===(0,l._y)(r)?1:-1);if(null!=f&&!(h>=f.length)&&!(h<0)){var p=(0,a.pg)(r,"axis","hover",String(h));t.dispatch((0,i.o4)({active:!0,activeIndex:h.toString(),activeDataKey:void 0,activeCoordinate:p}))}}}}}),f.startListening({actionCreator:c,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var o=(0,a.pg)(r,"axis","hover",String("0"));t.dispatch((0,i.o4)({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:o}))}}}})},85224:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return Object.keys(t).reduce((e,r)=>(void 0===e[r]&&void 0!==t[r]&&(e[r]=t[r]),e),r)}r.d(t,{e:()=>i})},85339:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var n=r(49580),i=(e,t)=>{var r,i=Number(t);if(!(0,n.M8)(i)&&null!=t)return i>=0?null==e||null==(r=e[i])?void 0:r.value:void 0}},85998:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},86651:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},87095:(e,t,r)=>{"use strict";r.d(t,{W:()=>u});var n=r(12115),i=r(2821),o=r(70543),a=["children","className"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var u=n.forwardRef((e,t)=>{var{children:r,className:u}=e,s=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,a),c=(0,i.$)("recharts-layer",u);return n.createElement("g",l({className:c},(0,o.J9)(s,!0),{ref:t}),r)})},87176:(e,t,r)=>{"use strict";r.d(t,{r:()=>l});var n=r(12115),i=r(81024),o=r(83507),a=r(35704);function l(e){var{fn:t,args:r}=e,l=(0,i.j)(),u=(0,a.r)();return(0,n.useEffect)(()=>{if(!u){var e=t(r);return l((0,o.Ix)(e)),()=>{l((0,o.XB)(e))}}},[t,r,l,u]),null}},87744:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}},88011:(e,t,r)=>{e.exports=r(57333).throttle},89139:(e,t,r)=>{e.exports=r(31730).uniqBy},89569:(e,t,r)=>{"use strict";r.d(t,{i:()=>u});let n=Math.PI,i=2*n,o=i-1e-6;function a(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class l{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?a:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return a;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,i,o){this._append`C${+e},${+t},${+r},${+n},${this._x1=+i},${this._y1=+o}`}arcTo(e,t,r,i,o){if(e*=1,t*=1,r*=1,i*=1,(o*=1)<0)throw Error(`negative radius: ${o}`);let a=this._x1,l=this._y1,u=r-e,s=i-t,c=a-e,f=l-t,d=c*c+f*f;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(d>1e-6)if(Math.abs(f*u-s*c)>1e-6&&o){let h=r-a,p=i-l,y=u*u+s*s,g=Math.sqrt(y),v=Math.sqrt(d),m=o*Math.tan((n-Math.acos((y+d-(h*h+p*p))/(2*g*v)))/2),b=m/v,w=m/g;Math.abs(b-1)>1e-6&&this._append`L${e+b*c},${t+b*f}`,this._append`A${o},${o},0,0,${+(f*h>c*p)},${this._x1=e+w*u},${this._y1=t+w*s}`}else this._append`L${this._x1=e},${this._y1=t}`}arc(e,t,r,a,l,u){if(e*=1,t*=1,r*=1,u=!!u,r<0)throw Error(`negative radius: ${r}`);let s=r*Math.cos(a),c=r*Math.sin(a),f=e+s,d=t+c,h=1^u,p=u?a-l:l-a;null===this._x1?this._append`M${f},${d}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-d)>1e-6)&&this._append`L${f},${d}`,r&&(p<0&&(p=p%i+i),p>o?this._append`A${r},${r},0,1,${h},${e-s},${t-c}A${r},${r},0,1,${h},${this._x1=f},${this._y1=d}`:p>1e-6&&this._append`A${r},${r},0,${+(p>=n)},${h},${this._x1=e+r*Math.cos(l)},${this._y1=t+r*Math.sin(l)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function u(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new l(t)}l.prototype},89644:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(37047);t.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&n.isLength(e.length)}},90135:(e,t,r)=>{"use strict";r.d(t,{HS:()=>a,LF:()=>i,z3:()=>o});var n=r(76069),i=e=>e.chartData,o=(0,n.Mz)([i],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),a=(e,t,r,n)=>n?o(e):i(e)},90167:(e,t,r)=>{"use strict";r.d(t,{Kp:()=>p,W7:()=>c,WX:()=>g,fz:()=>y,rY:()=>d,sk:()=>u,yi:()=>f}),r(12115);var n=r(81024),i=r(8291),o=r(27588),a=r(35704),l=r(50257),u=()=>{var e,t=(0,a.r)(),r=(0,n.G)(i.Ds),o=(0,n.G)(l.U),u=null==(e=(0,n.G)(l.C))?void 0:e.padding;return t&&o&&u?{width:o.width-u.left-u.right,height:o.height-u.top-u.bottom,x:u.left,y:u.top}:r},s={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},c=()=>{var e;return null!=(e=(0,n.G)(i.HZ))?e:s},f=()=>(0,n.G)(o.Lp),d=()=>(0,n.G)(o.A$),h={top:0,right:0,bottom:0,left:0},p=()=>{var e;return null!=(e=(0,n.G)(e=>e.layout.margin))?e:h},y=e=>e.layout.layoutType,g=()=>(0,n.G)(y)},90737:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return n}});let n=r(28140)._(r(12115)).default.createContext({})},91169:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},91640:(e,t,r)=>{"use strict";r.d(t,{dc:()=>l,ff:()=>a,g0:()=>u});var n=r(76069),i=r(97354),o=r.n(i),a=e=>e.legend.settings,l=e=>e.legend.size,u=(0,n.Mz)([e=>e.legend.payload,a],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?o()(n,r):n})},92056:(e,t,r)=>{"use strict";r.d(t,{Cj:()=>o,Pg:()=>a,Ub:()=>l});var n=r(81024),i=r(83507),o=(e,t)=>{var r=(0,n.j)();return(n,o)=>a=>{null==e||e(n,o,a),r((0,i.RD)({activeIndex:String(o),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},a=e=>{var t=(0,n.j)();return(r,n)=>o=>{null==e||e(r,n,o),t((0,i.oP)())}},l=(e,t)=>{var r=(0,n.j)();return(n,o)=>a=>{null==e||e(n,o,a),r((0,i.ML)({activeIndex:String(o),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}}},92143:(e,t,r)=>{"use strict";r.d(t,{i:()=>k});var n=r(12115);let i=Math.cos,o=Math.sin,a=Math.sqrt,l=Math.PI,u=2*l,s={draw(e,t){let r=a(t/l);e.moveTo(r,0),e.arc(0,0,r,0,u)}},c=a(1/3),f=2*c,d=o(l/10)/o(7*l/10),h=o(u/10)*d,p=-i(u/10)*d,y=a(3),g=a(3)/2,v=1/a(12),m=(v/2+1)*3;var b=r(73595),w=r(89569);a(3),a(3);var x=r(2821),O=r(70543),M=r(49580),A=["type","size","sizeType"];function j(){return(j=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function S(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function _(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?S(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var P={symbolCircle:s,symbolCross:{draw(e,t){let r=a(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=a(t/f),n=r*c;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=a(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=a(.8908130915292852*t),n=h*r,l=p*r;e.moveTo(0,-r),e.lineTo(n,l);for(let t=1;t<5;++t){let a=u*t/5,s=i(a),c=o(a);e.lineTo(c*r,-s*r),e.lineTo(s*n-c*l,c*n+s*l)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-a(t/(3*y));e.moveTo(0,2*r),e.lineTo(-y*r,-r),e.lineTo(y*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=a(t/m),n=r/2,i=r*v,o=r*v+r,l=-n;e.moveTo(n,i),e.lineTo(n,o),e.lineTo(l,o),e.lineTo(-.5*n-g*i,g*n+-.5*i),e.lineTo(-.5*n-g*o,g*n+-.5*o),e.lineTo(-.5*l-g*o,g*l+-.5*o),e.lineTo(-.5*n+g*i,-.5*i-g*n),e.lineTo(-.5*n+g*o,-.5*o-g*n),e.lineTo(-.5*l+g*o,-.5*o-g*l),e.closePath()}}},E=Math.PI/180,k=e=>{var{type:t="circle",size:r=64,sizeType:i="area"}=e,o=_(_({},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,A)),{},{type:t,size:r,sizeType:i}),{className:a,cx:l,cy:u}=o,c=(0,O.J9)(o,!0);return l===+l&&u===+u&&r===+r?n.createElement("path",j({},c,{className:(0,x.$)("recharts-symbols",a),transform:"translate(".concat(l,", ").concat(u,")"),d:(()=>{var e=P["symbol".concat((0,M.Zb)(t))]||s;return(function(e,t){let r=null,n=(0,w.i)(i);function i(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return e="function"==typeof e?e:(0,b.A)(e||s),t="function"==typeof t?t:(0,b.A)(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"==typeof t?t:(0,b.A)(t),i):e},i.size=function(e){return arguments.length?(t="function"==typeof e?e:(0,b.A)(+e),i):t},i.context=function(e){return arguments.length?(r=null==e?null:e,i):r},i})().type(e).size(((e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*E;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}})(r,i,t))()})()})):null};k.registerSymbol=(e,t)=>{P["symbol".concat((0,M.Zb)(e))]=t}},92377:(e,t,r)=>{"use strict";function n(e){return Number.isFinite(e)}function i(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}r.d(t,{F:()=>i,H:()=>n})},92487:(e,t,r)=>{"use strict";r.d(t,{CA:()=>y,MC:()=>s,QG:()=>p,Vi:()=>u,cU:()=>c,fR:()=>f});var n=r(26286),i=r(60013);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var l=(0,n.Z0)({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=(0,i.h4)(t.payload)},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=(0,i.h4)(t.payload)},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=(0,i.h4)(t.payload)},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=a(a({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:u,removeXAxis:s,addYAxis:c,removeYAxis:f,addZAxis:d,removeZAxis:h,updateYAxisWidth:p}=l.actions,y=l.reducer},93100:(e,t,r)=>{"use strict";r.d(t,{mZ:()=>l,vE:()=>a});var n=r(26286),i={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},o=(0,n.Z0)({name:"rootProps",initialState:i,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!=(r=t.payload.barGap)?r:i.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),a=o.reducer,{updateOptions:l}=o.actions},93276:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(81943),i=r(94202),o=r(35067),a=r(25723);t.get=function e(t,r,l){if(null==t)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;let o=t[r];if(void 0===o)if(i.isDeepKey(r))return e(t,a.toPath(r),l);else return l;return o}case"number":case"symbol":{"number"==typeof r&&(r=o.toKey(r));let e=t[r];if(void 0===e)return l;return e}default:{if(Array.isArray(r)){var u=t,s=r,c=l;if(0===s.length)return c;let e=u;for(let t=0;t<s.length;t++){if(null==e||n.isUnsafeProperty(s[t]))return c;e=e[s[t]]}return void 0===e?c:e}if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;let e=t[r];if(void 0===e)return l;return e}}}},93341:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},94078:(e,t,r)=>{"use strict";r.d(t,{LV:()=>l,M:()=>o,hq:()=>i});var n=(0,r(26286).Z0)({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload){e.dataStartIndex=0,e.dataEndIndex=0;return}t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:i,setDataStartEndIndexes:o,setComputedData:a}=n.actions,l=n.reducer},94202:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},94820:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function o(e,t,n,o,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var l=new i(n,o||e,a),u=r?r+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],l]:e._events[u].push(l):(e._events[u]=l,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),l.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},l.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,o=n.length,a=Array(o);i<o;i++)a[i]=n[i].fn;return a},l.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,i,o,a){var l=r?r+e:e;if(!this._events[l])return!1;var u,s,c=this._events[l],f=arguments.length;if(c.fn){switch(c.once&&this.removeListener(e,c.fn,void 0,!0),f){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,t),!0;case 3:return c.fn.call(c.context,t,n),!0;case 4:return c.fn.call(c.context,t,n,i),!0;case 5:return c.fn.call(c.context,t,n,i,o),!0;case 6:return c.fn.call(c.context,t,n,i,o,a),!0}for(s=1,u=Array(f-1);s<f;s++)u[s-1]=arguments[s];c.fn.apply(c.context,u)}else{var d,h=c.length;for(s=0;s<h;s++)switch(c[s].once&&this.removeListener(e,c[s].fn,void 0,!0),f){case 1:c[s].fn.call(c[s].context);break;case 2:c[s].fn.call(c[s].context,t);break;case 3:c[s].fn.call(c[s].context,t,n);break;case 4:c[s].fn.call(c[s].context,t,n,i);break;default:if(!u)for(d=1,u=Array(f-1);d<f;d++)u[d-1]=arguments[d];c[s].fn.apply(c[s].context,u)}}return!0},l.prototype.on=function(e,t,r){return o(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return o(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,n,i){var o=r?r+e:e;if(!this._events[o])return this;if(!t)return a(this,o),this;var l=this._events[o];if(l.fn)l.fn!==t||i&&!l.once||n&&l.context!==n||a(this,o);else{for(var u=0,s=[],c=l.length;u<c;u++)(l[u].fn!==t||i&&!l[u].once||n&&l[u].context!==n)&&s.push(l[u]);s.length?this._events[o]=1===s.length?s[0]:s:a(this,o)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,e.exports=l},94868:(e,t,r)=>{"use strict";r.d(t,{w:()=>n});var n=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}}},94913:(e,t,r)=>{"use strict";r.d(t,{n:()=>o});var n=r(12115),i=r(49580);function o(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,n.useRef)((0,i.NF)(t)),o=(0,n.useRef)(e);return o.current!==e&&(r.current=(0,i.NF)(t),o.current=e),r.current}},95603:(e,t,r)=>{"use strict";r.d(t,{R:()=>i});var n=r(90167),i=e=>{var t=(0,n.fz)(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"}},95962:(e,t,r)=>{"use strict";var n=r(12115);"function"==typeof Object.is&&Object.is,n.useSyncExternalStore,n.useRef,n.useEffect,n.useMemo,n.useDebugValue},96288:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(52451),i=r(89644),o=r(35865),a=r(77159);t.isIterateeCall=function(e,t,r){return!!o.isObject(r)&&(!!("number"==typeof t&&i.isArrayLike(r)&&n.isIndex(t))&&t<r.length||"string"==typeof t&&t in r)&&a.eq(r[t],e)}},97354:(e,t,r)=>{e.exports=r(2851).sortBy},98496:(e,t,r)=>{"use strict";r.d(t,{g:()=>c});var n=r(76069),i=r(90167),o=r(72481),a=r(8291),l=r(59799),u=r(14821),s=r(95603),c=(0,n.Mz)([(e,t)=>t,i.fz,u.D0,s.R,o.gL,o.R4,l.r1,a.HZ],l.aX)},99180:(e,t,r)=>{"use strict";r.d(t,{yl:()=>u});var n=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],i=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),o=(e,t)=>r=>i(n(e,t),r),a=function(){let e,t;for(var r,a,l,u,s=arguments.length,c=Array(s),f=0;f<s;f++)c[f]=arguments[f];if(1===c.length)switch(c[0]){case"linear":[r,l,a,u]=[0,0,1,1];break;case"ease":[r,l,a,u]=[.25,.1,.25,1];break;case"ease-in":[r,l,a,u]=[.42,0,1,1];break;case"ease-out":[r,l,a,u]=[.42,0,.58,1];break;case"ease-in-out":[r,l,a,u]=[0,0,.58,1];break;default:var d=c[0].split("(");"cubic-bezier"===d[0]&&4===d[1].split(")")[0].split(",").length&&([r,l,a,u]=d[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===c.length&&([r,l,a,u]=c);var h=o(r,a),p=o(l,u),y=(e=r,t=a,r=>i([...n(e,t).map((e,t)=>e*t).slice(1),0],r)),g=e=>e>1?1:e<0?0:e,v=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=h(r)-t,o=y(r);if(1e-4>Math.abs(i-t)||o<1e-4)break;r=g(r-i/o)}return p(r)};return v.isStepper=!1,v},l=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,o)=>{var a=o+(-(e-i)*t-o*r)*n/1e3,l=o*n/1e3+e;return 1e-4>Math.abs(l-i)&&1e-4>Math.abs(a)?[i,0]:[l,a]};return i.isStepper=!0,i.dt=n,i},u=e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return a(e);case"spring":return l();default:if("cubic-bezier"===e.split("(")[0])return a(e)}return"function"==typeof e?e:null}}}]);