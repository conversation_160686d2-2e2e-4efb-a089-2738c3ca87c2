"use client"

import { Card, CardContent } from "@/components/ui/card"
import { TrendingUp, TrendingDown, Users, DollarSign, Activity, CreditCard } from "lucide-react"
import { formatCurrency } from "@/lib/utils"

interface StatsCardProps {
  title: string
  value: string | number
  change?: number
  icon: React.ReactNode
  color?: string
}

function StatsCard({ title, value, change, icon, color = "blue" }: StatsCardProps) {
  const isPositive = change && change > 0
  const colorClasses = {
    blue: "bg-blue-100 text-blue-600",
    green: "bg-green-100 text-green-600",
    orange: "bg-orange-100 text-orange-600",
    purple: "bg-purple-100 text-purple-600"
  }

  return (
    <Card className="bg-white rounded-[25px] border-none shadow-lg">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex flex-col space-y-2">
            <span className="text-sm text-[#718EBF] font-medium">{title}</span>
            <span className="text-2xl font-bold text-[#232323]">
              {typeof value === 'number' ? formatCurrency(value) : value}
            </span>
            {change && (
              <div className="flex items-center space-x-1">
                {isPositive ? (
                  <TrendingUp className="h-4 w-4 text-green-500" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-500" />
                )}
                <span className={`text-sm font-medium ${isPositive ? 'text-green-500' : 'text-red-500'}`}>
                  {Math.abs(change)}%
                </span>
              </div>
            )}
          </div>
          <div className={`p-4 rounded-full ${colorClasses[color as keyof typeof colorClasses]}`}>
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

interface StatsCardsProps {
  totalUsers: number
  totalTransactions: number
  totalRevenue: number
  activeAgents: number
}

export function StatsCards({ totalUsers, totalTransactions, totalRevenue, activeAgents }: StatsCardsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <StatsCard
        title="Total Users"
        value={totalUsers.toLocaleString()}
        change={12.5}
        icon={<Users className="h-6 w-6" />}
        color="blue"
      />
      <StatsCard
        title="Total Transactions"
        value={totalTransactions.toLocaleString()}
        change={8.2}
        icon={<Activity className="h-6 w-6" />}
        color="green"
      />
      <StatsCard
        title="Total Revenue"
        value={totalRevenue}
        change={-2.1}
        icon={<DollarSign className="h-6 w-6" />}
        color="orange"
      />
      <StatsCard
        title="Active Agents"
        value={activeAgents.toLocaleString()}
        change={5.7}
        icon={<CreditCard className="h-6 w-6" />}
        color="purple"
      />
    </div>
  )
}
