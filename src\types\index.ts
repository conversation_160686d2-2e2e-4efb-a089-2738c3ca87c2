export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  balance: number
  cardNumber: string
  cardHolder: string
  validThru: string
  tier: string
}

export interface Transaction {
  id: string
  type: 'cash_in' | 'cash_out' | 'transfer' | 'loan'
  amount: number
  description: string
  date: string
  status: 'completed' | 'pending' | 'failed'
  agent?: string
}

export interface StatisticItem {
  label: string
  value: number
  percentage: number
  color: string
}

export interface ChartData {
  name: string
  value: number
  color?: string
}

export interface WeeklyActivity {
  day: string
  cashIn: number
  cashOut: number
}

export interface AdminStats {
  totalUsers: number
  totalTransactions: number
  totalRevenue: number
  activeAgents: number
}
