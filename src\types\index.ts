export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  balance: number
  cardNumber: string
  cardHolder: string
  validThru: string
  tier: string
  phone?: string
  firstName?: string
  lastName?: string
  dateOfBirth?: string
  address?: string
  isVerified?: boolean
  kycStatus?: 'pending' | 'verified' | 'rejected'
}

export interface Transaction {
  id: string
  type: 'cash_in' | 'cash_out' | 'transfer' | 'loan' | 'bill_payment' | 'withdrawal' | 'deposit'
  amount: number
  description: string
  date: string
  status: 'completed' | 'pending' | 'failed'
  agent?: string
  recipient?: string
  recipientAccount?: string
  fee?: number
  reference?: string
  category?: string
  provider?: string
}

export interface StatisticItem {
  label: string
  value: number
  percentage: number
  color: string
}

export interface ChartData {
  name: string
  value: number
  color?: string
}

export interface WeeklyActivity {
  day: string
  cashIn: number
  cashOut: number
}

export interface AdminStats {
  totalUsers: number
  totalTransactions: number
  totalRevenue: number
  activeAgents: number
}

export interface TransferOption {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  color: string
}

export interface BillCategory {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  color: string
  providers: string[]
}

export interface WithdrawalMethod {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  color: string
  fee: string
  processingTime: string
}

export interface FundingSource {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  color: string
}

export interface LoanData {
  amount: number
  purpose: string
  repaymentPeriod: string
  interestRate: number
  processingFee: number
  totalRepayment: number
}

export interface NotificationSettings {
  emailNotifications: boolean
  smsNotifications: boolean
  pushNotifications: boolean
  transactionAlerts: boolean
  securityAlerts: boolean
}
