"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/dashboard/header"
import { Sidebar } from "@/components/dashboard/sidebar"
import { AccountCard } from "@/components/dashboard/account-card"
import { Statistics } from "@/components/dashboard/statistics"
import { RecentTransactions } from "@/components/dashboard/recent-transactions"
import { Transfer } from "@/components/dashboard/transfer"
import { User, Transaction, StatisticItem } from "@/types"

// Mock data
const mockUser: User = {
  id: "1",
  name: "<PERSON>",
  email: "<EMAIL>",
  balance: 35673,
  cardNumber: "****************",
  cardHolder: "James Bond",
  validThru: "12/22",
  tier: "Tier 1"
}

const mockTransactions: Transaction[] = [
  {
    id: "1",
    type: "cash_in",
    amount: 850,
    description: "Cash in (Agent yusuff)",
    date: "2021-01-28",
    status: "completed",
    agent: "Agent yusuff"
  },
  {
    id: "2",
    type: "cash_out",
    amount: 2500,
    description: "Cash out",
    date: "2021-01-25",
    status: "completed"
  },
  {
    id: "3",
    type: "transfer",
    amount: 5400,
    description: "Transfer",
    date: "2021-01-21",
    status: "completed"
  }
]

const mockStatistics: StatisticItem[] = [
  { label: "Cash in", value: 30, percentage: 30, color: "#1814F3" },
  { label: "Loan", value: 20, percentage: 20, color: "#16DBCC" },
  { label: "Cash out", value: 15, percentage: 15, color: "#FF82AC" },
  { label: "Others", value: 35, percentage: 35, color: "#FFBB38" }
]

export default function DashboardPage() {
  const [activeMenuItem, setActiveMenuItem] = useState("dashboard")

  return (
    <div className="flex h-screen bg-[#F5F7FA]">
      <Sidebar activeItem={activeMenuItem} onItemClick={setActiveMenuItem} />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header 
          title="Overview" 
          userName={mockUser.name}
        />
        
        <main className="flex-1 overflow-auto p-4 md:p-8">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
            {/* Left Column */}
            <div className="lg:col-span-8 space-y-6">
              {/* Account Card */}
              <AccountCard user={mockUser} />

              {/* Recent Transactions */}
              <RecentTransactions transactions={mockTransactions} />
            </div>

            {/* Right Column */}
            <div className="lg:col-span-4 space-y-6">
              {/* Transfer Actions */}
              <Transfer />

              {/* Statistics */}
              <Statistics data={mockStatistics} />
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
