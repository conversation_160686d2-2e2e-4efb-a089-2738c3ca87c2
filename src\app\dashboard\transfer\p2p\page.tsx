"use client"

import { useState } from "react"
import { Head<PERSON> } from "@/components/dashboard/header"
import { Sidebar } from "@/components/dashboard/sidebar"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ArrowLeft, Eye, Copy, Info } from "lucide-react"

const quickAmounts = [50, 100, 200, 500]

export default function P2PTransferPage() {
  const [activeMenuItem, setActiveMenuItem] = useState("transfer")
  const [amount, setAmount] = useState("3000")
  const [selectedQuickAmount, setSelectedQuickAmount] = useState<number | null>(null)
  const [recipient, setRecipient] = useState("")
  const [purpose, setPurpose] = useState("")

  const handleQuickAmountClick = (value: number) => {
    setAmount(value.toString())
    setSelectedQuickAmount(value)
  }

  const handleAmountChange = (value: string) => {
    setAmount(value)
    setSelectedQuickAmount(null)
  }

  return (
    <div className="flex h-screen bg-[#F5F7FA]">
      <Sidebar activeItem={activeMenuItem} onItemClick={setActiveMenuItem} />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header 
          title="Transfer" 
          userName="James Bond"
        />
        
        <main className="flex-1 overflow-auto p-4 md:p-8">
          <div className="max-w-4xl mx-auto">
            {/* Back Button */}
            <Button 
              variant="ghost" 
              className="mb-6 text-[#343C6A] hover:bg-gray-100"
              onClick={() => window.history.back()}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Transfer Options
            </Button>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column - Amount Entry */}
              <div>
                <Card className="bg-[#1B263B] text-white">
                  <CardContent className="p-8">
                    <div className="text-center space-y-6">
                      <div>
                        <p className="text-[#AFABAB] text-lg mb-2">Enter Amount</p>
                        <div className="relative">
                          <span className="text-6xl font-semibold">${amount}</span>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <p className="text-[#AFABAB] text-left">Quick Amounts</p>
                        <div className="grid grid-cols-4 gap-3">
                          {quickAmounts.map((value) => (
                            <Button
                              key={value}
                              variant="outline"
                              className={`border-[#059AD1] text-white hover:bg-[#059AD1]/20 ${
                                selectedQuickAmount === value ? 'bg-[#059AD1]/20' : 'bg-black/30'
                              }`}
                              onClick={() => handleQuickAmountClick(value)}
                            >
                              ${value}
                            </Button>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Right Column - Transfer Details */}
              <div className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="recipient" className="text-[#585858] font-semibold">
                      Recipient
                    </Label>
                    <Input
                      id="recipient"
                      placeholder="Enter recipient details"
                      value={recipient}
                      onChange={(e) => setRecipient(e.target.value)}
                      className="mt-2 h-16 border-[#CBCBCB] rounded-3xl px-6"
                    />
                  </div>

                  <div>
                    <Label htmlFor="purpose" className="text-[#585858] font-semibold">
                      Transfer purpose
                    </Label>
                    <Input
                      id="purpose"
                      placeholder="Input transfer purpose"
                      value={purpose}
                      onChange={(e) => setPurpose(e.target.value)}
                      className="mt-2 h-16 border-[#CBCBCB] rounded-3xl px-6"
                    />
                  </div>
                </div>

                {/* Info Alert */}
                <div className="bg-[#F1F6FD] border border-[#A7C5FD] rounded-lg p-4">
                  <div className="flex gap-3">
                    <Info className="h-5 w-5 text-[#0052EA] mt-0.5 flex-shrink-0" />
                    <div className="space-y-2">
                      <p className="text-[#181818] font-medium text-sm">
                        Tier 1 Limit: You can transfer up to ₦50,000.
                      </p>
                      <p className="text-[#181818] text-sm">
                        Upgrade your tier to unlock higher limits and better rates.
                      </p>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="border-[#181818] text-[#181818] hover:bg-[#181818] hover:text-white"
                      >
                        Upgrade
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Continue Button */}
            <div className="mt-8 flex justify-center">
              <Button 
                className="bg-[#059AD1] hover:bg-[#059AD1]/90 text-white px-16 py-6 text-lg rounded-2xl"
                onClick={() => {
                  // Navigate to confirmation page
                  window.location.href = '/dashboard/transfer/p2p/confirm'
                }}
              >
                Continue
              </Button>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
