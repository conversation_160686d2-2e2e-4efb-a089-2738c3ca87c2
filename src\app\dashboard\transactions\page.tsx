"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/dashboard/header"
import { Sidebar } from "@/components/dashboard/sidebar"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  Search, 
  Filter, 
  Download, 
  ArrowUpRight, 
  ArrowDownLeft, 
  CreditCard,
  DollarSign,
  Calendar
} from "lucide-react"

interface TransactionItem {
  id: string
  type: 'transfer' | 'withdrawal' | 'deposit' | 'bill_payment' | 'loan'
  amount: number
  description: string
  date: string
  time: string
  status: 'completed' | 'pending' | 'failed'
  recipient?: string
  category?: string
}

const mockTransactions: TransactionItem[] = [
  {
    id: "TXN001",
    type: "transfer",
    amount: 2500,
    description: "Transfer to John Doe",
    date: "2024-01-15",
    time: "14:30",
    status: "completed",
    recipient: "<PERSON>"
  },
  {
    id: "TXN002",
    type: "bill_payment",
    amount: 5000,
    description: "Electricity Bill - NEPA",
    date: "2024-01-14",
    time: "10:15",
    status: "completed",
    category: "Utilities"
  },
  {
    id: "TXN003",
    type: "deposit",
    amount: 10000,
    description: "Bank Transfer Deposit",
    date: "2024-01-13",
    time: "09:45",
    status: "completed"
  },
  {
    id: "TXN004",
    type: "withdrawal",
    amount: 3000,
    description: "ATM Withdrawal",
    date: "2024-01-12",
    time: "16:20",
    status: "completed"
  },
  {
    id: "TXN005",
    type: "loan",
    amount: 15000,
    description: "Personal Loan",
    date: "2024-01-10",
    time: "11:30",
    status: "pending"
  }
]

export default function TransactionsPage() {
  const [activeMenuItem, setActiveMenuItem] = useState("transactions")
  const [searchTerm, setSearchTerm] = useState("")
  const [filterType, setFilterType] = useState("all")
  const [filterStatus, setFilterStatus] = useState("all")
  const [dateRange, setDateRange] = useState("all")

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'transfer':
        return <ArrowUpRight className="h-5 w-5 text-red-500" />
      case 'deposit':
        return <ArrowDownLeft className="h-5 w-5 text-green-500" />
      case 'withdrawal':
        return <ArrowUpRight className="h-5 w-5 text-red-500" />
      case 'bill_payment':
        return <CreditCard className="h-5 w-5 text-blue-500" />
      case 'loan':
        return <DollarSign className="h-5 w-5 text-orange-500" />
      default:
        return <ArrowUpRight className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredTransactions = mockTransactions.filter(transaction => {
    const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.recipient?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = filterType === "all" || transaction.type === filterType
    const matchesStatus = filterStatus === "all" || transaction.status === filterStatus
    
    return matchesSearch && matchesType && matchesStatus
  })

  return (
    <div className="flex h-screen bg-[#F5F7FA]">
      <Sidebar activeItem={activeMenuItem} onItemClick={setActiveMenuItem} />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header 
          title="Transaction History" 
          userName="James Bond"
        />
        
        <main className="flex-1 overflow-auto p-4 md:p-8">
          <div className="max-w-6xl mx-auto">
            {/* Filters and Search */}
            <Card className="mb-6">
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search transactions..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Transaction Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="transfer">Transfer</SelectItem>
                      <SelectItem value="deposit">Deposit</SelectItem>
                      <SelectItem value="withdrawal">Withdrawal</SelectItem>
                      <SelectItem value="bill_payment">Bill Payment</SelectItem>
                      <SelectItem value="loan">Loan</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="failed">Failed</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={dateRange} onValueChange={setDateRange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Date Range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Time</SelectItem>
                      <SelectItem value="today">Today</SelectItem>
                      <SelectItem value="week">This Week</SelectItem>
                      <SelectItem value="month">This Month</SelectItem>
                      <SelectItem value="year">This Year</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex justify-between items-center mt-4">
                  <p className="text-sm text-[#6E6E6E]">
                    Showing {filteredTransactions.length} of {mockTransactions.length} transactions
                  </p>
                  <Button variant="outline" className="flex items-center gap-2">
                    <Download className="h-4 w-4" />
                    Export
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Transaction Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-[#6E6E6E]">Total Inflow</p>
                      <p className="text-2xl font-bold text-green-600">$25,000</p>
                    </div>
                    <ArrowDownLeft className="h-8 w-8 text-green-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-[#6E6E6E]">Total Outflow</p>
                      <p className="text-2xl font-bold text-red-600">$15,500</p>
                    </div>
                    <ArrowUpRight className="h-8 w-8 text-red-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-[#6E6E6E]">This Month</p>
                      <p className="text-2xl font-bold text-[#333B69]">127</p>
                    </div>
                    <Calendar className="h-8 w-8 text-[#059AD1]" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-[#6E6E6E]">Pending</p>
                      <p className="text-2xl font-bold text-yellow-600">3</p>
                    </div>
                    <Filter className="h-8 w-8 text-yellow-500" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Transactions List */}
            <Card>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b">
                      <tr>
                        <th className="text-left p-4 font-medium text-[#6E6E6E]">Transaction</th>
                        <th className="text-left p-4 font-medium text-[#6E6E6E]">Type</th>
                        <th className="text-left p-4 font-medium text-[#6E6E6E]">Amount</th>
                        <th className="text-left p-4 font-medium text-[#6E6E6E]">Date & Time</th>
                        <th className="text-left p-4 font-medium text-[#6E6E6E]">Status</th>
                        <th className="text-left p-4 font-medium text-[#6E6E6E]">Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredTransactions.map((transaction) => (
                        <tr key={transaction.id} className="border-b hover:bg-gray-50">
                          <td className="p-4">
                            <div className="flex items-center gap-3">
                              {getTransactionIcon(transaction.type)}
                              <div>
                                <p className="font-medium text-[#333B69]">{transaction.description}</p>
                                <p className="text-sm text-[#6E6E6E]">ID: {transaction.id}</p>
                              </div>
                            </div>
                          </td>
                          <td className="p-4">
                            <span className="capitalize text-[#333B69]">
                              {transaction.type.replace('_', ' ')}
                            </span>
                          </td>
                          <td className="p-4">
                            <span className={`font-semibold ${
                              transaction.type === 'deposit' ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {transaction.type === 'deposit' ? '+' : '-'}${transaction.amount.toLocaleString()}
                            </span>
                          </td>
                          <td className="p-4">
                            <div>
                              <p className="text-[#333B69]">{transaction.date}</p>
                              <p className="text-sm text-[#6E6E6E]">{transaction.time}</p>
                            </div>
                          </td>
                          <td className="p-4">
                            <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
                              {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                            </span>
                          </td>
                          <td className="p-4">
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => window.location.href = `/dashboard/receipt?id=${transaction.id}`}
                            >
                              View Details
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {filteredTransactions.length === 0 && (
                  <div className="text-center py-12">
                    <p className="text-[#6E6E6E]">No transactions found matching your criteria.</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  )
}
