"use client"

import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Search, Bell, Settings } from "lucide-react"
import Image from "next/image"

interface HeaderProps {
  title: string
  userAvatar?: string
  userName?: string
}

export function Header({ title, userAvatar, userName }: HeaderProps) {
  return (
    <header className="bg-white border-b border-gray-200 px-4 md:px-8 py-4 md:py-6">
      <div className="flex items-center justify-between">
        <h1 className="text-xl md:text-2xl font-semibold text-[#343C6A]">{title}</h1>

        <div className="flex items-center space-x-2 md:space-x-6">
          {/* Search */}
          <div className="relative hidden md:block">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search for something"
              className="pl-10 w-60 lg:w-80 bg-[#F5F7FA] border-none"
            />
          </div>

          {/* Notifications */}
          <Button variant="ghost" size="icon" className="relative">
            <Bell className="h-5 w-5 text-[#718EBF]" />
            <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></span>
          </Button>

          {/* Settings */}
          <Button variant="ghost" size="icon">
            <Settings className="h-5 w-5 text-[#718EBF]" />
          </Button>

          {/* User Avatar */}
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-200">
              {userAvatar ? (
                <Image 
                  src={userAvatar} 
                  alt={userName || "User"} 
                  width={40} 
                  height={40}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center text-white font-semibold">
                  {userName?.charAt(0) || "U"}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
