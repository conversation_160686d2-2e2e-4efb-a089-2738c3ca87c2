(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{3998:(e,s,a)=>{"use strict";a.d(s,{$:()=>c});var t=a(95155),l=a(12115),r=a(83101),n=a(64269);let i=(0,r.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-[#1814F3] text-white shadow hover:bg-[#1814F3]/90",destructive:"bg-red-500 text-white shadow-sm hover:bg-red-500/90",outline:"border border-gray-200 bg-white shadow-sm hover:bg-gray-50 hover:text-gray-900",secondary:"bg-gray-100 text-gray-900 shadow-sm hover:bg-gray-100/80",ghost:"hover:bg-gray-100 hover:text-gray-900",link:"text-[#1814F3] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=l.forwardRef((e,s)=>{let{className:a,variant:l,size:r,asChild:c=!1,...d}=e;return(0,t.jsx)("button",{className:(0,n.cn)(i({variant:l,size:r,className:a})),ref:s,...d})});c.displayName="Button"},20672:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>D});var t=a(95155),l=a(12115),r=a(47845),n=a(64269),i=a(3998),c=a(71847);let d=(0,c.A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);var o=a(93341),h=a(78519);let x=(0,c.A)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]);var m=a(91169),u=a(15870);let f=(0,c.A)("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),p=(0,c.A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]),g=[{id:"dashboard",label:"Dashboard",icon:d},{id:"transactions",label:"Transactions",icon:o.A},{id:"accounts",label:"Accounts",icon:h.A},{id:"investments",label:"Investments",icon:x},{id:"credit-cards",label:"Credit Cards",icon:h.A},{id:"loans",label:"Loans",icon:m.A},{id:"services",label:"Services",icon:u.A},{id:"privileges",label:"My Privileges",icon:m.A},{id:"settings",label:"Setting",icon:u.A}];function b(e){let{activeItem:s="dashboard",onItemClick:a}=e;return(0,t.jsxs)("aside",{className:"w-64 bg-white border-r border-gray-200 h-screen flex flex-col hidden lg:flex",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsx)("h2",{className:"text-2xl font-bold text-[#343C6A]",children:"AeTrust"})}),(0,t.jsx)("nav",{className:"flex-1 p-4",children:(0,t.jsx)("ul",{className:"space-y-2",children:g.map(e=>{let l=e.icon,r=s===e.id;return(0,t.jsx)("li",{children:(0,t.jsxs)(i.$,{variant:"ghost",className:(0,n.cn)("w-full justify-start h-12 px-4 text-left font-medium transition-all",r?"bg-[#1814F3] text-white hover:bg-[#1814F3]/90":"text-[#718EBF] hover:bg-gray-50 hover:text-[#343C6A]"),onClick:()=>null==a?void 0:a(e.id),children:[(0,t.jsx)(l,{className:"mr-3 h-5 w-5"}),e.label,r&&(0,t.jsx)("div",{className:"absolute right-0 top-0 bottom-0 w-1 bg-[#1814F3] rounded-l-lg"})]})},e.id)})})}),(0,t.jsxs)("div",{className:"p-4 border-t border-gray-200",children:[(0,t.jsxs)(i.$,{variant:"ghost",className:"w-full justify-start h-12 px-4 text-[#718EBF] hover:bg-gray-50 hover:text-[#343C6A]",children:[(0,t.jsx)(f,{className:"mr-3 h-5 w-5"}),"Help & Support"]}),(0,t.jsxs)(i.$,{variant:"ghost",className:"w-full justify-start h-12 px-4 text-[#718EBF] hover:bg-gray-50 hover:text-red-600",children:[(0,t.jsx)(p,{className:"mr-3 h-5 w-5"}),"Logout"]})]})]})}var j=a(86948);let v=(0,c.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);var N=a(57828);let y=(0,c.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),w=(0,c.A)("badge",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}]]);function A(e){let{user:s}=e;return(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 rounded-[25px]"}),(0,t.jsx)("div",{className:"absolute top-0 right-0 w-32 h-32 bg-blue-500/20 rounded-full -translate-y-8 translate-x-8"}),(0,t.jsx)("div",{className:"absolute bottom-0 left-0 w-24 h-24 bg-blue-400/20 rounded-full translate-y-4 -translate-x-4"}),(0,t.jsx)("div",{className:"absolute top-1/2 right-8 w-16 h-16 bg-yellow-400/30 rounded-full"}),(0,t.jsxs)(j.Zp,{className:"relative bg-transparent border-none shadow-none text-white p-8",children:[(0,t.jsxs)("div",{className:"flex flex-col space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold",children:s.name}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"text-sm opacity-80",children:["*** **** * ",s.cardNumber.slice(-3)]}),(0,t.jsx)(i.$,{variant:"ghost",size:"icon",className:"h-6 w-6 text-white hover:bg-white/20",children:(0,t.jsx)(v,{className:"h-3 w-3"})})]})]}),(0,t.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,t.jsx)("span",{className:"text-sm opacity-80",children:"Your balance"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl font-bold",children:(0,n.vv)(s.balance)}),(0,t.jsx)(i.$,{variant:"ghost",size:"icon",className:"h-6 w-6 text-white hover:bg-white/20",children:(0,t.jsx)(N.A,{className:"h-4 w-4"})})]})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)(i.$,{variant:"ghost",size:"icon",className:"h-10 w-10 bg-white/20 hover:bg-white/30 rounded-full",children:(0,t.jsx)(y,{className:"h-5 w-5"})})})]}),(0,t.jsxs)("div",{className:"absolute bottom-6 right-6 flex items-center space-x-2 bg-white/20 rounded-full px-3 py-1",children:[(0,t.jsx)(w,{className:"h-4 w-4 text-yellow-400"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:s.tier})]})]})]})}var F=a(37846);function k(e){let{data:s}=e,a=s.map(e=>({name:e.label,value:e.value,color:e.color}));return(0,t.jsxs)(j.Zp,{className:"bg-white rounded-[25px] border-none shadow-lg",children:[(0,t.jsx)(j.aR,{children:(0,t.jsx)(j.ZB,{className:"text-[#343C6A] text-xl font-semibold",children:"Statistics"})}),(0,t.jsx)(j.Wu,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(F.k,{data:a,height:200,showLegend:!1})}),(0,t.jsx)("div",{className:"flex flex-col space-y-4 ml-8",children:s.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color}}),(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsxs)("span",{className:"text-sm font-medium text-white",children:[e.percentage,"%"]}),(0,t.jsx)("span",{className:"text-xs text-white/80",children:e.label})]})]},s))})]})})]})}var C=a(17215);let B=(0,c.A)("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]),M=(0,c.A)("arrow-right-left",[["path",{d:"m16 3 4 4-4 4",key:"1x1c3m"}],["path",{d:"M20 7H4",key:"zbl0bi"}],["path",{d:"m8 21-4-4 4-4",key:"h9nckh"}],["path",{d:"M4 17h16",key:"g4d7ey"}]]);function _(e){let{transactions:s}=e;return(0,t.jsxs)(j.Zp,{className:"bg-white rounded-[25px] border-none shadow-lg",children:[(0,t.jsx)(j.aR,{children:(0,t.jsx)(j.ZB,{className:"text-[#343C6A] text-xl font-semibold",children:"Recent Transaction"})}),(0,t.jsx)(j.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:s.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 hover:bg-gray-50 rounded-lg transition-colors",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"p-3 rounded-full ".concat(function(e){switch(e){case"cash_in":return"bg-green-100";case"cash_out":return"bg-red-100";case"transfer":return"bg-blue-100";case"loan":return"bg-orange-100";default:return"bg-gray-100"}}(e.type)),children:function(e){switch(e){case"cash_in":return(0,t.jsx)(C.A,{className:"h-5 w-5 text-green-500"});case"cash_out":return(0,t.jsx)(B,{className:"h-5 w-5 text-red-500"});case"transfer":return(0,t.jsx)(M,{className:"h-5 w-5 text-blue-500"});case"loan":return(0,t.jsx)(C.A,{className:"h-5 w-5 text-orange-500"});default:return(0,t.jsx)(M,{className:"h-5 w-5 text-gray-500"})}}(e.type)}),(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{className:"font-medium text-[#232323]",children:e.description}),(0,t.jsx)("span",{className:"text-sm text-[#718EBF]",children:(0,n.sL)(e.date)})]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsxs)("span",{className:"font-semibold ".concat(function(e){switch(e){case"cash_in":case"transfer":return"text-green-600";case"cash_out":case"loan":return"text-red-600";default:return"text-gray-600"}}(e.type)),children:["cash_out"===e.type||"loan"===e.type?"-":"+",(0,n.vv)(e.amount)]})})]},e.id))})})]})}function R(){return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(j.Zp,{className:"bg-[#1814F3] rounded-[25px] border-none shadow-lg text-white",children:(0,t.jsx)(j.Wu,{className:"p-6",children:(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"p-3 bg-white/20 rounded-full",children:(0,t.jsx)(B,{className:"h-6 w-6"})}),(0,t.jsx)("span",{className:"text-lg font-medium",children:"Transfer"})]})})})}),(0,t.jsx)(j.Zp,{className:"bg-white rounded-[25px] border border-gray-200 shadow-lg",children:(0,t.jsx)(j.Wu,{className:"p-6",children:(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"p-3 bg-gray-100 rounded-full",children:(0,t.jsx)(C.A,{className:"h-6 w-6 text-[#718EBF]"})}),(0,t.jsx)("span",{className:"text-lg font-medium text-[#718EBF]",children:"Withdraw"})]})})})}),(0,t.jsx)(j.Zp,{className:"bg-white rounded-[25px] border border-gray-200 shadow-lg",children:(0,t.jsx)(j.Wu,{className:"p-6",children:(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"p-3 bg-gray-100 rounded-full",children:(0,t.jsx)(C.A,{className:"h-6 w-6 text-[#718EBF]"})}),(0,t.jsx)("span",{className:"text-lg font-medium text-[#718EBF]",children:"Deposit"})]})})})})]})}let z={id:"1",name:"James Bond",email:"<EMAIL>",balance:35673,cardNumber:"3778123456781234",cardHolder:"James Bond",validThru:"12/22",tier:"Tier 1"},E=[{id:"1",type:"cash_in",amount:850,description:"Cash in (Agent yusuff)",date:"2021-01-28",status:"completed",agent:"Agent yusuff"},{id:"2",type:"cash_out",amount:2500,description:"Cash out",date:"2021-01-25",status:"completed"},{id:"3",type:"transfer",amount:5400,description:"Transfer",date:"2021-01-21",status:"completed"}],Z=[{label:"Cash in",value:30,percentage:30,color:"#1814F3"},{label:"Loan",value:20,percentage:20,color:"#16DBCC"},{label:"Cash out",value:15,percentage:15,color:"#FF82AC"},{label:"Others",value:35,percentage:35,color:"#FFBB38"}];function D(){let[e,s]=(0,l.useState)("dashboard");return(0,t.jsxs)("div",{className:"flex h-screen bg-[#F5F7FA]",children:[(0,t.jsx)(b,{activeItem:e,onItemClick:s}),(0,t.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,t.jsx)(r.Y,{title:"Overview",userName:z.name}),(0,t.jsx)("main",{className:"flex-1 overflow-auto p-4 md:p-8",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-12 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-8 space-y-6",children:[(0,t.jsx)(A,{user:z}),(0,t.jsx)(_,{transactions:E})]}),(0,t.jsxs)("div",{className:"lg:col-span-4 space-y-6",children:[(0,t.jsx)(R,{}),(0,t.jsx)(k,{data:Z})]})]})})]})]})}},37846:(e,s,a)=>{"use strict";a.d(s,{k:()=>o});var t=a(95155),l=a(26991),r=a(12723),n=a(4035),i=a(69386),c=a(11345);let d=["#1814F3","#16DBCC","#FF82AC","#FFBB38"];function o(e){let{data:s,width:a=400,height:o=300,showLegend:h=!0}=e;return(0,t.jsx)(l.u,{width:"100%",height:o,children:(0,t.jsxs)(r.r,{children:[(0,t.jsx)(n.F,{data:s,cx:"50%",cy:"50%",innerRadius:60,outerRadius:100,paddingAngle:5,dataKey:"value",children:s.map((e,s)=>(0,t.jsx)(i.f,{fill:e.color||d[s%d.length]},"cell-".concat(s)))}),h&&(0,t.jsx)(c.s,{verticalAlign:"bottom",height:36,formatter:(e,s)=>(0,t.jsx)("span",{style:{color:s.color},children:e})})]})})}},44080:(e,s,a)=>{Promise.resolve().then(a.bind(a,20672))},47845:(e,s,a)=>{"use strict";a.d(s,{Y:()=>o});var t=a(95155),l=a(65142),r=a(3998),n=a(86651),i=a(85998),c=a(15870),d=a(15239);function o(e){let{title:s,userAvatar:a,userName:o}=e;return(0,t.jsx)("header",{className:"bg-white border-b border-gray-200 px-4 md:px-8 py-4 md:py-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h1",{className:"text-xl md:text-2xl font-semibold text-[#343C6A]",children:s}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-6",children:[(0,t.jsxs)("div",{className:"relative hidden md:block",children:[(0,t.jsx)(n.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(l.p,{placeholder:"Search for something",className:"pl-10 w-60 lg:w-80 bg-[#F5F7FA] border-none"})]}),(0,t.jsxs)(r.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,t.jsx)(i.A,{className:"h-5 w-5 text-[#718EBF]"}),(0,t.jsx)("span",{className:"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"})]}),(0,t.jsx)(r.$,{variant:"ghost",size:"icon",children:(0,t.jsx)(c.A,{className:"h-5 w-5 text-[#718EBF]"})}),(0,t.jsx)("div",{className:"flex items-center space-x-3",children:(0,t.jsx)("div",{className:"w-10 h-10 rounded-full overflow-hidden bg-gray-200",children:a?(0,t.jsx)(d.default,{src:a,alt:o||"User",width:40,height:40,className:"w-full h-full object-cover"}):(0,t.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center text-white font-semibold",children:(null==o?void 0:o.charAt(0))||"U"})})})]})]})})}},64269:(e,s,a)=>{"use strict";a.d(s,{Yq:()=>i,cn:()=>r,sL:()=>c,vv:()=>n});var t=a(2821),l=a(75889);function r(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,l.QP)((0,t.$)(s))}function n(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e)}function i(e){return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric"}).format(new Date(e))}function c(e){return new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric"}).format(new Date(e))}},65142:(e,s,a)=>{"use strict";a.d(s,{p:()=>n});var t=a(95155),l=a(12115),r=a(64269);let n=l.forwardRef((e,s)=>{let{className:a,type:l,...n}=e;return(0,t.jsx)("input",{type:l,className:(0,r.cn)("flex h-9 w-full rounded-md border border-gray-200 bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-[#1814F3] disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...n})});n.displayName="Input"},86948:(e,s,a)=>{"use strict";a.d(s,{Wu:()=>d,ZB:()=>c,Zp:()=>n,aR:()=>i});var t=a(95155),l=a(12115),r=a(64269);let n=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:(0,r.cn)("rounded-xl border bg-white text-gray-950 shadow",a),...l})});n.displayName="Card";let i=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",a),...l})});i.displayName="CardHeader";let c=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("h3",{ref:s,className:(0,r.cn)("font-semibold leading-none tracking-tight",a),...l})});c.displayName="CardTitle",l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("p",{ref:s,className:(0,r.cn)("text-sm text-gray-500",a),...l})}).displayName="CardDescription";let d=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:(0,r.cn)("p-6 pt-0",a),...l})});d.displayName="CardContent",l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:(0,r.cn)("flex items-center p-6 pt-0",a),...l})}).displayName="CardFooter"}},e=>{e.O(0,[618,441,255,358],()=>e(e.s=44080)),_N_E=e.O()}]);